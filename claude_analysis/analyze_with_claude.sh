#!/bin/bash
# Claude Analysis Pipeline
# Usage: ./analyze_with_claude.sh

echo "🧠 Sending fantasy analysis to <PERSON>..."

# Option 1: Using Claude CLI (if you have it installed)
# claude --model=sonnet < claude_prompt.md > claude_response.md

# Option 2: Copy prompt to clipboard (macOS)
cat claude_prompt.md | pbcopy
echo "📋 Prompt copied to clipboard - paste into Claude interface"
echo "💡 Or pipe through your preferred Claude integration"

# Option 3: Display prompt for manual copy
echo ""
echo "=== CLAUDE PROMPT ==="
cat claude_prompt.md
echo ""
echo "=== CLEAN PROMPT ==="
cat clean_prompt.md
echo ""
echo "=== END PROMPT ==="
