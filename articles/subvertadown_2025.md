Title: How to Get Most Out of the Subvertadown Website 2025

URL Source: https://subvertadown.com/article/how-to-get-most-out-of-the-subvertadown-website-2025

Markdown Content:
![Image 1: <PERSON>](https://subvertadown.com/images/hurts.jpg)HILADELPHIA, PA - SEPTEMBER 04: Philadelphia Eagles quarterback <PERSON><PERSON><PERSON> (1) scores a touchdown during the game between the Dallas Cowboys and the Philadelphia Eagles on September 4th, 2025 at Lincoln Financial Field in Philadelphia, PA. (Photo by <PERSON>/Icon Sportswire)

**Hello everyone, and welcome to a new season at the Subvertadown website!**

To enjoy as best possible, here’s an overview of how you’re intended to use the offerings here.

### History

When I launched Subvertadown.com a few years ago, the set-up was fairly simple. There were a bunch of tables, showing projection numbers to help you rank your choices.

But this website has been under constant development, and now it’s filled with nuggets and nuances. That means, if you’re new and missed the explanation for each new release, you might feel lost.

So here’s how I envision you’ll use the site and make your selection each week.

### Step 1: Looking at Projections Instead of Rankings

You’ll pick up on a theme in this article: A lot of you should get away from just grabbing players “at the top of the list”.

If my work was only giving “rankings”, that would be justified. But the first distinction to make is that the numerical model outputs— the projections— are more useful than rankings. They’re better at telling you how close players are, which gives you more freedom in your choices.

The problem with plain rankings is that players often have very similar outlooks. Maybe a player is “closer to rank #2 -and-a-half” instead of being ranked #3. And when a few players are closely ranked, it should rather start looking like a “Tier” system. You should start thinking of projection lists like a “more precise version of Tiered lists”.

![Image 2](https://imgur.com/E151Zmd.jpeg)

What does this mean in practice? When 2 players are ranked within, let’s say <0.5 points from each other, **you don’t need to take the ordering so literally**. You should feel some liberty in applying your own discretion to the options. So you might supplement your choice with knowledge you get from elsewhere, or you might use some of the other tools described below to “break the tie”.

### Step 2: Looking Ahead at Future Week Forecasts

Streaming D/STs, Kickers, and even QBs is usually meant for owning these players for a single week and then dropping them.

One possible problem with that is: next week you might lose a battle on waivers, to claim the next streamer you want. And an extra layer of complication is: you might be fighting on waivers for other positional players, like a break-out WR, or an RB handcuff etc.

Might you be at a disadvantage, if you don’t have a playable D/ST already? Are your league mates active on their waiver wires? Do they actively stream?

![Image 3](https://imgur.com/B4B3MOE.jpeg)

**Looking ahead by at least one week could alleviate some of this headache.**

*   One option for D/ST (or maybe QB)— which might be relevant for some people with larger benches or less competitive waivers— would be to grab 2 streamers and stash one on your bench. That, however, takes bench space. This is a strategy that becomes more useful heading into playoffs, though.

*   The other option— for all positions D/ST, Kicker, and QB— might be to claim a player who appears to have a good matchup for 2 weeks in a row. You might decide to sacrifice the current week just a little bit. For example, you might accept a D/ST projected to score 7.8 instead of 8.5, because you can see that the following week might be risky for you. The point is, build the look-ahead into your selection, and don’t necessarily just grab “the top player”.

### Step 3: Looking Ahead Even More

If you happen to have the Long Range view, you should also keep an eye on players you might even consider holding. Holding through a bye, or holding through 1 week of bad matchup. The players who appear best to hold will always fluctuate through the season, so you need to keep up.

![Image 4](https://imgur.com/GIu8A5W.jpeg)

### Step 4: The Pros and Cons (“Why So High / Why So Low”)

If you hover over the little “up/down” arrows, you’ll see a pop-up list of reasons why the player is ranked high or low.

This kind of works like running the model in reverse. We start with the fantasy score projection, and then we ask the models to break it down and explain the key reasons— relative to other players— that explain the ranking for this player.

These are not meant to be perfect explanations, but they’re mean to contextualize the choice, to help you think. **Does the explanation make sense to you? Does it sway you towards one player over another?** Your thought process might sway you to end up with a different player than you expected.

![Image 5](https://imgur.com/ifZfkQx.jpeg)

You might even have reasons (or intuition) that the model’s suggestion is off. In which case you can “disregard the pro” / “disregard the con”, based on your own feeling for why you think the model might not be completely right. Either way, this can help inform your choice, when deciding on a players within 1 fantasy point difference or so.

### Step 5: The Explanatory Charts — “How They Make Points”

For Kickers and D/ST, there are currently 2 very useful (and very cool) charts that both explain HOW the fantasy player is most likely to build up his score.

*   **For D/STs:** New this year, everyone can view a simple bar chart that explains “what type of metrics” give that D/ST an advantage. I hope everyone knows by now, this is NOT showing you mere “averages” of past sacks, interceptions, fumble recoveries, points-allowed, or TDs. Each of these parameters is modeled— by its own predictive model— using a bunch of various inputs to get the best estimate. Usefully, the chart tells you the “bonus” fantasy point contributions from each factor, relative to the league average.

![Image 6](https://imgur.com/UBwklJP.jpeg)

*   **For Kickers:**New last year, everyone can view the “Kicker Utilization Curves”. Each graph represents the team’s likelihood (relative to league average) of STOPPING a drive at each labeled yard-line. Naturally, not every drive ends with a successful kick. They can end in: turn-overs, punts, failed 4th downs, or even missed kicks. Still, the usefulness of the tool is helping us question whether the outlook of team field advancement is aligned with our expectations.

![Image 7](https://imgur.com/KLXLyYm.jpeg)

### Step 6: Historical Matchup Comparisons

This is a special option for D/ST, and in my opinion it must be one of the overlooked features on the site.

I began developing this by asking “Could D/ST accuracy be even higher, if we turned away from regression modeling, and instead based our decisions on comparable games from the past?”

The tool works by first identifying “most similar games”. It picks 50-100 of them, out of thousands spanning a decade. Then it weights the influence of each game, with a weighting function I’ve optimized. When I check the accuracy of this approach, historically, it’s almost getting as good as my normal D/ST model.

Therefore one use of this method is to "get a second opinion”, that contrasts the plain model projection.

However the other use is to identify the boom-bust nature of this pick. If this “kind of game” historically resulted in more random outcomes— instead of being centered more nicely in the middle of the curve— then it could be to your strategic advantage to select (or deselect) that kind of pick.

Most importantly, however, I want everyone to observe the wide "Range of Outcomes” from all of these graphs, every single week. The possible fantasy score of the D/ST you choose could be 8 points lower or 8 points higher, any time. Integrate that into your thinking, to help you feel more freedom making the D/ST selection that you want.

![Image 8](https://imgur.com/opBBtJF.jpeg)

### Step 7: Review the Details and the Risk Level

The “Details” charts, available for all positions, shows additional factors to feed into your decision making.

1.   Observe the “Error” column. This is mathematically simulated “error level”, which means it’s a higher number if the model thinks it’s more of a “boom or bust” option. A low error indicates a less volatile option.

2.   Check the trendline for D/STs. A small line graph shows how the fantasy score projection has moved during the week. Use this as a reference to know when there’s been a trend— or to know when an update has been made (to account for injury etc.).

### Step 8: Create Comments and Check Existing Comments

Another D/ST feature, users can add a short comment explaining their insights, opinions, or feelings about a pick. You can upvote these, and the highest visibility options appear in a “comment bubble” tool tip.

### Step 9: Make Sure You’ve Chosen the Right Scoring Setting

My website uniquely offers D/ST rankings tailored for the ESPN scoring setting. The order of players is often different from the standard that others in the industry use. The ESPN setting penalizes D/STs for allowing yards to the opposing offense. Make sure to choose the “ESPN” setting instead of the “Yahoo” setting, if this applies to you.

Coming soon this year, my website also uniquely offers dedicated rankings for Decimal Kicker Scoring. Increasing number of leagues are rewarding their kickers 0.1 points for each yard of distance that the ball is successfully kicked. The ordering of players can be very different between the two settings.

### Step 10: Check the Discussion on the Reddit Forum

The Reddit fantasyfootball subreddit is where I post for most of my discussions with the community. Often I will make a weekly post about D/STs and/or Kickers, and you should definitely check out what people have to say!

*   [Most recent D/ST post](https://www.reddit.com/r/fantasyfootball/comments/1n6i05y/defensive_maneuvers_2025_welcome_to_week_1/)

*   [Most recent Kicker post](https://www.reddit.com/r/fantasyfootball/comments/1n88bly/comment/ncfhykg/?context=1)

### Other Nice-to-Haves

There’s more to the website that doesn’t all fit in a check list. Here are examples:

*   **Turn on “Dark Mode”**. At the bottom right of the screen, there is an icon that will flip the display from WHITE background to BLACK background. That might be easier on the eyes at night.

*   **Use the Injury Inquiry Tool**. If you’re in doubt that I have accounted for a specific player’s injury, in my models, then check the list of players that other users have asked about. You’ll see a response next to each player’s name telling you whether it’s included or not. If you don’t see the player you’re really concerned about, you can submit your own request.

*   [Don’t miss the Strength-of-Schedule charts for RB / WR / TE.](https://www.reddit.com/r/fantasyfootball/comments/1n1g78u/the_sos_study_reporting_real_accuracy_for/)

Thanks for reading, and **I really hope everyone feels an elevated level of engagement** with your Fantasy Streaming picks, using all these features!

**/Subvertadown**

