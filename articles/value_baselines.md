Title: Guide to understanding the different baselines in Value Based Drafting (VBD): VOLS vs. VORP vs. Man-games (and BEER +)

URL Source: https://subvertadown.com/article/guide-to-understanding-the-different-baselines-in-value-based-drafting-vbd-vols-vs-vorp-vs-man-games-and-beer-

Markdown Content:
This is a guide to help you select the right “Valuation” type, when you use a fantasy football [drafting tool like TapThatDraft](https://subvertadown.com/tap-that-draft).

The terminology of Value-Based Drafting can be difficult to follow. In this short article, I will lay out the key differences between VOLS, VORP, and Man-games options. And I will offer advice about what option you should select.

You can read further about how the valuation is used for [auctions here](https://subvertadown.com/article/how-auction-pricing-and-positional-scarcity-work----value-based-drafting-in-fantasy-football-auction-drafts) and how my modified version is used for [snake drafts here](https://subvertadown.com/article/fantasy-snake-drafts-and-strategizing-for-scarcity----snake-value-based-drafting).

**TL;DR**
---------

_If you don’t want to bother with different baseline options in the draft tool, I recommend the default “BEER+” option. It’s calculated with detailed thought to give a compromise between getting strong starters vs. de-risking your lineup with a strong bench. The standard “BEER” option, familiar to many, is also useful but might not emphasize your starters enough for you to remain competitive. The other options (VORP and VOLS) have different risk/reward tradeoffs, where VOLS prioritizes starters_ only _and VORP prioritizes a strong_ bench _._

![Image 1](https://imgur.com/pvN8vME.jpeg)

**Value Based Drafting (VBD) - In General**
-------------------------------------------

Fantasy football drafting terminology has evolved. The acronym “VBD” (value-based drafting) has become a generic term for various approaches to prioritizing players in your draft.

The underlying premise of all approaches is this: The “Value” of drafting a fantasy player, at a certain skill position, should be determined by **comparing the player against the “baseline” value of a “replacement”** player, meaning a player that you can reasonably consider as your substitute. This basic idea goes back at least a couple decades: instead of prioritizing the players who are expected to score highest, you instead need to subtract off some “reference” value.

Probably you’ve all been exposed to this concept at your first draft. You might have asked, “why shouldn’t I just always draft a quarterback first, since they usually score the most points?” The answer, as most of you know, is that normally a QB should not be your first draft pick (depending on league settings) because, in any given week, you can probably pick up a backup QB who is likely to score around 15 points or so. So you should be valuing your QB according to “points _above_ the backup”, rather than by points alone.

The following figure shows how a lower-ranked QB can still provide points, and therefore the value of higher-ranked QBs should be taken as relative to this high baseline. [QB data is based on Subvertadown predictive models, averaged over all simulated weeks.]

![Image 2](https://imgur.com/iFE7Dti.jpeg)

With this basic idea in mind, the question is **which baseline should you use?** Luckily for us, over many years, fantasy football nerds (a.k.a. awesome guys) have devised multiple types of baselines. We use the general term “Value Over Baseline” or “VOB” to refer to all versions. (The terminology “value over replacement” is often used elsewhere, but as described below that has become even more confusing.)

The following 3 options are the most common baselines that have emerged. Each type of baseline serves a different purpose.

**Value Over Last Starter (VOLS)**
----------------------------------

Let’s start with the easy one: Value over last starter. Your "starters” are the guys who fill your active roster. So “Value over Last Starter” effectively means that you define your value baseline right at the cutoff between active players and your fantasy bench. It could as well be “value over best bench player”.

If you choose to use VOLS, you’re deciding that your bench players are less valuable, because you’re prioritizing starter-tier players.

*   In an auction draft, VOLS inflates the recommended prices for top players, while leaving a tiny budget for your bench.

*   In a snake draft, VOLS prioritizes earlier picks for positions that are more replaceable.

It’s easy to think that VOLS can help you lead with a killer active roster. However, it comes with a an increased risk of exposure: usually not all of your starters will pan out. You will certainly need a strong substitute at some point during your season. The same is true for your league-mates. If you see them overvaluing players by using VOLS, chances are that they will be at risk of missing a strong back-up at some point.

There are a couple reasons you might consider using VOLS:

*   if you have a smaller league and so the waiver wire is large, or

*   if your league is not that competitive on waivers, and you consider yourself more active with waivers.

Essentially you’re taking a bet that you’ll have a good chance of picking up strong substitutes when you need them.

![Image 3](https://imgur.com/eHyQ35S.jpeg)

**Value Over Replacement Player (VORP)**
----------------------------------------

I can’t be the only person who finds this term confusing. I mean, don’t all these baselines consider “replacements”?

Anyway, “Value Over Replacement Player” has come to mean that you set your baseline equal to the best un-rostered player in the league. In other words: the best replacement on waivers. **It would be much clearer if we called it “Value over Waivers”!**

In contrast to focusing on starters (like VOLS does), “VORP” is more risk-averse, and it devotes more priority to your bench.

*   In an auction draft, VORP reduces the recommended prices for top players, therefore leaving a larger budget for your bench.

*   In a snake draft, VORP prioritizes earlier picks for valuable bench back-ups, sometimes before filling your starting roster positions.

VORP is designed to address your full bench without giving priority to your starting roster. This is surely more useful in fantasy baseball, where there’s more opportunity to use your bench and injuries are fewer. In fantasy football, it’s generally less useful to set your baseline at the level of waivers. The reason you might consider using VORP:

*   if your league uses a format like Best Ball, making your bench significantly more valuable, or

*   if your league culture is very competitive for waiver pickups.

In the Subvertadown draft tool TapThatDraft, we have removed VORP as an option. We are open to requests, but the better winning strategy is usually to put more emphasis on your starters. Some people haven’t realized that VORP down-prioritizes starters, perhaps because the term “replacement player” can be ambiguous and is sometimes used interchangeably with other VBD methods.

![Image 4](https://imgur.com/R5QXy5f.jpeg)

**Man-Games**, which our website refers to as **“Best Ever Evaluation of Replacement” (BEER)**
----------------------------------------------------------------------------------------------

Then there is the “man-games” approach, which you might consider a happy medium between VOLS and VORP. The idea was originally published in 2012 by Frank Dupont, who was contemplating the **supply/demand** of fantasy positions in the fantasy football draft. He brought a more detailed consideration to what defines a “Replacement Player”. The approach might have been most popularized during the 2010s by the cheat sheets branded as BeerSheets— so, as an homage, I’m assigning the moniker “BEER” to this VOB method.

The basic idea is to estimate **how many players the active rosters will need all season**. Obviously, that number has to include more players than just the active starters in week 1, because there will be bye weeks, trades, injuries, and other unforeseen events.

In more detail, the idea is to use historical data to approximate how many games each drafted player is likely to play.

*   For example, a typical top QB ends up playing 15 games on average (out of 17).

*   And a typical top RB will maybe end up playing 13 games,

*   But an RB ranked 40-50 will maybe only play 11 games.

Let’s keep it simple: **These types of reductions in _games-played_ mean that some of your league’s “bench” players will become valuable active starters**, at some point during the season. And the theory is that we should set our baselines by counting up player availability, until we include enough players to meet the #games demanded for an entire season. This is done for each position.

*   This BEER method gives a more balanced treatment to prioritizing your roster’s starters and bench players.

*   You might prefer VOLS if you think your league’s waivers can be as fruitful for you as your own bench.

*   You might prefer VORP if bench players make a bigger difference in your league, as described above.

Despite the improvements of BEER, it might still not give enough weight to starters. Some enthusiasts have considered the BEER auction prices to be too low for top-ranked players. Therefore, we have introduced BEER+ variation, described in the following section, which adds further reasoning to yield higher, fairer auction prices to starters.

![Image 5](https://imgur.com/CZ0FvVO.jpeg)

* * *

**BEER+ “Best Ever Evaluation of Replacement**PLUS**” — Proportional Emphasis on Starters**
-------------------------------------------------------------------------------------------

Finally, there is the BEER+ metric, introduced by Subvertadown as a further improvement to draft valuation.

_But wait, why should anything be better than plain BEER?_

The fantasy football community has been discussing shortfalls of VBD for years. BEER+ contains several improvements to improve positional valuation during the fantasy draft:

1.   Improved baselines logic, utilizing 2 relevant baselines,

2.   Risk-adjusted valuation, accounting for historical variance in points scored, by position, and

3.   Adjusting QB valuation, to account for the possibility of QB streaming.

1. Multiple baselines.
----------------------

The BEER metric, as described above, gives equal weight to (1) your starters and (2) your “needed bench players”.

However, users have observed that the original BEER method can tend to yield auction prices that seem unfairly low to starters, especially top-tier starters. Part of the reason for this is that BEER gives weight to starters and relevant benchers with the same proportion to value-over-baseline. That’s a deliberate assumption of the theory. However, there’s good logic to prioritizing your starters with greater weight than the bench, even for those “most-needed” bench players that get determined by the man-games assessment. Starters are the ones you’ll depend upon to produce for you, after the draft. In contrast, you might not need to tap into your bench for a few weeks. And while you’re waiting to dip into that bench potential, the value of those drafted bench players may decline, as alternative hot players emerge on the waiver wire during those first weeks.

Therefore, failing to invest enough in your starting roster can leave you behind. For context: if you choose BEER instead of VOLS, the league’s top player value can change by as much as 10% of your auction budget. Choosing the wrong baseline could cause you to pass up on reasonable value.

The BEER+ metric starts with BEER and then **redistributes some bench value to up to the starters**.

*   In other words, this is a mixing of both VOLS and BEER. It applies the logic of 2 baselines instead of just 1 baseline.

*   This has the effect of giving proportionally more value to the very top-ranked starters. BEER+ will generally increase their suggested auction prices.

*   The degree of redistribution is calculated by: (number of relevant players from VOLS) / (number of relevant players from BEER + number of relevant players from VOLS). As an example, this ratio will often be approximately 40% VOLS (and 60% BEER), but depending on roster composition.

2. Risk-adjusted valuation
--------------------------

I have further developed BEER+ to calculate the **risk-adjusted value** of players. This warrants a much longer summary, but very simply: I have analyzed historical data of draft rankings and found that some positions tend to deliver on their projections with higher **certainty**. Other certain positions end up with more random season outcomes, relative to their draft rankings. (While I wait to publish my own results, [here is FFA’s recent study](https://fantasyfootballanalytics.net/2025/07/fantasy-football-projections-exploring-positional-bias-in-projections.html) of positional risk.) Therefore BEER+ calculates a risk-adjusted player valuation, by means of Sharpe ratio estimation (which is used to approximate the value of investments for their risk-adjusted returns).

3. Accounting for QB Streaming (now available for all baseline selections, too)
-------------------------------------------------------------------------------

A third recurring request has been for draft tools to more properly account for QB value, by recognizing the potential to increase points by streaming quarterbacks. Subvertadown is particularly well-suited to integrate QB streaming into advice for the fantasy draft, because I have long analyzed [QB streaming potential](https://subvertadown.com/article/streaming-qbs-can-be-a-viable-strategy-if-your-league-is-not-too-deep-) and I offer weekly [QB streaming projections for all teams](https://subvertadown.com/weekly/quarterback?sort=current_week_projection).

I have analyzed 11 years of historical weekly returns from QB streaming— and I have simulated it for each number of QBs owned by your league-mates. This assumes that your league-mates prefer to own all the top-averaging team-QBs. Carefully using my projection models, I know the outcome QB scoring outcomes that would have occurred. To implement this feature, I currently consider your league-size, and I assume that your league-mates each choose to own 2QBs.

Note: The QB-streaming baseline is now an option you can select for all baselines, instead of being applied by default. It’s not recommended to choose the QB streaming baseline if your league is a 2QB/Superflex league with 12 or more teams. It’s only valid if the total number of QBs owned by your league-mates is 25 or fewer.

Summary
-------

With these improvements, I hope and expect the BEER+ valuation will serve as an almost “worry-free” option, for most fantasy managers. We have confidently defined BEER+ as the default in our draft tool, [TapThatDraft](https://subvertadown.com/tap-that-draft).

* * *

**In short: Don’t get lost in the the acronyms for VBD, VOLS, VORP, and BEER (or VONA and others). You should probably use the BEER+ valuation in general, unless you find yourself in the situations outlined above.**

You can read further about how the valuation is used for [auctions here](https://subvertadown.com/article/how-auction-pricing-and-positional-scarcity-work----value-based-drafting-in-fantasy-football-auction-drafts) and how my modified version is used for [snake drafts here](https://subvertadown.com/article/fantasy-snake-drafts-and-strategizing-for-scarcity----snake-value-based-drafting).

Good luck with your draft!

/Subvertadown

