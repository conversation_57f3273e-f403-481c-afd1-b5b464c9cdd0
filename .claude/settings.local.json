{"permissions": {"allow": ["WebFetch(domain:github.com)", "<PERSON><PERSON>(uv run:*)", "Bash(git add:*)", "WebFetch(domain:winwithodds.com)", "Bash(git branch:*)", "Bash(grep:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(python:*)", "Bash(PYTHONPATH=src uv run python:*)", "Bash(./stream_import.sh:*)", "mcp__mcp-server-motherduck__query", "Bash(git ls-tree:*)", "Bash(git commit:*)", "<PERSON><PERSON>(chmod:*)", "Bash(git push:*)"], "deny": [], "ask": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["browsermcp"]}