#!/bin/bash
# stream_import.sh - Ultra-fast Subvertadown import

set -e

POSITION="$1"
WEEK="${2:-1}"

if [ -z "$POSITION" ]; then
    echo "Usage: ./stream_import.sh <dst|qb|k> [week]"
    echo ""
    echo "Workflow:"
    echo "1. Select Subvertadown table in browser"
    echo "2. Cmd+C to copy"
    echo "3. Run: ./stream_import.sh dst 1"
    echo "4. Get instant analysis!"
    exit 1
fi

echo "📋 Importing $POSITION data from clipboard (Week $WEEK)..."

# Run AI parser
uv run python import_streaming_ai.py "$POSITION" "$WEEK"

# Quick streaming analysis
echo ""
echo "🎯 Streaming Analysis:"

uv run python -c "
from src.enhanced_projection_storage import EnhancedProjectionStorage
from src.strategic_analytics import StrategicAnalytics

storage = EnhancedProjectionStorage()
analytics = StrategicAnalytics(storage)

# Get projections for this week
df = storage.get_projections(week=$WEEK, sources=['Subvertadown'])

if not df.empty:
    pos_df = df[df['position'] == '$POSITION'.upper()]
    if not pos_df.empty:
        print(f'📊 Total $POSITION projections: {len(pos_df)}')
        
        # Show top 5 streaming targets
        top_5 = pos_df.nlargest(5, 'projected_points')
        print(f'🔥 Top 5 streaming targets:')
        for _, row in top_5.iterrows():
            hold_flag = ''
            if 'hold' in str(row.get('raw_source_data', {})):
                hold_flag = ' [HOLD]' if 'true' in str(row['raw_source_data']).lower() else ''
            print(f'  {row[\"player_name\"]}: {row[\"projected_points\"]:.1f}{hold_flag}')
    else:
        print('No data found for this position')
else:
    print('No projections found')
"

echo ""
echo "✅ Import complete! Data ready for analysis."