# -*- coding: utf-8 -*-
"""fantasy-2025.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/19x9b4f2cxV7WOLuozVomDkaOVskf7FZ5
"""

wk = 1

!pip install espn_api

from espn_api.football import League

import pandas as pd
import numpy as np

espn_auth = 'AEAxwg1YSDePIGtrBVNf0%2B7VkoblDur7zYOTDbrqx3e0ZTpep0WEBKV%2Ftk2n8STitN%2BNz4RyH6N6z%2FaFvJzAC3uPje3voR%2FMG%2BeHf2q1ejQwZ%2BLQ2xbQW0Koz5ojk6LLvbv9clWbHc00E4d%2Fs7O6wqMHFmzOUVSeFqXWx%2FHDdZLbQvBriVuAz5lDb043onra6Z%2BB3dLxZ9nbKmuiRZbbBmtXXkrfzZgdaB%2B%2FUF1c7cXbpk7%2F9bLNOViGOzppbW8O5TgDf8uqyurPPS8nrxGujt55AZXmqiTX4BNP%2B0SGMmiJ%2Fbf3tqtaC4tKxkjTOxWOQ9I%3D'

fhope = League(league_id=1455043016, year=2025, swid='{F72C318E-8726-479E-AC31-8E8726579E0C}', espn_s2=espn_auth)

fhope.free_agents(size=5)

fhope.power_rankings(week=wk)

fhope.power_rankings()

fhope.recent_activity(size=50)

fhope.recent_activity()

fhope.teams

fas = fhope.free_agents(size=600)

fas_df = pd.DataFrame([o.__dict__ for o in fas])

fas_df[['name','playerId']]

from google.colab import auth
auth.authenticate_user()

import gspread
from google.auth import default
creds, _ = default()

gc = gspread.authorize(creds)

rustlers = fhope.teams[8]

rustlers

rustlers.schedule

rustlers.roster[0]

rustlers.roster[0].stats

rustlers_df = pd.DataFrame([o.__dict__ for o in rustlers.roster])

rustlers_df["projected_points"] = rustlers_df.apply(
         lambda row: row["stats"][wk]["projected_points"],
         axis=1)

# fas_df["projected_points"] = fas_df.apply(
#          lambda row: row["stats"][wk]["projected_avg_points"],
#          axis=1)

# prior_week = rustlers_df.copy(deep=False)
# prior_week["projected_points"] = prior_week.apply(
#          lambda row: row["stats"][wk-1]["projected_points"],
#          axis=1)
# prior_week["actual"] = prior_week.apply(
#          lambda row: row["stats"][wk-1]["points"],
#          axis=1)
# prior_week["delta"] = prior_week["actual"] - prior_week["projected_points"]
# prior_week[['name','projected_points','actual','delta']]

rustlers_df

rustlers_fa = pd.concat([fas_df, rustlers_df], ignore_index=True)

rustlers_fa[['name','projected_points']]

position_set = set(['QB','RB','WR','TE','K','D/ST'])

rustlers_fa['position'] = rustlers_fa.eligibleSlots.apply(lambda x: set(x).intersection(position_set).pop())

cleaned_fa = rustlers_fa #rustlers_fa[~(rustlers_fa.position.isin(['D/ST','K']))]
#cleaned_fa = cleaned_fa[cleaned_fa.posRank > 0]
cleaned_fa['slot_position'].fillna('Rustlers', inplace=True)
cleaned_fa = cleaned_fa[['name','proTeam','position', 'slot_position', 'pro_opponent','posRank', 'projected_points']]

cleaned_fa.position.unique()

clean_sorted_waiver = cleaned_fa.sort_values(by=['projected_points'], ascending=False)

espn_waiver = gc.open('fantasy 2024-01 rustlers').worksheet('espn-waiver')

espn_waiver.clear()

clean_sorted_waiver.fillna(0, inplace=True)
clean_sorted_waiver_list = clean_sorted_waiver.sort_values(by=['projected_points'], ascending=False).to_numpy().tolist()
headers = clean_sorted_waiver.columns.to_list()
waiver_write = [headers] + clean_sorted_waiver_list
espn_waiver.update(waiver_write)

#waiver_write

import requests
import pandas as pd

graphql_url = "https://fdresearch-api.fanduel.com/graphql"
graphql_payload = {
  "query": '''query GetProjections($input: ProjectionsInput!) {
  getProjections(input: $input) {
    ... on MlbPitcher {
      player {
        numberFireId
        name
        position
        playerPageUrl
        imageUrl
        handedness
      }
      team {
        numberFireId
        name
        imageUrl
        abbreviation
      }
      winsLosses
      salary
      value
      wins
      losses
      earnedRunsAvg
      gamesStarted
      saves
      inningsPitched
      hits
      runs
      earnedRuns
      homeRuns
      walks
      strikeouts
      walksPlusHitsPerInningsPitched
      gamesPlayed
      fantasy
      gameInfo {
        homeTeam {
          numberFireId
          name
          imageUrl
          abbreviation
        }
        awayTeam {
          numberFireId
          name
          imageUrl
          abbreviation
        }
        gameTime
      }
    }
    ... on MlbBatter {
      player {
        numberFireId
        name
        position
        playerPageUrl
        imageUrl
        handedness
      }
      team {
        numberFireId
        name
        imageUrl
        abbreviation
      }
      salary
      value
      plateAppearances
      runs
      hits
      singles
      doubles
      triples
      homeRuns
      runsBattedIn
      stolenBases
      caughtStealing
      walks
      strikeouts
      battingAverage
      onBasePercentage
      sluggingPercentage
      onBasePlusSlugging
      fantasy
      gameInfo {
        homeTeam {
          numberFireId
          name
          imageUrl
          abbreviation
        }
        awayTeam {
          numberFireId
          name
          imageUrl
          abbreviation
        }
        gameTime
      }
    }
    ... on NflSkill {
      player {
        numberFireId
        name
        position
        playerPageUrl
        imageUrl
        handedness
      }
      team {
        numberFireId
        name
        imageUrl
        abbreviation
      }
      gameInfo {
        homeTeam {
          numberFireId
          name
          imageUrl
          abbreviation
        }
        awayTeam {
          numberFireId
          name
          imageUrl
          abbreviation
        }
        gameTime
      }
      salary
      value
      completionsAttempts
      passingYards
      passingTouchdowns
      interceptionsThrown
      rushingAttempts
      rushingYards
      rushingTouchdowns
      receptions
      targets
      receivingYards
      receivingTouchdowns
      fantasy
      positionRank
      overallRank
      opponentDefensiveRank
    }
    ... on NflKicker {
      player {
        numberFireId
        name
        position
        playerPageUrl
        imageUrl
        handedness
      }
      team {
        numberFireId
        name
        imageUrl
        abbreviation
      }
      gameInfo {
        homeTeam {
          numberFireId
          name
          imageUrl
          abbreviation
        }
        awayTeam {
          numberFireId
          name
          imageUrl
          abbreviation
        }
        gameTime
      }
      salary
      value
      extraPointsAttempted
      extraPointsMade
      fieldGoalsAttempted
      fieldGoalsMade
      fieldGoalsMade0To19
      fieldGoalsMade20To29
      fieldGoalsMade30To39
      fieldGoalsMade40To49
      fieldGoalsMade50Plus
      fantasy
      positionRank
      opponentDefensiveRank
    }
    ... on NflDefenseSt {
      player {
        numberFireId
        name
        position
        playerPageUrl
        imageUrl
        handedness
      }
      team {
        numberFireId
        name
        imageUrl
        abbreviation
      }
      gameInfo {
        homeTeam {
          numberFireId
          name
          imageUrl
          abbreviation
        }
        awayTeam {
          numberFireId
          name
          imageUrl
          abbreviation
        }
        gameTime
      }
      salary
      value
      pointsAllowed
      yardsAllowed
      sacks
      interceptions
      fumblesRecovered
      touchdowns
      fantasy
      positionRank
      opponentOffensiveRank
    }
    ... on NflDefensePlayer {
      player {
        numberFireId
        name
        position
        playerPageUrl
        imageUrl
        handedness
      }
      team {
        numberFireId
        name
        imageUrl
        abbreviation
      }
      gameInfo {
        homeTeam {
          numberFireId
          name
          imageUrl
          abbreviation
        }
        awayTeam {
          numberFireId
          name
          imageUrl
          abbreviation
        }
        gameTime
      }
      tackles
      sacks
      interceptions
      touchdowns
      passesDefended
      fumblesRecovered
      opponentOffensiveRank
    }
    ... on NhlSkater {
      player {
        numberFireId
        name
        position
        playerPageUrl
        imageUrl
        handedness
      }
      team {
        numberFireId
        name
        imageUrl
        abbreviation
      }
      minutesPlayed
      salary
      value
      shots
      goals
      assists
      points
      powerPlayGoals
      powerPlayAssists
      plusMinus
      blockedShots
      penaltiesInMinutes
      fantasy
      timeOnIce
      avgTimeOnIce
      gamesPlayed
      gameInfo {
        homeTeam {
          numberFireId
          name
          imageUrl
          abbreviation
        }
        awayTeam {
          numberFireId
          name
          imageUrl
          abbreviation
        }
        gameTime
      }
    }
    ... on NhlGoalie {
      player {
        numberFireId
        name
        position
        playerPageUrl
        imageUrl
        handedness
      }
      team {
        numberFireId
        name
        imageUrl
        abbreviation
      }
      minutesPlayed
      salary
      value
      goalsAgainst
      shotsAgainst
      saves
      shutouts
      wins
      losses
      savePercent
      timeOnIce
      tiesPlusOvertimeOrShootoutLosses
      fantasy
      gamesPlayed
      goalsAgainstAvg
      gameInfo {
        homeTeam {
          numberFireId
          name
          imageUrl
          abbreviation
        }
        awayTeam {
          numberFireId
          name
          imageUrl
          abbreviation
        }
        gameTime
      }
    }
    ... on NbaPlayer {
      player {
        numberFireId
        name
        position
        playerPageUrl
        imageUrl
        handedness
      }
      team {
        numberFireId
        name
        imageUrl
        abbreviation
      }
      gameInfo {
        homeTeam {
          numberFireId
          name
          imageUrl
          abbreviation
        }
        awayTeam {
          numberFireId
          name
          imageUrl
          abbreviation
        }
        gameTime
      }
      salary
      value
      minutes
      fieldGoalsMade
      fieldGoalsAttempted
      threePointsMade
      threePointsAttempted
      freeThrowsMade
      freeThrowsAttempted
      assists
      steals
      blocks
      turnovers
      points
      rebounds
      gamesPlayed
      fieldGoalShootingPercentage
      threePointsShootingPercentage
      freeThrowShootingPercentage
      fantasy
      positionRank
      overallRank
    }
    ... on GolfPlayer {
      player {
        numberFireId
        name
        position
        playerPageUrl
        imageUrl
        handedness
      }
      fantasy
      salary
      value
      score
      madeCut
      first
      topFive
      topTen
      topTwentyFive
      eagles
      birdies
      pars
      bogeys
      doubleBogeys
    }
  }
}''',
  "variables": {
    "input": {
      "type": "PPR",
      "position": "NFL_SKILL",
      "sport": "NFL"
    }
  },
  "operationName": "GetProjections"
}

response = requests.post(graphql_url, json=graphql_payload)
data = response.json()

projections = data['data']['getProjections']

fanduel_projections = []
for p in projections:
    if p.get('player') and p.get('team') and p.get('gameInfo'):
        player_name = p['player']['name']
        fantasy_points = p['fantasy']
        team_abbr = p['team']['abbreviation']
        home_team_abbr = p['gameInfo']['homeTeam']['abbreviation']
        away_team_abbr = p['gameInfo']['awayTeam']['abbreviation']

        opponent = away_team_abbr if team_abbr == home_team_abbr else home_team_abbr

        fanduel_projections.append({
            'player': player_name,
            'proj': fantasy_points,
            'opp': opponent
        })

numberfire = pd.DataFrame(fanduel_projections)

numberfire_fa = pd.merge(numberfire, rustlers_fa, left_on="player", right_on="name")

cleaned_nf = numberfire_fa[['player','position','slot_position','opp','proj','projected_points','posRank']]
cleaned_nf['slot_position'].fillna('Rustlers', inplace=True)
cleaned_nf.columns=['player','pos','team','opp','nf_proj','espn_proj','rank']
cleaned_nf

nf_waiver = gc.open('fantasy 2024-01 rustlers').worksheet('nf-waiver')

nf_waiver.clear()

cleaned_nf.fillna(0, inplace=True)
cleaned_nf["nf_proj"] = cleaned_nf["nf_proj"].astype(float)
# fill gridiron waiver with values sorted by expected
nf_waiver_list = cleaned_nf.sort_values(by=['nf_proj'], ascending=False).to_numpy().tolist()
headers = cleaned_nf.columns.to_list()
waiver_write = [headers] + nf_waiver_list
nf_waiver.update(waiver_write)
