# XHR Request Analysis for ESPN Fantasy Football

This document outlines the process for analyzing XHR requests to extend the functionality of the espn-api library for team management actions.

## Current espn-api Limitations

The espn-api library is excellent for **reading** data:
- League information
- Team rosters  
- Player stats
- Matchup data
- Free agents list

However, it has limited capabilities for **writing/modifying** data:
- Setting lineups
- Adding/dropping players
- Managing waivers
- Making trades

## XHR Analysis Process

To identify the necessary API calls for team management actions, follow these steps:

### 1. Browser Developer Tools Setup
1. Open ESPN Fantasy Football in your browser
2. Open Developer Tools (F12)
3. Go to Network tab
4. Filter by XHR requests
5. Clear existing requests

### 2. Key Actions to Analyze

#### Setting Lineup
1. Navigate to your team's lineup page
2. Make a roster move (move player from bench to starter)
3. Look for XHR requests to endpoints like:
   - `PUT /leagues/{leagueId}/teams/{teamId}/roster`
   - `POST /leagues/{leagueId}/transactions`

#### Adding Players
1. Go to Players > Available Players
2. Add a player to your team
3. Look for requests to:
   - `POST /leagues/{leagueId}/transactions`
   - Transaction type: "ADD" or "CLAIM"

#### Dropping Players  
1. Drop a player from your roster
2. Look for similar transaction requests with type "DROP"

#### Waiver Claims
1. Navigate to waivers
2. Submit a waiver claim
3. Analyze the request structure

### 3. Request Structure Analysis

For each XHR request, document:
- **URL**: Full endpoint URL
- **Method**: GET, POST, PUT, DELETE
- **Headers**: Required authentication headers
- **Payload**: JSON structure of the request body
- **Response**: Expected response format

### 4. Authentication Requirements

ESPN Fantasy requires:
- `espn_s2` cookie for session authentication
- `SWID` cookie for user identification
- Proper headers including User-Agent

### 5. Implementation Strategy

Once XHR requests are analyzed:
1. Create helper methods in `FantasyManager` class
2. Use `requests` library to make authenticated calls
3. Handle error responses and retries
4. Add proper logging for debugging

## Common ESPN API Endpoints (Hypothetical)

Based on typical REST API patterns, look for these types of endpoints:

```
# Roster management
PUT /leagues/{leagueId}/teams/{teamId}/roster
POST /leagues/{leagueId}/transactions

# Player transactions  
POST /leagues/{leagueId}/transactions
- Type: "ADD", "DROP", "CLAIM"
- Player ID
- Team ID

# Waiver management
GET /leagues/{leagueId}/waivers
POST /leagues/{leagueId}/waivers
PUT /leagues/{leagueId}/waivers/{waiverID}

# Lineup changes
PUT /leagues/{leagueId}/teams/{teamId}/lineup
- Week number
- Player positions/slots
```

## Testing Strategy

1. Start with read-only operations to verify authentication
2. Test each write operation individually
3. Implement rollback/undo for reversible actions
4. Add extensive error handling
5. Test with different league settings (public vs private)

## Next Steps

1. **Manual XHR Analysis**: Use browser dev tools to capture actual requests
2. **Request Implementation**: Code the captured requests in Python
3. **Error Handling**: Add robust error handling and retries  
4. **Testing**: Thoroughly test each operation
5. **Documentation**: Document each new capability

## Risk Considerations

- **Rate Limiting**: ESPN may have rate limits on API calls
- **Account Security**: Improper requests could cause account issues
- **League Rules**: Some actions may violate league settings
- **Timing**: Roster locks and waiver periods must be respected