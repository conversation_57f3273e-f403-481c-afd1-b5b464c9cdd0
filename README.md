# Fantasy AI

A Python-based automation tool for managing ESPN fantasy football teams with intelligent waiver wire analysis and multi-source projection integration featuring an **enhanced DuckDB analytics backend with historical data tracking**.

This tool helps you automate tedious weekly tasks, analyze your team and the waiver wire, and make more informed decisions with professional-grade cheatsheets and comprehensive projection analysis from 5+ major fantasy sources with complete historical context.

## Features

### 🗃️ **Enhanced Data Storage & Analytics**
- **Historical Data Preservation**: All projections and SOS data preserved with timestamps for trend analysis
- **DuckDB Analytics Backend**: High-performance analytical queries with JSON data preservation  
- **Multi-Source Integration**: ESPN, FanDuel, DraftSharks, WinWithOdds, Subvertadown with complete data lineage
- **Automated Schema Migration**: Seamless database updates preserving existing data
- **Change Detection**: Track projection and matchup changes over time for strategic advantage

### 📊 **Professional Waiver Wire Analysis**
- **Interactive HTML Reports**: FantasyPros-style layout with sortable columns and historical context
- **Multi-Source Projections**: Side-by-side comparison of 500+ players across all sources
- **Historical Trend Analysis**: Track how player values change throughout the week
- **Smart Recommendations**: AI-powered ADD/DROP suggestions with confidence scoring
- **Position Coverage**: Complete QB, RB, WR, TE, K, D/ST analysis with matchup data

### 🤖 **Advanced AI Features**
- **AI-Enhanced Analysis**: Claude-level strategic insights with historical decision context
- **Post-Week Analysis**: Comprehensive projection accuracy tracking and performance evaluation
- **Strategic Recommendations**: ADD/DROP suggestions with confidence and urgency scoring
- **Historical Learning**: AI analysis of past decisions and outcomes for continuous improvement
- **Season Projections**: Long-term player value assessment from multiple sources

### 📋 **Start/Sit Accuracy Analysis**
- **Team Performance Tracking**: Lineup efficiency analysis and points left on bench per team
- **League-Wide Intelligence**: Rankings, decision-making insights, and competitive analysis  
- **Multi-Source Comparison**: Compare ESPN, FanDuel, WinWithOdds for optimal lineup decisions
- **Position Trends**: Identify which positions are hardest to optimize league-wide
- **Decision Impact Analysis**: Quantify the value of individual start/sit decisions
- **Professional Reports**: Interactive HTML reports with team rankings and grades

### ⚡ **Professional CLI with Analytics**
- **Rich Output**: Color-coded results with professional formatting and trend indicators
- **Historical Queries**: Access current vs historical data for any player or team
- **Modular Commands**: Status, analysis, reports, automation triggers with data context
- **Export Options**: HTML, CSV, JSON formats for external analysis
- **Database Management**: Schema migration and data validation commands

### 🔧 **Clean Automation Architecture** *(Latest Enhancement)*
- **Simplified Automation**: Direct CLI-based automation with `--email` flags
- **Template-Based Reports**: Professional Jinja2 templates with responsive design
- **Eliminated Duplication**: Removed 400+ lines of redundant HTML generation code
- **Tuesday 6am Automation**: One-command setup for post-week analysis delivery
- **Webhook Integration**: Seamless email delivery via Google Apps Script
- **Cron Job Ready**: Simple setup scripts for automated report generation

## Quick Start

### 1. Installation
```bash
git clone https://github.com/yourusername/fantasy-ai.git
cd fantasy-ai
uv pip install .
```

### 2. Configuration
```bash
cp .env.example .env
# Edit .env with your ESPN league credentials (LEAGUE_ID, ESPN_S2, SWID)
```

### 3. Initialize Enhanced Storage
```bash
# Initialize database with historical tracking (auto-migrates existing data)
uv run python -c "from src.enhanced_projection_storage import EnhancedProjectionStorage; storage = EnhancedProjectionStorage(); print('✅ Database ready')"

# Check current schema
uv run python -c "
from src.enhanced_projection_storage import EnhancedProjectionStorage
storage = EnhancedProjectionStorage()
tables = storage.conn.execute('SHOW TABLES').fetchall()
print('📊 Available tables:', [t[0] for t in tables])
"
```

## 🎯 Professional CLI Commands

### Core Analysis Commands
```bash
# Quick team analysis with historical context
uv run python cli.py quick --top 5

# Comprehensive waiver wire cheatsheet with trend analysis
uv run python cli.py waiver --size 100 --open

# Team status with projection trends and historical performance
uv run python cli.py status --verbose

# AI-enhanced strategic analysis with historical decision context
uv run python cli.py claude --copy --verbose

# Start/sit lineup recommendations with matchup history
uv run python cli.py startsit --open

# Comprehensive start/sit accuracy analysis
uv run python cli.py start-sit-analysis --week 1 --show-summary --open
```

### Automation Commands
```bash
# Post-week analysis with projection accuracy and start/sit intelligence
uv run python cli.py postweek --html --sources espn fanduel wwo --strategic

# Post-week analysis with automatic email delivery
uv run python cli.py postweek --html --email --sources espn fanduel wwo --strategic

# Set up automated Tuesday 6am post-week analysis
./automation/setup_postweek_cron.sh
```

### Data Capture & Import Commands
```bash
# Capture projections with historical preservation
uv run python cli.py capture --sources espn fanduel wwo --week 1

# Import strength of schedule data (preserves all historical data)
uv run python -m archive.import_team_sos wr
uv run python -m archive.import_team_sos rb
uv run python -m archive.import_team_sos te
uv run python -m archive.import_team_sos qb

# Import streaming projections with historical tracking
uv run python archive/import_streaming_ai.py dst 1
uv run python archive/import_streaming_ai.py k 1
```

### Analytics & Historical Analysis
```bash
# Post-week projection accuracy analysis with historical context
uv run python cli.py postweek --html --save analysis.json --sources espn fanduel wwo

# Team-specific start/sit analysis with league context
uv run python cli.py start-sit-analysis --team "Your Team Name" --verbose

# League-wide start/sit intelligence and rankings
uv run python cli.py start-sit-analysis --week 1 --no-projections

# Email automation with historical insights (legacy support)
uv run python cli.py email pre-waiver --verbose
uv run python cli.py email post-waiver

# Test automated reports (new clean architecture)
uv run python cli.py postweek --html --email --week 1 --sources espn fanduel wwo

# Integrate all cached projection sources into enhanced storage
python -m src.projection_integrator
```

### Database Analytics Examples
```python
from src.enhanced_projection_storage import EnhancedProjectionStorage
from src.start_sit_accuracy_analyzer import StartSitAccuracyAnalyzer

storage = EnhancedProjectionStorage()

# Current vs historical analysis
current_wr_sos = storage.get_current_team_sos('WR')
falcons_history = storage.get_team_sos_history('Falcons', 'WR')

# Track recent changes
recent_changes = storage.get_sos_changes_since(days_back=7, position='WR')

# Get current projections with historical context
current_projections = storage.get_current_projections(week=1, position='WR')

# Start/sit accuracy analysis
analyzer = StartSitAccuracyAnalyzer(league)
team_performance = analyzer.analyze_team_start_sit_performance(team, week=1)
league_intelligence = analyzer.analyze_league_start_sit_intelligence(week=1)

print(f"Team efficiency: {team_performance.lineup_efficiency:.1f}%")
print(f"Points left on bench: {team_performance.points_left_on_bench:.1f}")
print(f"League average efficiency: {league_intelligence.league_avg_lineup_efficiency:.1f}%")
```

## Database Schema Overview

### Enhanced Analytics Tables
```sql
-- Projections with historical tracking
projections (
    week, year, player_name, source, position, team,
    projected_points, capture_timestamp, raw_source_data
    PRIMARY KEY (week, year, player_name, source, capture_timestamp)
)

-- Team strength of schedule with change tracking
team_strength_of_schedule (
    team, position, baseline, week_1...week_17,
    capture_timestamp, data_source
    PRIMARY KEY (team, position, capture_timestamp)
)

-- Performance and decision tracking
player_performance_history, decision_outcomes, report_metadata, capture_metadata
```

## File Overview

### Core Components
- `fantasy_manager.py`: Main FantasyManager class with waiver analysis and HTML generation
- `src/enhanced_projection_storage.py`: **DuckDB analytics backend with historical tracking**
- `waiver_analyzer.py`: Dedicated waiver wire analysis logic with historical context
- `fanduel_projections.py`: FanDuel GraphQL API integration for external projections
- `src/strategic_analyzer.py`: AI-enhanced strategic analysis with historical decision learning
- `src/start_sit_accuracy_analyzer.py`: **Comprehensive start/sit decision analysis and league intelligence**
- `src/start_sit_report_generator.py`: Professional HTML reports with Jinja2 templates

### Data Import & Analytics
- `archive/import_team_sos.py`: **Team strength of schedule import with historical preservation**
- `archive/import_streaming_ai.py`: Subvertadown streaming projections with trend tracking
- `src/projection_integrator.py`: Multi-source projection integration and validation
- `src/post_week_analyzer.py`: Historical projection accuracy analysis and performance tracking

### CLI & Automation
- `cli.py`: Professional command-line interface with analytics integration
- `src/commands/`: Modular command structure with historical data access
- `automation/`: Google Apps Script integration and cron automation
- `src/claude_analyzer.py`: AI analysis with historical context and decision learning

## Key Features

### 📈 **Historical Data Architecture**
- **No Data Loss**: All historical projections and SOS data preserved with timestamps
- **Trend Analysis**: Track how projections change throughout the week and season
- **Change Detection**: Identify significant projection or matchup changes for strategic advantage
- **Performance Tracking**: Historical accuracy analysis by projection source and position

### 🎯 **Strategic Decision Support**
- **Historical Context**: Reference past decisions and outcomes for better future choices
- **Market Timing**: Identify when projections change for optimal waiver wire timing
- **Source Reliability**: Track which projection sources are most accurate over time
- **Continuous Learning**: AI analysis improves based on historical decision outcomes

### ⚡ **Professional Analytics**
- **Rich Querying**: Easy access to current vs historical data for any player or team
- **Performance Optimization**: DuckDB provides fast analytical queries on large datasets
- **Data Validation**: Comprehensive validation pipeline with automatic error correction
- **Export Flexibility**: Multiple output formats for external analysis and sharing

### 🎯 **Start/Sit Intelligence Use Cases**
- **Lineup Optimization**: Identify teams that consistently make poor start/sit decisions
- **Competitive Analysis**: Track which teams are best at lineup construction in your league
- **Position Strategy**: Understand which positions are hardest to optimize (RB vs WR difficulty)
- **Projection Validation**: Determine which sources (ESPN/FanDuel/WinWithOdds) are best for lineup decisions
- **Historical Learning**: Build database of decision outcomes for continuous improvement
- **League Intelligence**: Generate insights like "You make better start/sit decisions than 8/10 teams"

**Example Insights Generated**:
- "RB position has highest points left on bench (3.2 avg) - hardest lineup decisions"
- "FanDuel projections led to 15% better lineup efficiency than ESPN for Week 1"
- "League average leaves 12.3 points on bench per week - you left only 8.1 points"
- "Team Alpha has 94.2% lineup efficiency (A grade) - best decision maker in league"

This enhanced data architecture transforms Fantasy AI from a simple projection tool into a comprehensive analytics platform capable of sophisticated trend analysis, historical context, strategic decision support, and competitive intelligence for championship-level fantasy football management.
