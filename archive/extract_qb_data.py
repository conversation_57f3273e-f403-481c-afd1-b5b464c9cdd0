#!/usr/bin/env python3
"""
Extract all QB data from Subvertadown browser snapshot and store in DuckDB
"""

from src.enhanced_projection_storage import EnhancedProjectionStorage, StrategicProjectionRecord

def extract_qb_data_from_browser():
    """Extract all QB data from the browser snapshot"""
    
    # Based on the browser snapshot, here's ALL the QB data (32 quarterbacks total)
    qb_projections = [
        {
            "player": "Jalen Hurts",
            "matchup": "PHI vs. DAL",
            "wk1": 21.9,
            "wk2": 19.7,
            "error_rate": 6.6,
            "passing_yards": 226,
            "rushing_yards": 47,
            "passing_tds": 1.8,
            "int_rate": 0.6
        },
        {
            "player": "<PERSON>",
            "matchup": "BUF vs. BAL",
            "wk1": 21.8,
            "wk2": 20.4,
            "error_rate": 6.7,
            "passing_yards": 238,
            "rushing_yards": 40,
            "passing_tds": 1.9,
            "int_rate": 0.7
        },
        {
            "player": "<PERSON>",
            "matchup": "BAL @ BUF",
            "wk1": 21.8,
            "wk2": 21.8,
            "error_rate": 6.3,
            "passing_yards": 234,
            "rushing_yards": 39,
            "passing_tds": 2.0,
            "int_rate": 0.7
        },
        {
            "player": "Jayden Daniels",
            "matchup": "WAS vs. NYG",
            "wk1": 21.7,
            "wk2": 20.1,
            "error_rate": 6.5,
            "passing_yards": 237,
            "rushing_yards": 45,
            "passing_tds": 1.8,
            "int_rate": 0.6
        },
        {
            "player": "Daniel Jones",
            "matchup": "IND vs. MIA",
            "wk1": 19.5,
            "wk2": 16.4,
            "error_rate": 6.4,
            "passing_yards": 231,
            "rushing_yards": 36,
            "passing_tds": 1.6,
            "int_rate": 0.8
        },
        {
            "player": "Kyler Murray",
            "matchup": "ARI @ NO",
            "wk1": 19.0,
            "wk2": 18.7,
            "error_rate": 6.1,
            "passing_yards": 234,
            "rushing_yards": 29,
            "passing_tds": 1.5,
            "int_rate": 0.6
        },
        {
            "player": "Joe Burrow",
            "matchup": "CIN @ CLE",
            "wk1": 18.9,
            "wk2": 19.8,
            "error_rate": 5.5,
            "passing_yards": 263,
            "rushing_yards": 12,
            "passing_tds": 2.0,
            "int_rate": 0.6
        },
        {
            "player": "Patrick Mahomes",
            "matchup": "KC @ LAC",
            "wk1": 18.0,
            "wk2": 17.0,
            "error_rate": 4.5,
            "passing_yards": 243,
            "rushing_yards": 21,
            "passing_tds": 1.7,
            "int_rate": 0.7
        },
        {
            "player": "Baker Mayfield",
            "matchup": "TB @ ATL",
            "wk1": 17.9,
            "wk2": 18.3,
            "error_rate": 6.0,
            "passing_yards": 247,
            "rushing_yards": 14,
            "passing_tds": 1.8,
            "int_rate": 0.7
        },
        {
            "player": "Trevor Lawrence",
            "matchup": "JAC vs. CAR",
            "wk1": 17.9,
            "wk2": 16.7,
            "error_rate": 5.9,
            "passing_yards": 242,
            "rushing_yards": 21,
            "passing_tds": 1.7,
            "int_rate": 0.7
        },
        {
            "player": "Bo Nix",
            "matchup": "DEN vs. TEN",
            "wk1": 17.9,
            "wk2": 17.7,
            "error_rate": 5.9,
            "passing_yards": 224,
            "rushing_yards": 27,
            "passing_tds": 1.6,
            "int_rate": 0.6
        },
        {
            "player": "Drake Maye",
            "matchup": "NE vs. LV",
            "wk1": 17.4,
            "wk2": 15.9,
            "error_rate": 5.9,
            "passing_yards": 211,
            "rushing_yards": 31,
            "passing_tds": 1.4,
            "int_rate": 0.7
        },
        {
            "player": "Jordan Love",
            "matchup": "GB vs. DET",
            "wk1": 17.2,
            "wk2": 17.5,
            "error_rate": 5.7,
            "passing_yards": 234,
            "rushing_yards": 15,
            "passing_tds": 1.8,
            "int_rate": 0.7
        },
        {
            "player": "Caleb Williams",
            "matchup": "CHI vs. MIN",
            "wk1": 16.3,
            "wk2": 16.9,
            "error_rate": 6.0,
            "passing_yards": 210,
            "rushing_yards": 29,
            "passing_tds": 1.4,
            "int_rate": 0.7
        },
        {
            "player": "Jared Goff",
            "matchup": "DET @ GB",
            "wk1": 16.0,
            "wk2": 17.8,
            "error_rate": 5.6,
            "passing_yards": 235,
            "rushing_yards": 9,
            "passing_tds": 1.7,
            "int_rate": 0.7
        },
        {
            "player": "Joe Flacco",
            "matchup": "CLE vs. CIN",
            "wk1": 15.9,
            "wk2": 14.6,
            "error_rate": 5.9,
            "passing_yards": 207,
            "rushing_yards": 25,
            "passing_tds": 1.4,
            "int_rate": 0.8
        },
        {
            "player": "Matthew Stafford",
            "matchup": "LAR vs. HOU",
            "wk1": 15.8,
            "wk2": 16.9,
            "error_rate": 6.1,
            "passing_yards": 234,
            "rushing_yards": 12,
            "passing_tds": 1.6,
            "int_rate": 0.7
        },
        {
            "player": "Tua Tagovailoa",
            "matchup": "MIA @ IND",
            "wk1": 15.6,
            "wk2": 12.4,
            "error_rate": 5.4,
            "passing_yards": 230,
            "rushing_yards": 9,
            "passing_tds": 1.5,
            "int_rate": 0.7
        },
        {
            "player": "J.J. McCarthy",
            "matchup": "MIN @ CHI",
            "wk1": 15.6,
            "wk2": 18.9,
            "error_rate": 5.8,
            "passing_yards": 225,
            "rushing_yards": 15,
            "passing_tds": 1.6,
            "int_rate": 0.8
        },
        {
            "player": "Bryce Young",
            "matchup": "CAR @ JAC",
            "wk1": 15.6,
            "wk2": 14.3,
            "error_rate": 5.8,
            "passing_yards": 210,
            "rushing_yards": 19,
            "passing_tds": 1.4,
            "int_rate": 0.8
        },
        {
            "player": "Michael Penix",
            "matchup": "ATL vs. TB",
            "wk1": 15.4,
            "wk2": 12.3,
            "error_rate": 5.9,
            "passing_yards": 236,
            "rushing_yards": 9,
            "passing_tds": 1.5,
            "int_rate": 0.7
        },
        {
            "player": "Russell Wilson",
            "matchup": "NYG @ WAS",
            "wk1": 15.4,
            "wk2": 15.2,
            "error_rate": 6.0,
            "passing_yards": 194,
            "rushing_yards": 27,
            "passing_tds": 1.2,
            "int_rate": 0.8
        },
        {
            "player": "Brock Purdy",
            "matchup": "SF @ SEA",
            "wk1": 15.2,
            "wk2": 17.9,
            "error_rate": 5.8,
            "passing_yards": 223,
            "rushing_yards": 20,
            "passing_tds": 1.3,
            "int_rate": 0.7
        },
        {
            "player": "Justin Herbert",
            "matchup": "LAC @ KC",
            "wk1": 15.1,
            "wk2": 15.4,
            "error_rate": 4.5,
            "passing_yards": 205,
            "rushing_yards": 21,
            "passing_tds": 1.4,
            "int_rate": 0.7
        },
        {
            "player": "Sam Darnold",
            "matchup": "SEA vs. SF",
            "wk1": 14.9,
            "wk2": 13.4,
            "error_rate": 5.7,
            "passing_yards": 214,
            "rushing_yards": 17,
            "passing_tds": 1.3,
            "int_rate": 0.7
        },
        {
            "player": "C.J. Stroud",
            "matchup": "HOU @ LAR",
            "wk1": 14.8,
            "wk2": 17.1,
            "error_rate": 5.9,
            "passing_yards": 215,
            "rushing_yards": 16,
            "passing_tds": 1.4,
            "int_rate": 0.8
        },
        {
            "player": "Aaron Rodgers",
            "matchup": "PIT @ NYJ",
            "wk1": 14.1,
            "wk2": 16.2,
            "error_rate": 5.5,
            "passing_yards": 195,
            "rushing_yards": 20,
            "passing_tds": 1.2,
            "int_rate": 0.7
        },
        {
            "player": "Geno Smith",
            "matchup": "LV @ NE",
            "wk1": 13.4,
            "wk2": 17.1,
            "error_rate": 5.6,
            "passing_yards": 213,
            "rushing_yards": 13,
            "passing_tds": 1.2,
            "int_rate": 0.8
        },
        {
            "player": "Dak Prescott",
            "matchup": "DAL @ PHI",
            "wk1": 13.4,
            "wk2": 16.0,
            "error_rate": 5.7,
            "passing_yards": 206,
            "rushing_yards": 10,
            "passing_tds": 1.4,
            "int_rate": 0.8
        },
        {
            "player": "Tyler Shough",
            "matchup": "NO vs. ARI",
            "wk1": 12.6,
            "wk2": 14.1,
            "error_rate": 5.4,
            "passing_yards": 199,
            "rushing_yards": 14,
            "passing_tds": 1.1,
            "int_rate": 0.8
        },
        {
            "player": "Cam Ward",
            "matchup": "TEN @ DEN",
            "wk1": 11.3,
            "wk2": 15.1,
            "error_rate": 5.3,
            "passing_yards": 177,
            "rushing_yards": 15,
            "passing_tds": 1.0,
            "int_rate": 0.8
        },
        {
            "player": "Justin Fields",
            "matchup": "NYJ vs. PIT",
            "wk1": 11.3,
            "wk2": 14.2,
            "error_rate": 5.3,
            "passing_yards": 191,
            "rushing_yards": 17,
            "passing_tds": 0.8,
            "int_rate": 0.8
        }
    ]
    
    return qb_projections

def convert_to_qb_records(data_list, week: int = 1):
    """Convert extracted QB data to StrategicProjectionRecord objects"""
    
    records = []
    
    for item in data_list:
        # Extract team from matchup
        team = item['matchup'].split()[0]
        
        raw_data = {
            'matchup': item['matchup'],
            'error_rate': item['error_rate'],
            'passing_yards': item['passing_yards'],
            'rushing_yards': item['rushing_yards'],
            'passing_touchdowns': item['passing_tds'],
            'interception_rate': item['int_rate'],
            'week_2_projection': item.get('wk2')
        }
        
        record = StrategicProjectionRecord(
            week=week,
            year=2025,
            player_id=None,
            player_name=item['player'],
            position='QB',
            team=team,
            source='Subvertadown',
            projected_points=item['wk1'],
            raw_source_data=raw_data
        )
        records.append(record)
    
    return records

def main():
    """Extract QB data from browser and store in enhanced projection storage"""
    
    print("🤖 Extracting QB data from browser snapshot...")
    
    # Extract the data
    qb_data = extract_qb_data_from_browser()
    
    print(f"📊 Extracted {len(qb_data)} quarterbacks")
    
    # Convert to projection records
    records = convert_to_qb_records(qb_data, week=1)
    
    # Store in DuckDB
    storage = EnhancedProjectionStorage()
    count = storage._store_projections(records)
    
    print(f"✅ Imported {count} QB projections to enhanced projection storage")
    
    # Show top 5 projections
    if records:
        print(f"\n🔥 Elite Tier QBs (20+ points):")
        elite_qbs = [r for r in records if r.projected_points >= 20.0]
        for i, r in enumerate(sorted(elite_qbs, key=lambda r: r.projected_points, reverse=True), 1):
            matchup = r.raw_source_data.get('matchup', '')
            rushing_yards = r.raw_source_data.get('rushing_yards', 0)
            print(f"  {i}. {r.player_name} {matchup}: {r.projected_points} (RuY: {rushing_yards})")
        
        print(f"\n🎯 Streaming Tier QBs (15-19 points):")
        streaming_qbs = [r for r in records if 15.0 <= r.projected_points < 20.0]
        top_streaming = sorted(streaming_qbs, key=lambda r: r.projected_points, reverse=True)[:5]
        for i, r in enumerate(top_streaming, 1):
            matchup = r.raw_source_data.get('matchup', '')
            rushing_yards = r.raw_source_data.get('rushing_yards', 0)
            error_rate = r.raw_source_data.get('error_rate', 0)
            print(f"  {i}. {r.player_name} {matchup}: {r.projected_points} (RuY: {rushing_yards}, Err: {error_rate})")
        
        # Highlight variance opportunities
        print(f"\n📈 Highest Week-to-Week Variance (Streaming Opportunities):")
        variance_qbs = []
        for r in records:
            wk2 = r.raw_source_data.get('week_2_projection')
            if wk2 is not None:
                variance = abs(r.projected_points - wk2)
                variance_qbs.append((r, variance))
        
        top_variance = sorted(variance_qbs, key=lambda x: x[1], reverse=True)[:5]
        for r, var in top_variance:
            wk2 = r.raw_source_data.get('week_2_projection')
            print(f"  {r.player_name}: {r.projected_points} → {wk2} ({var:+.1f} variance)")
    
    print(f"\n📈 QB data now available in enhanced projection storage!")

if __name__ == "__main__":
    main()