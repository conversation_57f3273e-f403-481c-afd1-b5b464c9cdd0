#!/usr/bin/env python3
"""
AI-powered clipboard to DuckDB importer for Subvertadown data
Usage: Copy table from browser → python import_streaming_ai.py dst
"""

import subprocess
import sys
import re
from typing import List, Dict, Any
from src.enhanced_projection_storage import EnhancedProjectionStorage, StrategicProjectionRecord

def get_clipboard_data():
    """Get data from macOS clipboard"""
    try:
        result = subprocess.run(['pbpaste'], capture_output=True, text=True)
        return result.stdout.strip()
    except Exception as e:
        print(f"Error reading clipboard: {e}")
        sys.exit(1)

def parse_dst_with_ai(clipboard_text: str) -> List[Dict[str, Any]]:
    """Use AI logic to intelligently parse D/ST table data"""
    
    lines = [line.strip() for line in clipboard_text.split('\n') if line.strip()]
    
    print(f"Debug: Total lines: {len(lines)}")
    
    # Find where team data starts
    team_start = None
    for i, line in enumerate(lines):
        if 'vs.' in line or '@' in line:
            team_start = i
            break
    
    if team_start is None:
        print("Debug: No team matchup lines found!")
        return []
    
    print(f"Debug: Found first team at line {team_start}: '{lines[team_start]}'")
    
    extracted_data = []
    i = team_start
    
    while i < len(lines):
        line = lines[i]
        
        # Check if this is a team matchup line
        if 'vs.' in line or '@' in line:
            try:
                # Extract team name
                if ' vs. ' in line:
                    team = line.split(' vs. ')[0]
                elif ' @ ' in line:
                    team = line.split(' @ ')[0]
                else:
                    team = line.split()[0]
                
                matchup = line
                
                # Get the next values - they might be on separate lines or tab-separated
                values = []
                j = 1
                while j <= 12 and i + j < len(lines):  # Look for next 12 values
                    val_line = lines[i + j].strip()
                    
                    # Stop if we hit another team name
                    if 'vs.' in val_line or '@' in val_line:
                        break
                    
                    # Check if this line has tab-separated values
                    if '\t' in val_line:
                        # Split on tabs and add all values
                        tab_values = val_line.split('\t')
                        values.extend(tab_values)
                        j += 1
                        break  # This line had multiple values, we're done
                    else:
                        # Single value on this line
                        values.append(val_line)
                        j += 1
                
                if len(values) >= 3:  # At least need Wk1, Trend, Wk2
                    wk1_proj = float(values[0]) if values[0] != '-' else 0.0
                    wk2_proj = float(values[2]) if len(values) > 2 and values[2] != '-' else None
                    
                    # Parse additional stats if available
                    error_rate = float(values[5]) if len(values) > 5 and values[5] != '-' else None
                    pa = float(values[6]) if len(values) > 6 and values[6] != '-' else None
                    ya = float(values[7]) if len(values) > 7 and values[7] != '-' else None
                    int_pct = float(values[8]) if len(values) > 8 and values[8] != '-' else None
                    sacks = float(values[9]) if len(values) > 9 and values[9] != '-' else None
                    fr = float(values[10]) if len(values) > 10 and values[10] != '-' else None
                    td = float(values[11]) if len(values) > 11 and values[11] != '-' else None
                    
                    extracted_data.append({
                        'team': team,
                        'matchup': matchup,
                        'wk1': wk1_proj,
                        'wk2': wk2_proj,
                        'hold': False,  # Would need to detect from Hold column
                        'error_rate': error_rate,
                        'pa': pa,
                        'ya': ya,
                        'int_pct': int_pct,
                        'sacks': sacks,
                        'fr': fr,
                        'td': td
                    })
                    
                    print(f"Debug: Parsed {team}: Wk1={wk1_proj}, Wk2={wk2_proj}")
                
                # Skip to next team (advance by number of lines we consumed)
                i += j
                
            except Exception as e:
                print(f"Error parsing team at line {i}: {e}")
                i += 1
        else:
            i += 1
    
    return extracted_data

def parse_qb_with_ai(clipboard_text: str) -> List[Dict[str, Any]]:
    """Intelligently parse QB table data"""
    
    lines = [line.strip() for line in clipboard_text.split('\n') if line.strip()]
    
    print(f"Debug: Total lines: {len(lines)}")
    
    # Find where player data starts - look for player names followed by team matchups
    player_start = None
    for i, line in enumerate(lines):
        # Look for patterns like team abbreviations that indicate player data
        if any(team in line for team in ['PHI', 'BUF', 'BAL', 'KC', 'LAR', 'SF', 'DAL', 'MIA', 'vs.', '@']):
            # Check if previous line might be a player name
            if i > 0 and not any(keyword in lines[i-1] for keyword in ['Wk', 'Player', 'Matchup', 'Stats', 'Err', 'PaY', 'RuY', 'PTD', 'Int']):
                player_start = i - 1
                break
    
    if player_start is None:
        print("Debug: No player data found!")
        return []
    
    print(f"Debug: Found first player at line {player_start}: '{lines[player_start]}'")
    
    extracted_data = []
    i = player_start
    
    while i < len(lines):
        line = lines[i]
        
        # Check if this looks like a player name (not a stat value or header)
        if (not line.replace('.', '').replace('-', '').replace('\t', '').isdigit() and 
            not any(keyword in line for keyword in ['Wk', 'Player', 'Matchup', 'Stats', 'Err', 'PaY', 'RuY', 'PTD', 'Int']) and
            len(line.split()) >= 2 and
            not any(pattern in line for pattern in ['vs.', '@']) and
            not line.replace('\t', ' ').replace('.', '').replace(' ', '').isdigit()):  # Not just numbers
            
            try:
                player_name = line
                
                # Next line should be the matchup
                if i + 1 < len(lines):
                    matchup = lines[i + 1]
                    
                    # Get the next values - they might be on separate lines or tab-separated
                    values = []
                    j = 2  # Start after player name and matchup
                    while j <= 10 and i + j < len(lines):  # Look for next 8 values
                        val_line = lines[i + j].strip()
                        
                        # Stop if we hit another player name (not a number and not a matchup)
                        if (not val_line.replace('.', '').replace('-', '').replace('\t', '').isdigit() and 
                            len(val_line.split()) >= 2 and
                            not any(pattern in val_line for pattern in ['vs.', '@']) and
                            not val_line.replace('\t', ' ').replace('.', '').replace(' ', '').isdigit()):
                            break
                        
                        # Skip matchup lines
                        if any(pattern in val_line for pattern in ['vs.', '@']):
                            j += 1
                            continue
                        
                        # Check if this line has tab-separated values
                        if '\t' in val_line:
                            # Split on tabs and add all values
                            tab_values = val_line.split('\t')
                            values.extend(tab_values)
                            j += 1
                            break  # This line had multiple values, we're done
                        else:
                            # Single value on this line
                            values.append(val_line)
                            j += 1
                    
                    if len(values) >= 2:  # At least need Wk1, Wk2
                        wk1_proj = float(values[0]) if values[0] != '-' else 0.0
                        wk2_proj = float(values[1]) if len(values) > 1 and values[1] != '-' else None
                        
                        # Parse QB-specific stats if available
                        error_rate = float(values[2]) if len(values) > 2 and values[2] != '-' else None
                        pay = float(values[3]) if len(values) > 3 and values[3] != '-' else None
                        ruy = float(values[4]) if len(values) > 4 and values[4] != '-' else None
                        ptd = float(values[5]) if len(values) > 5 and values[5] != '-' else None
                        int_pct = float(values[6]) if len(values) > 6 and values[6] != '-' else None
                        
                        # Extract team from matchup
                        team = matchup.split()[0] if matchup else ""
                        
                        extracted_data.append({
                            'player': player_name,
                            'matchup': matchup,
                            'team': team,
                            'wk1': wk1_proj,
                            'wk2': wk2_proj,
                            'error_rate': error_rate,
                            'passing_yards': pay,
                            'rushing_yards': ruy,
                            'passing_tds': ptd,
                            'int_percentage': int_pct
                        })
                        
                        print(f"Debug: Parsed {player_name}: Wk1={wk1_proj}, Wk2={wk2_proj}")
                    
                    # Skip to next player (advance by number of lines we consumed)
                    i += j
                else:
                    i += 1
                    
            except Exception as e:
                print(f"Error parsing player at line {i}: {e}")
                i += 1
        else:
            i += 1
    
    return extracted_data

def parse_kicker_with_ai(clipboard_text: str) -> List[Dict[str, Any]]:
    """Intelligently parse Kicker table data"""
    
    lines = [line.strip() for line in clipboard_text.split('\n') if line.strip()]
    
    print(f"Debug: Total lines: {len(lines)}")
    
    # Find where player data starts - look for lines with team abbreviations and matchups
    player_start = None
    for i, line in enumerate(lines):
        # Look for patterns like "Player vs Team" or "Player @ Team" 
        if any(team in line for team in ['vs.', '@', 'IND', 'MIA', 'ARI', 'NO', 'SEA', 'SF', 'NYG', 'WAS', 'TEN', 'DEN']):
            player_start = i
            break
    
    if player_start is None:
        print("Debug: No player matchup lines found!")
        return []
    
    print(f"Debug: Found first player at line {player_start}: '{lines[player_start]}'")
    
    extracted_data = []
    i = player_start
    
    while i < len(lines):
        line = lines[i]
        
        # Check if this looks like a player line (has team abbreviations or vs/@)
        if any(pattern in line for pattern in ['vs.', '@']) or any(team in line for team in ['IND', 'MIA', 'ARI', 'NO', 'SEA', 'SF', 'NYG', 'WAS', 'TEN', 'DEN']):
            try:
                # This line should contain player name and matchup
                player_matchup = line
                
                # Extract player name (everything before the team matchup)
                parts = line.split()
                player_name = ""
                matchup = ""
                
                # Find where the matchup starts
                for j, part in enumerate(parts):
                    if any(team in part for team in ['IND', 'MIA', 'ARI', 'NO', 'SEA', 'SF', 'NYG', 'WAS', 'TEN', 'DEN']) or part in ['vs.', '@']:
                        player_name = ' '.join(parts[:j])
                        matchup = ' '.join(parts[j:])
                        break
                
                if not player_name:
                    # Fallback: assume first two words are player name
                    player_name = ' '.join(parts[:2])
                    matchup = ' '.join(parts[2:])
                
                # Get the next values - they might be on separate lines or tab-separated
                values = []
                j = 1
                while j <= 12 and i + j < len(lines):  # Look for next 12 values
                    val_line = lines[i + j].strip()
                    
                    # Stop if we hit another player name (contains team abbreviations)
                    if any(pattern in val_line for pattern in ['vs.', '@']) or any(team in val_line for team in ['IND', 'MIA', 'ARI', 'NO', 'SEA', 'SF', 'NYG', 'WAS', 'TEN', 'DEN']):
                        break
                    
                    # Check if this line has tab-separated values
                    if '\t' in val_line:
                        # Split on tabs and add all values
                        tab_values = val_line.split('\t')
                        values.extend(tab_values)
                        j += 1
                        break  # This line had multiple values, we're done
                    else:
                        # Single value on this line
                        values.append(val_line)
                        j += 1
                
                if len(values) >= 2:  # At least need Wk1, Wk2
                    wk1_proj = float(values[0]) if values[0] != '-' else 0.0
                    wk2_proj = float(values[1]) if len(values) > 1 and values[1] != '-' else None
                    
                    # Parse kicker-specific stats if available
                    error_rate = float(values[3]) if len(values) > 3 and values[3] != '-' else None
                    fg_att = float(values[4]) if len(values) > 4 and values[4] != '-' else None
                    fg_0_29 = float(values[5]) if len(values) > 5 and values[5] != '-' else None
                    fg_30_39 = float(values[6]) if len(values) > 6 and values[6] != '-' else None
                    fg_40_49 = float(values[7]) if len(values) > 7 and values[7] != '-' else None
                    fg_50_plus = float(values[8]) if len(values) > 8 and values[8] != '-' else None
                    xp = float(values[9]) if len(values) > 9 and values[9] != '-' else None
                    impl_score = float(values[10]) if len(values) > 10 and values[10] != '-' else None
                    
                    # Extract team from matchup
                    team = matchup.split()[0] if matchup else ""
                    
                    # Check for hold flag
                    hold_flag = any(val in ['Y', '✓', 'Hold'] for val in values[2:3]) if len(values) > 2 else False
                    
                    extracted_data.append({
                        'player': player_name,
                        'matchup': matchup,
                        'team': team,
                        'wk1': wk1_proj,
                        'wk2': wk2_proj,
                        'hold': hold_flag,
                        'error_rate': error_rate,
                        'fg_attempts': fg_att,
                        'fg_0_29': fg_0_29,
                        'fg_30_39': fg_30_39,
                        'fg_40_49': fg_40_49,
                        'fg_50_plus': fg_50_plus,
                        'xp': xp,
                        'implied_score': impl_score
                    })
                    
                    print(f"Debug: Parsed {player_name}: Wk1={wk1_proj}, Wk2={wk2_proj}")
                
                # Skip to next player (advance by number of lines we consumed)
                i += j
                
            except Exception as e:
                print(f"Error parsing player at line {i}: {e}")
                i += 1
        else:
            i += 1
    
    return extracted_data

def convert_to_projection_records(data_list, position_type: str, week: int = 1):
    """Convert extracted data to StrategicProjectionRecord objects"""
    
    records = []
    for data in data_list:
        try:
            # Handle different position formats
            if position_type == 'qb':
                player_name = data['player']
                team = data.get('team', '')
                position = 'QB'
            elif position_type == 'k':
                player_name = data['player']
                team = data.get('team', '')
                position = 'K'
            else:  # DST format
                player_name = f"{data['team']} D/ST"
                team = data['team']
                position = 'D/ST'
            
            # Use Week 1 projection as primary
            projected_points = data.get('wk1', 0.0)
            
            record = StrategicProjectionRecord(
                week=week,
                year=2025,
                player_id=None,
                player_name=player_name,
                position=position,
                team=team,
                source='Subvertadown',
                projected_points=projected_points,
                raw_source_data=data
            )
            records.append(record)
            
        except Exception as e:
            print(f"Error converting record: {e}")
            
    return records

def main():
    if len(sys.argv) < 2:
        print("Usage: python import_streaming_ai.py <dst|qb|k> [week]")
        sys.exit(1)
    
    position = sys.argv[1].lower()
    week = int(sys.argv[2]) if len(sys.argv) > 2 else 1
    
    print(f"🤖 Using AI to parse {position.upper()} data from clipboard...")
    
    # Get clipboard data
    clipboard_text = get_clipboard_data()
    
    if not clipboard_text:
        print("❌ No data found in clipboard")
        sys.exit(1)
    
    print(f"Clipboard preview: {clipboard_text[:100]}...")
    
    # Parse based on position
    if position == 'dst':
        parsed_data = parse_dst_with_ai(clipboard_text)
    elif position == 'qb':
        parsed_data = parse_qb_with_ai(clipboard_text)
    elif position == 'k':
        parsed_data = parse_kicker_with_ai(clipboard_text)
    else:
        print(f"Position {position} not supported")
        sys.exit(1)
    
    if not parsed_data:
        print("❌ No data parsed from clipboard")
        sys.exit(1)
    
    print(f"📊 Parsed {len(parsed_data)} rows")
    
    # Convert to records
    records = convert_to_projection_records(parsed_data, position, week)
    
    # DEBUG: Show what we're about to store
    print(f"\n🔍 Sample record structure:")
    if records:
        sample = records[0]
        print(f"  Player: {sample.player_name}")
        print(f"  Position: {sample.position}")
        print(f"  Team: {sample.team}")
        print(f"  Projected Points: {sample.projected_points}")
        print(f"  Source: {sample.source}")
        print(f"  Raw data keys: {list(sample.raw_source_data.keys())}")
        print(f"  Strategic insights keys: {list(sample.strategic_insights.keys())}")
    
    # Store in DuckDB
    storage = EnhancedProjectionStorage()
    count = storage._store_projections(records)
    print(f"✅ Imported {count} {position.upper()} projections to DuckDB")
    
    # print(f"✅ Would import {len(records)} {position.upper()} projections to DuckDB")
    
    # Show top 5 projections
    if records:
        top_5 = sorted(records, key=lambda r: r.projected_points, reverse=True)[:5]
        print(f"\nTop 5 {position.upper()} projections:")
        for r in top_5:
            hold_text = " [HOLD]" if r.raw_source_data.get('hold') else ""
            print(f"  {r.player_name}: {r.projected_points}{hold_text}")

if __name__ == "__main__":
    main()
