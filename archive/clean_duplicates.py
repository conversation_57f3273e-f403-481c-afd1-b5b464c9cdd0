#!/usr/bin/env python3
"""
Clean duplicate D/ST records from enhanced projection storage
"""

from src.enhanced_projection_storage import EnhancedProjectionStorage
import duckdb

def clean_dst_duplicates():
    """Remove duplicate D/ST records, keeping only the latest ones"""
    
    storage = EnhancedProjectionStorage()
    
    # Get current D/ST records
    query = """
    SELECT player_name, COUNT(*) as count
    FROM projections 
    WHERE position = 'DST' AND source = 'Subvertadown' AND week = 1
    GROUP BY player_name
    HAVING count > 1
    ORDER BY count DESC
    """
    
    duplicates = storage.conn.execute(query).df()
    print(f"Found duplicates for {len(duplicates)} teams")
    
    if not duplicates.empty:
        print("Teams with duplicates:")
        for _, row in duplicates.iterrows():
            print(f"  {row['player_name']}: {row['count']} records")
    
    # Delete older duplicate records, keeping only the most recent
    delete_query = """
    DELETE FROM projections 
    WHERE position = 'DST' AND source = 'Subvertadown' AND week = 1
    AND created_at NOT IN (
        SELECT MAX(created_at)
        FROM projections
        WHERE position = 'DST' AND source = 'Subvertadown' AND week = 1
        GROUP BY player_name
    )
    """
    
    result = storage.conn.execute(delete_query)
    deleted_count = result.fetchone()[0] if hasattr(result, 'fetchone') else 0
    
    # Check final count
    final_count = storage.conn.execute(
        "SELECT COUNT(*) FROM projections WHERE position = 'DST' AND source = 'Subvertadown' AND week = 1"
    ).fetchone()[0]
    
    print(f"✅ Cleaned up duplicates. Final D/ST count: {final_count}")
    
    return final_count

if __name__ == "__main__":
    clean_dst_duplicates()