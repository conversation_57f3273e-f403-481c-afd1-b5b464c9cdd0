#!/usr/bin/env python3
"""
Extract all kicker data from Subvertadown browser snapshot and store in DuckDB
"""

from src.enhanced_projection_storage import EnhancedProjectionStorage, StrategicProjectionRecord

def extract_kicker_data_from_browser():
    """Extract all kicker data from the browser snapshot"""
    
    # Based on the browser snapshot, here's ALL the kicker data (32 kickers total)
    kicker_projections = [
        {
            "player": "Spencer Shrader",
            "matchup": "IND vs. MIA",
            "wk1": 9.0,
            "wk2": 8.6,
            "hold": False,  # dash
            "error_rate": 3.7,
            "fg_attempts": 1.8,
            "fg_0_29": 0.5,
            "fg_30_39": 0.5,
            "fg_40_49": 0.5,
            "fg_50_plus": 0.4,
            "xp": 2.2,
            "implied_score": 24.5
        },
        {
            "player": "<PERSON>",
            "matchup": "ARI @ NO",
            "wk1": 8.8,
            "wk2": 8.7,
            "hold": True,  # Y
            "error_rate": 4.0,
            "fg_attempts": 1.9,
            "fg_0_29": 0.5,
            "fg_30_39": 0.5,
            "fg_40_49": 0.5,
            "fg_50_plus": 0.3,
            "xp": 2.4,
            "implied_score": 24.7
        },
        {
            "player": "<PERSON> Butker",
            "matchup": "KC @ LAC",
            "wk1": 8.7,
            "wk2": 8.3,
            "hold": True,  # Y
            "error_rate": 3.5,
            "fg_attempts": 1.7,
            "fg_0_29": 0.5,
            "fg_30_39": 0.5,
            "fg_40_49": 0.5,
            "fg_50_plus": 0.3,
            "xp": 2.5,
            "implied_score": 24.3
        },
        {
            "player": "Tyler Bass",
            "matchup": "BUF vs. BAL",
            "wk1": 8.6,
            "wk2": 8.4,
            "hold": True,  # Y
            "error_rate": 3.0,
            "fg_attempts": 1.6,
            "fg_0_29": 0.5,
            "fg_30_39": 0.5,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.2,
            "xp": 2.5,
            "implied_score": 26.1
        },
        {
            "player": "Jake Moody",
            "matchup": "SF @ SEA",
            "wk1": 8.5,
            "wk2": 9.0,
            "hold": True,  # Y
            "error_rate": 3.4,
            "fg_attempts": 1.8,
            "fg_0_29": 0.5,
            "fg_30_39": 0.5,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.3,
            "xp": 2.2,
            "implied_score": 23.3
        },
        {
            "player": "Younghoe Koo",
            "matchup": "ATL vs. TB",
            "wk1": 8.5,
            "wk2": 8.5,
            "hold": True,  # Y
            "error_rate": 3.8,
            "fg_attempts": 1.7,
            "fg_0_29": 0.4,
            "fg_30_39": 0.4,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.4,
            "xp": 2.1,
            "implied_score": 22.7
        },
        {
            "player": "Matt Gay",
            "matchup": "WAS vs. NYG",
            "wk1": 8.5,
            "wk2": 8.1,
            "hold": False,  # dash
            "error_rate": 3.4,
            "fg_attempts": 1.8,
            "fg_0_29": 0.6,
            "fg_30_39": 0.5,
            "fg_40_49": 0.5,
            "fg_50_plus": 0.2,
            "xp": 2.5,
            "implied_score": 25.9
        },
        {
            "player": "Evan McPherson",
            "matchup": "CIN @ CLE",
            "wk1": 8.4,
            "wk2": 8.1,
            "hold": False,  # dash
            "error_rate": 3.2,
            "fg_attempts": 1.7,
            "fg_0_29": 0.6,
            "fg_30_39": 0.5,
            "fg_40_49": 0.5,
            "fg_50_plus": 0.2,
            "xp": 2.7,
            "implied_score": 26.8
        },
        {
            "player": "Chase McLaughlin",
            "matchup": "TB @ ATL",
            "wk1": 8.4,
            "wk2": 8.6,
            "hold": False,  # dash
            "error_rate": 3.8,
            "fg_attempts": 1.6,
            "fg_0_29": 0.4,
            "fg_30_39": 0.4,
            "fg_40_49": 0.5,
            "fg_50_plus": 0.3,
            "xp": 2.4,
            "implied_score": 24.3
        },
        {
            "player": "Cam Little",
            "matchup": "JAC vs. CAR",
            "wk1": 8.4,
            "wk2": 7.9,
            "hold": False,  # dash
            "error_rate": 3.7,
            "fg_attempts": 1.7,
            "fg_0_29": 0.5,
            "fg_30_39": 0.5,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.2,
            "xp": 2.4,
            "implied_score": 24.9
        },
        {
            "player": "Dolphins Kicker",
            "matchup": "MIA @ IND",
            "wk1": 8.3,
            "wk2": 7.8,
            "hold": False,  # dash
            "error_rate": 3.7,
            "fg_attempts": 1.7,
            "fg_0_29": 0.4,
            "fg_30_39": 0.5,
            "fg_40_49": 0.5,
            "fg_50_plus": 0.4,
            "xp": 2.0,
            "implied_score": 22.4
        },
        {
            "player": "Joshua Karty",
            "matchup": "LAR vs. HOU",
            "wk1": 8.3,
            "wk2": 8.2,
            "hold": False,  # dash
            "error_rate": 3.5,
            "fg_attempts": 1.7,
            "fg_0_29": 0.5,
            "fg_30_39": 0.5,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.2,
            "xp": 2.3,
            "implied_score": 23.4
        },
        {
            "player": "Jake Elliott",
            "matchup": "PHI vs. DAL",
            "wk1": 8.2,
            "wk2": 8.5,
            "hold": True,  # Y
            "error_rate": 3.3,
            "fg_attempts": 1.6,
            "fg_0_29": 0.5,
            "fg_30_39": 0.5,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.2,
            "xp": 2.9,
            "implied_score": 26.9
        },
        {
            "player": "Wil Lutz",
            "matchup": "DEN vs. TEN",
            "wk1": 8.2,
            "wk2": 9.0,
            "hold": False,  # dash
            "error_rate": 3.4,
            "fg_attempts": 1.8,
            "fg_0_29": 0.6,
            "fg_30_39": 0.5,
            "fg_40_49": 0.5,
            "fg_50_plus": 0.3,
            "xp": 2.4,
            "implied_score": 25.0
        },
        {
            "player": "Chris Boswell",
            "matchup": "PIT @ NYJ",
            "wk1": 8.1,
            "wk2": 8.5,
            "hold": False,  # dash
            "error_rate": 3.6,
            "fg_attempts": 1.7,
            "fg_0_29": 0.4,
            "fg_30_39": 0.5,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.3,
            "xp": 1.8,
            "implied_score": 20.4
        },
        {
            "player": "Will Reichard",
            "matchup": "MIN @ CHI",
            "wk1": 8.0,
            "wk2": 8.5,
            "hold": True,  # Y
            "error_rate": 3.4,
            "fg_attempts": 1.7,
            "fg_0_29": 0.5,
            "fg_30_39": 0.5,
            "fg_40_49": 0.5,
            "fg_50_plus": 0.2,
            "xp": 2.1,
            "implied_score": 22.3
        },
        {
            "player": "Brandon McManus",
            "matchup": "GB vs. DET",
            "wk1": 8.0,
            "wk2": 8.2,
            "hold": False,  # dash
            "error_rate": 3.1,
            "fg_attempts": 1.4,
            "fg_0_29": 0.4,
            "fg_30_39": 0.4,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.2,
            "xp": 2.6,
            "implied_score": 25.0
        },
        {
            "player": "Tyler Loop",
            "matchup": "BAL @ BUF",
            "wk1": 8.0,
            "wk2": 8.2,
            "hold": False,  # dash
            "error_rate": 3.2,
            "fg_attempts": 1.4,
            "fg_0_29": 0.4,
            "fg_30_39": 0.4,
            "fg_40_49": 0.3,
            "fg_50_plus": 0.2,
            "xp": 2.7,
            "implied_score": 25.4
        },
        {
            "player": "Jake Bates",
            "matchup": "DET @ GB",
            "wk1": 7.9,
            "wk2": 8.4,
            "hold": True,  # Y
            "error_rate": 3.3,
            "fg_attempts": 1.5,
            "fg_0_29": 0.4,
            "fg_30_39": 0.4,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.3,
            "xp": 2.3,
            "implied_score": 23.5
        },
        {
            "player": "Ka'imi Fairbairn",
            "matchup": "HOU @ LAR",
            "wk1": 7.9,
            "wk2": 8.6,
            "hold": False,  # dash
            "error_rate": 3.6,
            "fg_attempts": 1.6,
            "fg_0_29": 0.4,
            "fg_30_39": 0.5,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.3,
            "xp": 1.9,
            "implied_score": 20.7
        },
        {
            "player": "Cameron Dicker",
            "matchup": "LAC @ KC",
            "wk1": 7.9,
            "wk2": 8.5,
            "hold": False,  # dash
            "error_rate": 3.6,
            "fg_attempts": 1.5,
            "fg_0_29": 0.4,
            "fg_30_39": 0.4,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.3,
            "xp": 1.8,
            "implied_score": 20.9
        },
        {
            "player": "Andres Borregales",
            "matchup": "NE vs. LV",
            "wk1": 7.9,
            "wk2": 7.9,
            "hold": False,  # dash
            "error_rate": 3.5,
            "fg_attempts": 1.6,
            "fg_0_29": 0.4,
            "fg_30_39": 0.5,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.3,
            "xp": 2.1,
            "implied_score": 22.6
        },
        {
            "player": "Cairo Santos",
            "matchup": "CHI vs. MIN",
            "wk1": 7.9,
            "wk2": 8.6,
            "hold": False,  # dash
            "error_rate": 3.2,
            "fg_attempts": 1.4,
            "fg_0_29": 0.4,
            "fg_30_39": 0.5,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.3,
            "xp": 1.9,
            "implied_score": 20.6
        },
        {
            "player": "Blake Grupe",
            "matchup": "NO vs. ARI",
            "wk1": 7.8,
            "wk2": 8.1,
            "hold": False,  # dash
            "error_rate": 4.2,
            "fg_attempts": 1.6,
            "fg_0_29": 0.3,
            "fg_30_39": 0.5,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.4,
            "xp": 1.5,
            "implied_score": 17.9
        },
        {
            "player": "Daniel Carlson",
            "matchup": "LV @ NE",
            "wk1": 7.8,
            "wk2": 8.8,
            "hold": False,  # dash
            "error_rate": 3.7,
            "fg_attempts": 1.6,
            "fg_0_29": 0.4,
            "fg_30_39": 0.5,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.3,
            "xp": 1.8,
            "implied_score": 19.8
        },
        {
            "player": "Nick Folk",
            "matchup": "NYJ vs. PIT",
            "wk1": 7.8,
            "wk2": 7.9,
            "hold": False,  # dash
            "error_rate": 3.4,
            "fg_attempts": 1.6,
            "fg_0_29": 0.4,
            "fg_30_39": 0.5,
            "fg_40_49": 0.5,
            "fg_50_plus": 0.3,
            "xp": 1.5,
            "implied_score": 17.9
        },
        {
            "player": "Ryan Fitzgerald",
            "matchup": "CAR @ JAC",
            "wk1": 7.6,
            "wk2": 8.3,
            "hold": False,  # dash
            "error_rate": 3.9,
            "fg_attempts": 1.5,
            "fg_0_29": 0.3,
            "fg_30_39": 0.5,
            "fg_40_49": 0.5,
            "fg_50_plus": 0.3,
            "xp": 1.9,
            "implied_score": 21.5
        },
        {
            "player": "Andre Szmyt",
            "matchup": "CLE vs. CIN",
            "wk1": 7.5,
            "wk2": 7.6,
            "hold": False,  # dash
            "error_rate": 3.4,
            "fg_attempts": 1.5,
            "fg_0_29": 0.4,
            "fg_30_39": 0.5,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.3,
            "xp": 1.8,
            "implied_score": 20.5
        },
        {
            "player": "Brandon Aubrey",
            "matchup": "DAL @ PHI",
            "wk1": 7.5,
            "wk2": 9.0,
            "hold": False,  # dash
            "error_rate": 3.6,
            "fg_attempts": 1.4,
            "fg_0_29": 0.3,
            "fg_30_39": 0.4,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.3,
            "xp": 1.7,
            "implied_score": 19.5
        },
        {
            "player": "Jason Myers",
            "matchup": "SEA vs. SF",
            "wk1": 7.5,
            "wk2": 7.6,
            "hold": False,  # dash
            "error_rate": 3.3,
            "fg_attempts": 1.5,
            "fg_0_29": 0.3,
            "fg_30_39": 0.4,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.3,
            "xp": 1.9,
            "implied_score": 20.9
        },
        {
            "player": "Graham Gano",
            "matchup": "NYG @ WAS",
            "wk1": 7.4,
            "wk2": 8.3,
            "hold": False,  # dash
            "error_rate": 3.6,
            "fg_attempts": 1.5,
            "fg_0_29": 0.3,
            "fg_30_39": 0.4,
            "fg_40_49": 0.4,
            "fg_50_plus": 0.3,
            "xp": 1.7,
            "implied_score": 19.6
        },
        {
            "player": "Joey Slye",
            "matchup": "TEN @ DEN",
            "wk1": 7.0,
            "wk2": 7.8,
            "hold": False,  # dash
            "error_rate": 3.6,
            "fg_attempts": 1.6,
            "fg_0_29": 0.4,
            "fg_30_39": 0.5,
            "fg_40_49": 0.5,
            "fg_50_plus": 0.3,
            "xp": 1.3,
            "implied_score": 16.2
        }
    ]
    
    return kicker_projections

def convert_to_kicker_records(data_list, week: int = 1):
    """Convert extracted kicker data to StrategicProjectionRecord objects"""
    
    records = []
    
    for item in data_list:
        # Extract team from matchup
        team = item['matchup'].split()[0]
        
        raw_data = {
            'matchup': item['matchup'],
            'hold_recommended': item['hold'],
            'error_rate': item['error_rate'],
            'fg_attempts': item['fg_attempts'],
            'fg_0_29': item['fg_0_29'],
            'fg_30_39': item['fg_30_39'],
            'fg_40_49': item['fg_40_49'],
            'fg_50_plus': item['fg_50_plus'],
            'extra_points': item['xp'],
            'implied_team_score': item['implied_score'],
            'week_2_projection': item.get('wk2')
        }
        
        record = StrategicProjectionRecord(
            week=week,
            year=2025,
            player_id=None,
            player_name=item['player'],
            position='K',
            team=team,
            source='Subvertadown',
            projected_points=item['wk1'],
            raw_source_data=raw_data
        )
        records.append(record)
    
    return records

def main():
    """Extract kicker data from browser and store in enhanced projection storage"""
    
    print("🤖 Extracting kicker data from browser snapshot...")
    
    # Extract the data
    kicker_data = extract_kicker_data_from_browser()
    
    print(f"📊 Extracted {len(kicker_data)} kickers")
    
    # Convert to projection records
    records = convert_to_kicker_records(kicker_data, week=1)
    
    # Store in DuckDB
    storage = EnhancedProjectionStorage()
    count = storage._store_projections(records)
    
    print(f"✅ Imported {count} kicker projections to enhanced projection storage")
    
    # Show top 5 projections
    if records:
        top_5 = sorted(records, key=lambda r: r.projected_points, reverse=True)[:5]
        print(f"\n🔥 Top 5 kicker streaming targets:")
        for i, r in enumerate(top_5, 1):
            hold_text = " [HOLD]" if r.raw_source_data.get('hold_recommended') else ""
            matchup = r.raw_source_data.get('matchup', '')
            implied_score = r.raw_source_data.get('implied_team_score', '')
            print(f"  {i}. {r.player_name} {matchup}: {r.projected_points} (Team: {implied_score}){hold_text}")
    
    # Show hold candidates
    hold_candidates = [r for r in records if r.raw_source_data.get('hold_recommended')]
    print(f"\n💎 HOLD candidates ({len(hold_candidates)}):")
    for r in sorted(hold_candidates, key=lambda r: r.projected_points, reverse=True):
        implied_score = r.raw_source_data.get('implied_team_score', '')
        print(f"  {r.player_name}: {r.projected_points} (Team: {implied_score})")
    
    print(f"\n📈 Kicker data now available in enhanced projection storage!")

if __name__ == "__main__":
    main()