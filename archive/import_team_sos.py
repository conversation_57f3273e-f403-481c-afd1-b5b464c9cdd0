#!/usr/bin/env python3
"""
Import team strength of schedule data from clipboard (Subvertadown format)
"""

import sys
import pyperclip
from src.enhanced_projection_storage import EnhancedProjectionStorage

def get_clipboard_data():
    """Get data from clipboard"""
    try:
        return pyperclip.paste()
    except Exception as e:
        print(f"Error reading clipboard: {e}")
        return None

def parse_sos_data(clipboard_text, position):
    """Parse team SOS data from clipboard"""
    lines = clipboard_text.strip().split('\n')
    
    # Skip header lines until we find team data
    data_start = 0
    for i, line in enumerate(lines):
        stripped = line.strip()
        if stripped in ['Falcons', 'Colts', 'Dolphins', 'Cardinals', 'Browns', 'Buccaneers', 'Bills', 'Saints', 'Patriots', 'Seahawks', 'Bears', 'Eagles', 'Jaguars', 'Broncos', 'Giants', 'Cowboys', 'Bengals', 'Rams', 'Packers', 'Chiefs', 'Chargers', 'Panthers', 'Commanders', 'Titans', 'Texans', 'Ravens', 'Vikings', 'Steelers', 'Lions', 'Raiders', '49ers', 'Jets']:
            data_start = i
            break
    
    print(f"Debug: Found data start at line {data_start}: '{lines[data_start] if data_start < len(lines) else 'NOT FOUND'}'")
    
    extracted_data = []
    i = data_start
    
    while i < len(lines):
        if i >= len(lines):
            break
            
        team_line = lines[i].strip()
        if not team_line:
            i += 1
            continue
            
        # Check if this is a team name
        if team_line in ['Falcons', 'Colts', 'Dolphins', 'Cardinals', 'Browns', 'Buccaneers', 'Bills', 'Saints', 'Patriots', 'Seahawks', 'Bears', 'Eagles', 'Jaguars', 'Broncos', 'Giants', 'Cowboys', 'Bengals', 'Rams', 'Packers', 'Chiefs', 'Chargers', 'Panthers', 'Commanders', 'Titans', 'Texans', 'Ravens', 'Vikings', 'Steelers', 'Lions', 'Raiders', '49ers', 'Jets']:
            try:
                team = team_line
                
                # Next line should be baseline
                baseline = float(lines[i + 1].strip()) if i + 1 < len(lines) else 0.0
                
                # Next line should be next_4_avg
                next_4_avg = float(lines[i + 2].strip()) if i + 2 < len(lines) else 0.0
                
                # Next line should be the weekly values (space-separated)
                weekly_line = lines[i + 3].strip() if i + 3 < len(lines) else ""
                weekly_parts = weekly_line.split()
                
                print(f"Debug: Processing {team}: baseline={baseline}, next_4={next_4_avg}, weekly_parts={len(weekly_parts)}")
                
                # Extract weekly values (should be 17 weeks + 2 averages = 19 total)
                weekly_values = []
                for j in range(17):  # weeks 1-17
                    if j < len(weekly_parts):
                        val = weekly_parts[j]
                        # Handle truncated data with "..." or other non-numeric values
                        if val == '-' or val == '...' or not val.replace('.', '').replace('-', '').isdigit():
                            weekly_values.append(None)
                        else:
                            try:
                                weekly_values.append(float(val))
                            except ValueError:
                                weekly_values.append(None)
                    else:
                        weekly_values.append(None)

                # Extract final averages from the weekly line
                def safe_float(val, default=0.0):
                    if val == '-' or val == '...' or not val.replace('.', '').replace('-', '').isdigit():
                        return default
                    try:
                        return float(val)
                    except (ValueError, IndexError):
                        return default

                season_avg = safe_float(weekly_parts[17]) if len(weekly_parts) > 17 else 0.0
                playoffs_avg = safe_float(weekly_parts[18]) if len(weekly_parts) > 18 else 0.0
                
                sos_record = {
                    'team': team,
                    'position': position.upper(),
                    'baseline': baseline,
                    'week_1': weekly_values[0],
                    'week_2': weekly_values[1],
                    'week_3': weekly_values[2],
                    'week_4': weekly_values[3],
                    'week_5': weekly_values[4],
                    'week_6': weekly_values[5],
                    'week_7': weekly_values[6],
                    'week_8': weekly_values[7],
                    'week_9': weekly_values[8],
                    'week_10': weekly_values[9],
                    'week_11': weekly_values[10],
                    'week_12': weekly_values[11],
                    'week_13': weekly_values[12],
                    'week_14': weekly_values[13],
                    'week_15': weekly_values[14],
                    'week_16': weekly_values[15],
                    'week_17': weekly_values[16],
                    'next_4_avg': next_4_avg,
                    'playoffs_avg': playoffs_avg
                }
                
                extracted_data.append(sos_record)
                print(f"Parsed {team} {position.upper()}: baseline={baseline}, week_1={weekly_values[0]}")
                
                # Skip to next team (team + baseline + next_4 + weekly_data = 4 lines)
                i += 4
                
            except Exception as e:
                print(f"Error parsing team {team_line}: {e}")
                i += 1
        else:
            i += 1
    
    return extracted_data

def store_sos_data(sos_records):
    """Store SOS records in team_strength_of_schedule table with historical tracking"""
    storage = EnhancedProjectionStorage()
    position = sos_records[0]['position'] if sos_records else 'UNKNOWN'
    
    # Don't delete - preserve historical data
    # Instead, insert new records with current timestamp
    
    # Insert new records
    for record in sos_records:
        storage.conn.execute("""
            INSERT INTO team_strength_of_schedule (
                team, position, baseline, week_1, week_2, week_3, week_4, week_5, week_6, week_7, week_8, week_9, week_10,
                week_11, week_12, week_13, week_14, week_15, week_16, week_17, next_4_avg, playoffs_avg,
                capture_timestamp, data_source
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, 'Subvertadown')
        """, (
            record['team'], record['position'], record['baseline'],
            record['week_1'], record['week_2'], record['week_3'], record['week_4'], record['week_5'], record['week_6'],
            record['week_7'], record['week_8'], record['week_9'], record['week_10'], record['week_11'], record['week_12'],
            record['week_13'], record['week_14'], record['week_15'], record['week_16'], record['week_17'],
            record['next_4_avg'], record['playoffs_avg']
        ))
    
    storage.conn.commit()
    print(f"✅ Stored {len(sos_records)} {position} team SOS records with timestamp")
    return len(sos_records)

def main():
    if len(sys.argv) < 2:
        print("Usage: python import_team_sos.py <rb|wr|te|qb|dst>")
        sys.exit(1)
    
    position = sys.argv[1].lower()
    
    print(f"🤖 Importing {position.upper()} team strength of schedule data from clipboard...")
    
    # Get clipboard data
    clipboard_text = get_clipboard_data()
    
    if not clipboard_text:
        print("❌ No data found in clipboard")
        sys.exit(1)
    
    print(f"Clipboard preview: {clipboard_text[:100]}...")
    
    # Parse SOS data
    sos_records = parse_sos_data(clipboard_text, position)
    
    if not sos_records:
        print("❌ No SOS data parsed from clipboard")
        sys.exit(1)
    
    print(f"📊 Parsed {len(sos_records)} team records")
    
    # Store in database
    count = store_sos_data(sos_records)
    print(f"✅ Imported {count} {position.upper()} team SOS records")
    
    # Show sample data
    if sos_records:
        print(f"\nSample {position.upper()} SOS data:")
        for record in sos_records[:5]:
            print(f"  {record['team']}: baseline={record['baseline']}, week_1={record['week_1']}")

if __name__ == "__main__":
    main()
