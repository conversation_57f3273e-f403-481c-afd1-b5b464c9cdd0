#!/usr/bin/env python3
"""
Extract Subvertadown D/ST data from browser snapshot and store in DuckDB
"""

from src.enhanced_projection_storage import EnhancedProjectionStorage, StrategicProjectionRecord

def extract_dst_data_from_browser():
    """Extract D/ST data from the browser snapshot we just captured"""
    
    # Based on the browser snapshot, here's ALL the D/ST data (32 teams total)
    dst_projections = [
        {
            "team": "Broncos",
            "matchup": "vs. Titans",
            "wk1": 9.3,
            "wk2": 7.1,
            "wk3": 7.5,
            "hold": True,  # checkmark in Hold column
            "error_rate": 5.0,
            "pa": 16.2,
            "ya": 283,
            "int_pct": 0.8,
            "sacks": 2.9,
            "fr": 0.6,
            "td": 0.20
        },
        {
            "team": "Commanders",
            "matchup": "vs. Giants",
            "wk1": 8.0,
            "wk2": 4.3,
            "wk3": 7.4,
            "hold": True,  # checkmark in Hold column
            "error_rate": 4.6,
            "pa": 19.6,
            "ya": 304,
            "int_pct": 0.8,
            "sacks": 2.8,
            "fr": 0.6,
            "td": 0.17
        },
        {
            "team": "Cardinals",
            "matchup": "@ Saints",
            "wk1": 7.9,
            "wk2": 7.5,
            "wk3": 5.0,
            "hold": False,  # dash in Hold column
            "error_rate": 5.6,
            "pa": 17.9,
            "ya": 295,
            "int_pct": 0.8,
            "sacks": 2.8,
            "fr": 0.6,
            "td": 0.18
        },
        {
            "team": "Steelers",
            "matchup": "@ Jets",
            "wk1": 7.8,
            "wk2": 7.6,
            "wk3": 6.1,
            "hold": False,  # dash in Hold column
            "error_rate": 5.0,
            "pa": 17.9,
            "ya": 294,
            "int_pct": 0.8,
            "sacks": 2.7,
            "fr": 0.6,
            "td": 0.19
        },
        {
            "team": "Bengals",
            "matchup": "@ Browns",
            "wk1": 7.6,
            "wk2": 5.7,
            "wk3": 5.0,
            "hold": False,  # dash in Hold column
            "error_rate": 4.9,
            "pa": 20.5,
            "ya": 319,
            "int_pct": 0.8,
            "sacks": 2.8,
            "fr": 0.6,
            "td": 0.20
        },
        {
            "team": "49ers",
            "matchup": "@ Seahawks",
            "wk1": 7.5,
            "wk2": 7.5,
            "wk3": 6.4,
            "hold": False,  # dash in Hold column
            "error_rate": 5.5,
            "pa": 20.9,
            "ya": 303,
            "int_pct": 0.7,
            "sacks": 2.7,
            "fr": 0.6,
            "td": 0.18
        },
        {
            "team": "Eagles",
            "matchup": "vs. Cowboys",
            "wk1": 7.5,
            "wk2": 7.0,
            "wk3": 6.4,
            "hold": True,  # checkmark in Hold column
            "error_rate": 4.9,
            "pa": 19.5,
            "ya": 313,
            "int_pct": 0.8,
            "sacks": 2.5,
            "fr": 0.6,
            "td": 0.20
        },
        {
            "team": "Jets",
            "matchup": "vs. Steelers",
            "wk1": 7.1,
            "wk2": 3.9,
            "wk3": 3.8,
            "hold": False,  # dash in Hold column
            "error_rate": 4.9,
            "pa": 20.4,
            "ya": 292,
            "int_pct": 0.7,
            "sacks": 2.5,
            "fr": 0.6,
            "td": 0.15
        },
        {
            "team": "Chiefs",
            "matchup": "@ Chargers",
            "wk1": 7.0,
            "wk2": 5.0,
            "wk3": 8.1,
            "hold": True,  # checkmark in Hold column
            "error_rate": 4.7,
            "pa": 20.9,
            "ya": 319,
            "int_pct": 0.7,
            "sacks": 2.9,
            "fr": 0.6,
            "td": 0.17
        },
        {
            "team": "Patriots",
            "matchup": "vs. Raiders",
            "wk1": 6.9,
            "wk2": 7.6,
            "wk3": 7.8,
            "hold": True,  # checkmark in Hold column
            "error_rate": 4.8,
            "pa": 19.8,
            "ya": 312,
            "int_pct": 0.8,
            "sacks": 2.4,
            "fr": 0.6,
            "td": 0.19
        },
        # Continue with all the remaining teams from the snapshot...
        {
            "team": "Vikings",
            "matchup": "@ Bears",
            "wk1": 6.9,
            "wk2": 6.9,
            "wk3": 5.1,
            "hold": False,
            "error_rate": 4.7,
            "pa": 20.6,
            "ya": 324,
            "int_pct": 0.7,
            "sacks": 2.7,
            "fr": 0.6,
            "td": 0.18
        },
        {
            "team": "Rams",
            "matchup": "vs. Texans",
            "wk1": 6.8,
            "wk2": 7.1,
            "wk3": 3.7,
            "hold": False,
            "error_rate": 4.7,
            "pa": 20.7,
            "ya": 320,
            "int_pct": 0.8,
            "sacks": 2.7,
            "fr": 0.6,
            "td": 0.17
        },
        {
            "team": "Jaguars",
            "matchup": "vs. Panthers",
            "wk1": 6.7,
            "wk2": 3.3,
            "wk3": 7.7,
            "hold": False,
            "error_rate": 5.0,
            "pa": 21.5,
            "ya": 328,
            "int_pct": 0.8,
            "sacks": 2.6,
            "fr": 0.6,
            "td": 0.17
        },
        {
            "team": "Colts",
            "matchup": "vs. Dolphins",
            "wk1": 6.1,
            "wk2": 5.2,
            "wk3": 5.6,
            "hold": False,
            "error_rate": 5.1,
            "pa": 22.4,
            "ya": 327,
            "int_pct": 0.7,
            "sacks": 2.5,
            "fr": 0.6,
            "td": 0.19
        },
        {
            "team": "Raiders",
            "matchup": "@ Patriots",
            "wk1": 6.0,
            "wk2": 7.0,
            "wk3": 3.5,
            "hold": False,
            "error_rate": 4.7,
            "pa": 22.6,
            "ya": 330,
            "int_pct": 0.7,
            "sacks": 2.5,
            "fr": 0.6,
            "td": 0.15
        },
        {
            "team": "Bears",
            "matchup": "vs. Vikings",
            "wk1": 5.6,
            "wk2": 4.0,
            "wk3": 6.9,
            "hold": False,
            "error_rate": 4.1,
            "pa": 22.3,
            "ya": 345,
            "int_pct": 0.8,
            "sacks": 2.5,
            "fr": 0.6,
            "td": 0.17
        },
        {
            "team": "Buccaneers",
            "matchup": "@ Falcons",
            "wk1": 5.6,
            "wk2": 5.9,
            "wk3": 8.0,
            "hold": False,
            "error_rate": 5.1,
            "pa": 22.7,
            "ya": 336,
            "int_pct": 0.7,
            "sacks": 2.5,
            "fr": 0.6,
            "td": 0.18
        },
        {
            "team": "Packers",
            "matchup": "vs. Lions",
            "wk1": 5.3,
            "wk2": 5.6,
            "wk3": 8.2,
            "hold": True,  # checkmark
            "error_rate": 4.5,
            "pa": 23.5,
            "ya": 346,
            "int_pct": 0.7,
            "sacks": 2.5,
            "fr": 0.5,
            "td": 0.18
        },
        {
            "team": "Seahawks",
            "matchup": "vs. 49ers",
            "wk1": 5.1,
            "wk2": 5.7,
            "wk3": 8.5,
            "hold": False,
            "error_rate": 4.5,
            "pa": 23.3,
            "ya": 345,
            "int_pct": 0.7,
            "sacks": 2.3,
            "fr": 0.6,
            "td": 0.15
        },
        {
            "team": "Dolphins",
            "matchup": "@ Colts",
            "wk1": 4.8,
            "wk2": 6.5,
            "wk3": 3.7,
            "hold": False,
            "error_rate": 4.9,
            "pa": 24.5,
            "ya": 347,
            "int_pct": 0.8,
            "sacks": 2.5,
            "fr": 0.6,
            "td": 0.15
        },
        {
            "team": "Chargers",
            "matchup": "@ Chiefs",
            "wk1": 4.7,
            "wk2": 4.7,
            "wk3": 5.6,
            "hold": False,
            "error_rate": 4.4,
            "pa": 24.3,
            "ya": 360,
            "int_pct": 0.7,
            "sacks": 2.2,
            "fr": 0.6,
            "td": 0.15
        },
        {
            "team": "Texans",
            "matchup": "@ Rams",
            "wk1": 4.7,
            "wk2": 4.4,
            "wk3": 4.4,
            "hold": False,
            "error_rate": 4.8,
            "pa": 23.4,
            "ya": 350,
            "int_pct": 0.7,
            "sacks": 2.1,
            "fr": 0.6,
            "td": 0.16
        },
        {
            "team": "Lions",
            "matchup": "@ Packers",
            "wk1": 4.6,
            "wk2": 7.0,
            "wk3": 3.3,
            "hold": False,
            "error_rate": 4.6,
            "pa": 25.0,
            "ya": 360,
            "int_pct": 0.7,
            "sacks": 2.3,
            "fr": 0.6,
            "td": 0.18
        },
        {
            "team": "Falcons",
            "matchup": "vs. Buccaneers",
            "wk1": 4.5,
            "wk2": 5.2,
            "wk3": 7.3,
            "hold": False,
            "error_rate": 5.0,
            "pa": 24.3,
            "ya": 357,
            "int_pct": 0.7,
            "sacks": 2.4,
            "fr": 0.6,
            "td": 0.16
        },
        {
            "team": "Titans",
            "matchup": "@ Broncos",
            "wk1": 4.5,
            "wk2": 4.0,
            "wk3": 6.9,
            "hold": False,
            "error_rate": 4.5,
            "pa": 25.0,
            "ya": 347,
            "int_pct": 0.6,
            "sacks": 2.0,
            "fr": 0.6,
            "td": 0.15
        },
        {
            "team": "Bills",
            "matchup": "vs. Ravens",
            "wk1": 4.2,
            "wk2": 7.2,
            "wk3": 8.4,
            "hold": True,  # checkmark
            "error_rate": 4.4,
            "pa": 25.4,
            "ya": 369,
            "int_pct": 0.7,
            "sacks": 2.5,
            "fr": 0.6,
            "td": 0.16
        },
        {
            "team": "Giants",
            "matchup": "@ Commanders",
            "wk1": 4.2,
            "wk2": 5.3,
            "wk3": 5.6,
            "hold": False,
            "error_rate": 4.6,
            "pa": 25.9,
            "ya": 369,
            "int_pct": 0.6,
            "sacks": 2.2,
            "fr": 0.6,
            "td": 0.17
        },
        {
            "team": "Cowboys",
            "matchup": "@ Eagles",
            "wk1": 4.1,
            "wk2": 7.8,
            "wk3": 5.3,
            "hold": False,
            "error_rate": 4.4,
            "pa": 26.9,
            "ya": 376,
            "int_pct": 0.6,
            "sacks": 2.4,
            "fr": 0.6,
            "td": 0.17
        },
        {
            "team": "Panthers",
            "matchup": "@ Jaguars",
            "wk1": 4.0,
            "wk2": 4.4,
            "wk3": 5.4,
            "hold": False,
            "error_rate": 4.9,
            "pa": 24.9,
            "ya": 361,
            "int_pct": 0.7,
            "sacks": 1.9,
            "fr": 0.6,
            "td": 0.15
        },
        {
            "team": "Browns",
            "matchup": "vs. Bengals",
            "wk1": 3.9,
            "wk2": 3.7,
            "wk3": 4.6,
            "hold": False,
            "error_rate": 4.4,
            "pa": 26.8,
            "ya": 375,
            "int_pct": 0.6,
            "sacks": 2.0,
            "fr": 0.6,
            "td": 0.16
        },
        {
            "team": "Saints",
            "matchup": "vs. Cardinals",
            "wk1": 3.8,
            "wk2": 3.7,
            "wk3": 4.7,
            "hold": False,
            "error_rate": 4.6,
            "pa": 24.7,
            "ya": 361,
            "int_pct": 0.6,
            "sacks": 2.1,
            "fr": 0.6,
            "td": 0.16
        },
        {
            "team": "Ravens",
            "matchup": "@ Bills",
            "wk1": 3.5,
            "wk2": 8.5,
            "wk3": 5.7,
            "hold": True,  # checkmark seen
            "error_rate": 4.2,
            "pa": 26.1,
            "ya": 374,
            "int_pct": 0.7,
            "sacks": 2.3,
            "fr": 0.6,
            "td": 0.16
        }
    ]
    
    return dst_projections

def convert_to_projection_records(data_list, week: int = 1):
    """Convert extracted data to StrategicProjectionRecord objects"""
    
    records = []
    
    for item in data_list:
        raw_data = {
            'matchup': item['matchup'],
            'hold_recommended': item['hold'],
            'error_rate': item['error_rate'],
            'points_against': item['pa'],
            'yards_against': item['ya'],
            'interception_rate': item['int_pct'],
            'sacks': item['sacks'],
            'fumble_recoveries': item['fr'],
            'defensive_tds': item['td'],
            'week_2_projection': item.get('wk2'),
            'week_3_projection': item.get('wk3')
        }
        
        record = StrategicProjectionRecord(
            week=week,
            year=2025,
            player_id=None,
            player_name=item['team'],
            position='DST',
            team=item['team'],
            source='Subvertadown',
            projected_points=item['wk1'],
            raw_source_data=raw_data
        )
        records.append(record)
    
    return records

def main():
    """Extract D/ST data from browser and store in enhanced projection storage"""
    
    print("🤖 Extracting D/ST data from browser snapshot...")
    
    # Extract the data
    dst_data = extract_dst_data_from_browser()
    
    print(f"📊 Extracted {len(dst_data)} D/ST teams")
    
    # Convert to projection records
    records = convert_to_projection_records(dst_data, week=1)
    
    # Store in DuckDB
    storage = EnhancedProjectionStorage()
    count = storage._store_projections(records)
    
    print(f"✅ Imported {count} DST projections to enhanced projection storage")
    
    # Show top 5 projections
    if records:
        top_5 = sorted(records, key=lambda r: r.projected_points, reverse=True)[:5]
        print(f"\n🔥 Top 5 D/ST streaming targets:")
        for i, r in enumerate(top_5, 1):
            hold_text = " [HOLD]" if r.raw_source_data.get('hold_recommended') else ""
            matchup = r.raw_source_data.get('matchup', '')
            print(f"  {i}. {r.player_name} {matchup}: {r.projected_points}{hold_text}")
    
    print(f"\n📈 D/ST data now available in enhanced projection storage for strategic analysis!")

if __name__ == "__main__":
    main()