import pandas as pd
from espn_api.football import League
from .projection_matcher import Projection<PERSON>atch<PERSON>
from .winwithodds_trends import WinWithOddsTrends
from .player_data_reader import PlayerDataReader
from .strategic_analyzer import StrategicAnalyzer

class WaiverAnalyzer:
    """Analyzes the waiver wire to find top players."""

    def __init__(self, league: League, player_data_reader: PlayerDataReader):
        """Initialize the Waiver Analyzer."""
        self.league = league
        self.projection_matcher = ProjectionMatcher()
        self.trends_analyzer = WinWithOddsTrends()
        self.player_data_reader = player_data_reader
        self.strategic_analyzer = StrategicAnalyzer()

    def get_waiver_wire_analysis(self, my_team, week: int = None, size: int = 100) -> pd.DataFrame:
        """
        Comprehensive waiver wire analysis with projections and recommendations.
        """
        if week is None:
            week = self.league.current_week

        print(f"Analyzing waiver wire for week {week}...")

        free_agents, roster = self._fetch_players_for_waiver_analysis(my_team, size)
        df = self._build_waiver_analysis_df(free_agents, roster, week)

        if df.empty:
            print("No player data available for waiver analysis.")
            return pd.DataFrame()

        # Clean and sort data
        df = df.fillna(0)
        df = df[df['projected_points'] > 0]
        df = df.sort_values('projected_points', ascending=False)

        # Integrate external projections
        df = self.projection_matcher.integrate_projections(df)
        
        # Add season-long context to all players (free agents and rostered)
        df = self.player_data_reader.add_season_context_to_df(df)
        
        # Add trend and DFS value insights
        df = self._add_trend_insights(df)
        
        # Get user's roster with full context for comparison
        my_team_roster_df = self.player_data_reader.get_roster_data(my_team, week)
        
        df = self._add_enhanced_waiver_recommendations(df, my_team_roster_df)
        
        # Apply strategic analysis for enhanced recommendations
        print("Applying strategic analysis...")
        df = self.strategic_analyzer.enhance_waiver_recommendations(df, my_team_roster_df)

        return df

    def _fetch_players_for_waiver_analysis(self, my_team, size: int) -> tuple:
        """Fetches free agents and roster players for waiver analysis."""
        print(f"Fetching players...")
        free_agents = self.league.free_agents(size=size)
        roster = my_team.roster if my_team else []
        return free_agents, roster

    def _build_waiver_analysis_df(self, free_agents: list, roster: list, week: int) -> pd.DataFrame:
        """Builds a DataFrame of players for waiver analysis."""
        all_players_data = []
        
        # Process free agents (available players)
        for player in free_agents:
            try:
                is_on_team = False
                lineup_slot = 'Available'
                projected_points = float(getattr(player, 'projected_total_points', 0))
                if hasattr(player, 'stats') and week in player.stats:
                    weekly_proj = player.stats[week].get('projected_points')
                    if weekly_proj is not None:
                        projected_points = float(weekly_proj)

                all_players_data.append({
                    'name': player.name,
                    'playerId': player.playerId,
                    'position': self._determine_position(player),
                    'proTeam': getattr(player, 'proTeam', 'FA'),
                    'pro_opponent': getattr(player, 'pro_opponent', ''),
                    'projected_points': projected_points,
                    'posRank': getattr(player, 'posRank', 0),
                    'percent_owned': getattr(player, 'percent_owned', 0),
                    'percent_started': getattr(player, 'percent_started', 0),
                    'status': 'Available',
                    'on_team': is_on_team
                })
            except Exception as e:
                print(f"Error processing free agent {getattr(player, 'name', 'Unknown')}: {e}")
        
        # Process roster players (on team)
        for player in roster:
            try:
                is_on_team = True
                lineup_slot = getattr(player, 'lineupSlot', 'BE')
                projected_points = float(getattr(player, 'projected_total_points', 0))
                if hasattr(player, 'stats') and week in player.stats:
                    weekly_proj = player.stats[week].get('projected_points')
                    if weekly_proj is not None:
                        projected_points = float(weekly_proj)

                all_players_data.append({
                    'name': player.name,
                    'playerId': player.playerId,
                    'position': self._determine_position(player),
                    'proTeam': getattr(player, 'proTeam', 'FA'),
                    'pro_opponent': getattr(player, 'pro_opponent', ''),
                    'projected_points': projected_points,
                    'posRank': getattr(player, 'posRank', 0),
                    'percent_owned': getattr(player, 'percent_owned', 0),
                    'percent_started': getattr(player, 'percent_started', 0),
                    'status': f'Roster ({lineup_slot})',
                    'on_team': is_on_team
                })
            except Exception as e:
                print(f"Error processing roster player {getattr(player, 'name', 'Unknown')}: {e}")
        
        return pd.DataFrame(all_players_data)

    def _add_trend_insights(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add trend and DFS value insights to waiver analysis."""
        print("Adding trend and value insights...")
        
        # Get trend and DFS data
        buy_low_targets, sell_high_targets = self.trends_analyzer.get_trade_targets()
        undervalued_players = self.trends_analyzer.get_undervalued_players()
        
        # Initialize new columns
        df['trend_category'] = 'neutral'
        df['trend_pct'] = 0.0
        df['dfs_value'] = 0.0
        df['value_tier'] = 'average'
        
        # Add trend data for available players
        if not buy_low_targets.empty:
            for _, trend_player in buy_low_targets.iterrows():
                matching_players = df[df['name'].str.contains(trend_player['name'], case=False, na=False)]
                for idx in matching_players.index:
                    df.at[idx, 'trend_category'] = 'buy_low'
                    df.at[idx, 'trend_pct'] = trend_player['trend_pct']
        
        if not sell_high_targets.empty:
            for _, trend_player in sell_high_targets.iterrows():
                matching_players = df[df['name'].str.contains(trend_player['name'], case=False, na=False)]
                for idx in matching_players.index:
                    df.at[idx, 'trend_category'] = 'sell_high'
                    df.at[idx, 'trend_pct'] = trend_player['trend_pct']
        
        # Add DFS value data
        if not undervalued_players.empty:
            for _, value_player in undervalued_players.iterrows():
                matching_players = df[df['name'].str.contains(value_player['name'], case=False, na=False)]
                for idx in matching_players.index:
                    df.at[idx, 'dfs_value'] = value_player['value']
                    if value_player['value'] >= 2.5:
                        df.at[idx, 'value_tier'] = 'elite_value'
                    elif value_player['value'] >= 2.3:
                        df.at[idx, 'value_tier'] = 'good_value'
        
        return df
    
    def _add_enhanced_waiver_recommendations(self, df: pd.DataFrame, my_team_roster_df: pd.DataFrame) -> pd.DataFrame:
        """Adds enhanced recommendation column with trend and value insights."""
        # Separate free agents and rostered players
        free_agents_df = df[df['on_team'] == False].copy()
        rostered_players_df = df[df['on_team'] == True].copy()

        # Apply recommendations to free agents
        if not free_agents_df.empty:
            free_agents_df['recommendation'] = free_agents_df.apply(
                lambda row: self._get_enhanced_waiver_recommendation(row, my_team_roster_df), axis=1
            )
        
        # Apply recommendations to rostered players (simplified for now, can be enhanced later)
        if not rostered_players_df.empty:
            rostered_players_df['recommendation'] = rostered_players_df.apply(
                lambda row: self._get_rostered_player_recommendation(row), axis=1
            )

        return pd.concat([free_agents_df, rostered_players_df]).sort_values('projected_points', ascending=False)

    def _get_rostered_player_recommendation(self, row) -> str:
        """
        Generates recommendations for players already on the user's roster.
        This is a simplified version, can be expanded.
        """
        if row.get('trend_category') == 'sell_high':
            return f'TRADE TARGET - Sell high ({row.get("trend_pct", 0):.1f}% overperforming)'
        elif row.get('trend_category') == 'buy_low':
            return f'HOLD TIGHT - Buy low opportunity ({abs(row.get("trend_pct", 0)):.1f}% underperforming)'
        elif row['status'] == 'Roster (BE)':
            return 'HOLD - On Bench'
        else:
            return 'HOLD - Starting'

    def _determine_position(self, player) -> str:
        """Determine primary position from eligible slots"""
        try:
            eligible_slots = getattr(player, 'eligibleSlots', [])
            position_map = {
                0: 'QB', 1: 'TQB', 2: 'RB', 3: 'RB/WR', 4: 'WR', 5: 'WR/TE',
                6: 'TE', 16: 'D/ST', 17: 'K', 20: 'BENCH', 21: 'IR', 23: 'FLEX'
            }

            position_priority = ['QB', 'RB', 'WR', 'TE', 'K', 'D/ST']

            positions = []
            for slot in eligible_slots:
                if slot in position_map:
                    pos = position_map[slot]
                    if pos in position_priority:
                        positions.append(pos)

            for priority_pos in position_priority:
                if priority_pos in positions:
                    return priority_pos

            return getattr(player, 'position', 'UNKNOWN')

        except Exception:
            return getattr(player, 'position', 'UNKNOWN')

    def _get_enhanced_waiver_recommendation(self, free_agent_row, my_team_roster_df: pd.DataFrame) -> str:
        """
        Get enhanced waiver wire recommendation for a free agent, comparing against rostered players.
        """
        best_proj = float(free_agent_row.get('external_proj', free_agent_row.get('projected_points', 0)))
        season_proj = float(free_agent_row.get('season_projection', 0))
        season_rank = int(free_agent_row.get('season_rank', 999))
        ownership = free_agent_row['percent_owned']
        trend_cat = free_agent_row.get('trend_category', 'neutral')
        value_tier = free_agent_row.get('value_tier', 'average')
        dfs_value = free_agent_row.get('dfs_value', 0)
        
        # Check for direct upgrades over rostered players
        for _, rostered_player in my_team_roster_df.iterrows():
            # Only compare if positions are compatible (same position or FLEX-eligible)
            if free_agent_row['position'] == rostered_player['position'] or \
               (free_agent_row['position'] in ['RB', 'WR', 'TE'] and rostered_player['position'] in ['RB', 'WR', 'TE']):
                
                rostered_best_proj = float(rostered_player.get('external_proj', rostered_player.get('projected_points', 0)))
                rostered_season_proj = float(rostered_player.get('season_projection', 0))
                rostered_season_rank = int(rostered_player.get('season_rank', 999))

                # Significant weekly projection upgrade
                if best_proj > rostered_best_proj + 3: # 3 point weekly upgrade threshold
                    return f'ADD {free_agent_row["name"]} (Drop {rostered_player["name"]}) - Weekly Upgrade'
                
                # Significant season-long projection upgrade
                if season_proj > rostered_season_proj + 20: # 20 point season upgrade threshold
                    return f'ADD {free_agent_row["name"]} (Drop {rostered_player["name"]}) - Season Upgrade'

                # Talent upgrade (better season rank)
                if season_rank < rostered_season_rank and season_rank < 50: # Top 50 talent upgrade
                    return f'ADD {free_agent_row["name"]} (Drop {rostered_player["name"]}) - Talent Upgrade'

        # Fallback to existing priority-based recommendation for free agents
        priority_score = self._calculate_waiver_priority(best_proj, ownership, trend_cat, value_tier, dfs_value)
        
        if priority_score >= 90:
            context = self._get_priority_context(trend_cat, value_tier, dfs_value)
            return f'🔥 MUST ADD - Elite target {context}'
        elif priority_score >= 75:
            context = self._get_priority_context(trend_cat, value_tier, dfs_value)
            return f'HIGH PRIORITY - Add immediately {context}'
        elif priority_score >= 60:
            context = self._get_priority_context(trend_cat, value_tier, dfs_value)
            return f'MEDIUM PRIORITY - Good pickup {context}'
        elif priority_score >= 40:
            if trend_cat == 'buy_low':
                return f'TRADE TARGET - Underperforming stud ({abs(free_agent_row.get("trend_pct", 0)):.1f}% below expectations)'
            else:
                return 'LOW PRIORITY - Depth option'
        else:
            if trend_cat == 'sell_high':
                return f'AVOID - Regression candidate ({free_agent_row.get("trend_pct", 0):.1f}% overperforming)'
            else:
                return 'AVOID - Low projection'
    
    def _calculate_waiver_priority(self, projection: float, ownership: float, trend_cat: str, value_tier: str, dfs_value: float) -> int:
        """Calculate waiver priority score (0-100)"""
        score = 0
        
        # Base projection score (0-40 points)
        if projection >= 15:
            score += 40
        elif projection >= 12:
            score += 30
        elif projection >= 8:
            score += 20
        elif projection >= 5:
            score += 10
        
        # Ownership bonus (0-25 points) - lower ownership = higher score
        if ownership < 10:
            score += 25
        elif ownership < 25:
            score += 20
        elif ownership < 50:
            score += 15
        elif ownership < 75:
            score += 10
        else:
            score += 5
        
        # Trend bonus/penalty (0-20 points)
        if trend_cat == 'buy_low':
            score += 20  # Major bonus for buy-low targets
        elif trend_cat == 'sell_high':
            score -= 10  # Penalty for regression candidates
        
        # Value tier bonus (0-15 points)
        if value_tier == 'elite_value':
            score += 15
        elif value_tier == 'good_value':
            score += 10
        
        # DFS value bonus
        if dfs_value >= 2.5:
            score += 10
        elif dfs_value >= 2.3:
            score += 5
        
        return min(100, max(0, score))
    
    def _get_priority_context(self, trend_cat: str, value_tier: str, dfs_value: float) -> str:
        """Get contextual message for priority recommendations"""
        context_parts = []
        
        if trend_cat == 'buy_low':
            context_parts.append('(Buy-low stud)')
        elif value_tier == 'elite_value':
            context_parts.append('(Elite efficiency)')
        elif value_tier == 'good_value':
            context_parts.append('(High value)')
        elif dfs_value >= 2.3:
            context_parts.append(f'({dfs_value:.2f} pts/$1k)')
        
        return ' '.join(context_parts) if context_parts else ''
