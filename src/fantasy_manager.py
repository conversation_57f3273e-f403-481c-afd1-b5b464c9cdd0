"""
Fantasy Football Manager - ESPN League Automation (Refactored)

This module provides tools to automate common fantasy football tasks
by extending the espn-api library with additional capabilities.
Clean, modular architecture with focused responsibilities.
"""

import json
import os
import requests
import pandas as pd
from typing import Dict, List, Optional, Any
from espn_api.football import League
from dataclasses import dataclass
from dotenv import load_dotenv
from .waiver_analyzer import WaiverAnalyzer
from .html_generator import HtmlGenerator
from .projection_matcher import ProjectionMatcher
from .start_sit_analyzer import StartSitAnalyzer
from .player_data_reader import PlayerDataReader
from .enhanced_projection_storage import EnhancedProjectionStorage
import time
# DEPRECATED: SMTP imports removed - use webhook system instead
# import smtplib
# from email.mime.text import MIMEText
# from email.mime.multipart import MI<PERSON>Multipart
from datetime import datetime

# Load environment variables
load_dotenv()

@dataclass
class PlayerMove:
    """Data class for player moves"""
    add_player_id: int
    drop_player_id: int
    move_type: str = "ADD"

class FantasyManager:
    """Main class for managing fantasy football teams - Clean Architecture"""

    def __init__(self, league_id: int = None, year: int = None, espn_s2: str = None, swid: str = None):
        """
        Initialize the Fantasy Manager

        Args:
            league_id: ESPN league ID (defaults to LEAGUE_ID env var)
            year: Fantasy season year (defaults to YEAR env var)
            espn_s2: ESPN session cookie (defaults to ESPN_S2 env var, required for private leagues)
            swid: ESPN SWID cookie (defaults to SWID env var, required for private leagues)
        """
        self.league_id = league_id or int(os.getenv('LEAGUE_ID', 0))
        self.year = year or int(os.getenv('YEAR', 2024))
        self.espn_s2 = espn_s2 or os.getenv('ESPN_S2')
        self.swid = swid or os.getenv('SWID')

        if not self.league_id:
            raise ValueError("League ID must be provided either as parameter or LEAGUE_ID environment variable")
        if not self.year:
            raise ValueError("Year must be provided either as parameter or YEAR environment variable")

        # Initialize ESPN API
        self.league = League(
            league_id=self.league_id,
            year=self.year,
            espn_s2=self.espn_s2,
            swid=self.swid,
            debug=False
        )

        # Initialize specialized components
        self.player_data_reader = PlayerDataReader(self.league, os.getenv('ODDS_API_KEY'))
        self.waiver_analyzer = WaiverAnalyzer(self.league, self.player_data_reader)
        self.html_generator = HtmlGenerator()
        self.start_sit_analyzer = StartSitAnalyzer(self.league, self.player_data_reader)
        self.storage = EnhancedProjectionStorage()

        # Base URL for ESPN API requests
        self.base_url = f"https://fantasy.espn.com/apis/v3/games/ffl/seasons/{self.year}/segments/0/leagues/{self.league_id}"

        # Headers for authenticated requests
        self.headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        }

        # Cookies for authentication
        self.cookies = {}
        if self.espn_s2:
            self.cookies['espn_s2'] = self.espn_s2
        if self.swid:
            self.cookies['SWID'] = self.swid

    def get_my_team(self) -> Any:
        """Get the current user's team"""
        team_id = int(os.getenv('TEAM_ID', 0))

        if team_id:
            # Use specific team ID if provided
            for team in self.league.teams:
                if team.team_id == team_id:
                    return team

        # Fallback: try to identify by current user (may not work if auth issues)
        try:
            for team in self.league.teams:
                if hasattr(team, 'owners') and team.owners:
                    for owner in team.owners:
                        if owner.get('id') == self.swid:
                            return team
        except:
            pass

        # If we get here, return the first team (for development/testing)
        if self.league.teams:
            print(f"Warning: Could not identify user team, using team: {self.league.teams[0].team_name}")
            return self.league.teams[0]

        raise ValueError("Could not identify user team")

    # Core waiver wire analysis method (delegates to WaiverAnalyzer)
    def get_waiver_wire_analysis(self, week: int = None, size: int = 100) -> pd.DataFrame:
        """
        Get comprehensive waiver wire analysis with projections and recommendations.
        
        Args:
            week: NFL week number (defaults to current week)
            size: Number of free agents to analyze
            
        Returns:
            DataFrame with waiver analysis including FanDuel projections
        """
        my_team = self.get_my_team()
        return self.waiver_analyzer.get_waiver_wire_analysis(my_team, week, size)

    # Core suggestion generation method (simplified)
    def generate_lineup_and_waiver_suggestions(self, week: int = None) -> Dict:
        """
        Generate lineup and waiver suggestions for the given week.
        
        Args:
            week: The week to generate suggestions for.
            
        Returns:
            Dictionary containing waiver analysis and lineup suggestions.
        """
        if week is None:
            week = self.league.current_week
            
        try:
            # Get waiver analysis with integrated projections
            waiver_df = self.get_waiver_wire_analysis(week=week, size=100)
            
            # Get current roster
            my_team = self.get_my_team()
            if not my_team:
                return {}
                
            return {
                'waiver_analysis': waiver_df,
                'my_team': my_team,
                'week': week
            }
        except Exception as e:
            print(f"Error generating suggestions: {e}")
            return {}

    # HTML report generation (delegates to HtmlGenerator)
    def generate_weekly_html_report(self, week: int = None) -> str:
        """
        Generates a weekly HTML report with waiver and lineup suggestions.

        Args:
            week: The week to generate the report for. If None, uses current week.

        Returns:
            HTML string containing the full report.
        """
        if week is None:
            week = self.league.current_week

        # Get suggestions
        suggestions = self.generate_lineup_and_waiver_suggestions(week)
        
        # Generate HTML using dedicated HTML generator
        return self.html_generator.generate_waiver_cheatsheet(suggestions, week)

    def generate_start_sit_html_report(self, week: int = None) -> str:
        """
        Generates a weekly HTML report for start/sit decisions.

        Args:
            week: The week to generate the report for. If None, uses current week.

        Returns:
            HTML string containing the full report.
        """
        if week is None:
            week = self.league.current_week

        my_team = self.get_my_team()
        # Call the new method to get optimal lineup recommendations
        optimal_lineup, opponent_optimal_lineup, my_team_bench, opponent_team_bench = self.start_sit_analyzer.get_optimal_lineup_recommendations(my_team, week)

        # Pass the optimal lineup, opponent projections, and bench players to the HTML generator
        return self.html_generator.generate_start_sit_cheatsheet(optimal_lineup, my_team, week, opponent_optimal_lineup, my_team_bench, opponent_team_bench)

    # Email functionality (kept from original)
    def send_email(self, subject: str, text: str, html: str = None) -> bool:
        """
        Send email report via Gmail SMTP
        
        DEPRECATED: This method uses Gmail SMTP which requires OAuth setup.
        Use the webhook system instead: `cli.py postweek --email`
        """
        print("⚠️  WARNING: send_email() is deprecated. Use webhook system instead.")
        print("   Recommended: `uv run python cli.py postweek --html --email`")
        return False
        
        # Original SMTP code kept for reference but not executed
        """
        try:
            smtp_server = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
            smtp_port = int(os.getenv('SMTP_PORT', 587))
            sender_email = os.getenv('SENDER_EMAIL')
            sender_password = os.getenv('SENDER_PASSWORD')
            recipient_email = os.getenv('RECIPIENT_EMAIL')

            if not all([sender_email, sender_password, recipient_email]):
                print("Email credentials not configured")
                return False

            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = sender_email
            msg['To'] = recipient_email

            msg.attach(MIMEText(text, 'plain'))
            if html:
                msg.attach(MIMEText(html, 'html'))

            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.starttls()
                server.login(sender_email, sender_password)
                server.send_message(msg)

            print(f"Email sent successfully to {recipient_email}")
            return True

        except Exception as e:
            print(f"Failed to send email: {e}")
            return False
        """

    # DEPRECATED: Email automation methods - use webhook system instead
    def generate_pre_waiver_report(self, week: int = None) -> Dict[str, str]:
        """Generate pre-waiver report for email automation"""
        html_report = self.generate_weekly_html_report(week)
        return {
            'subject': f'📊 Week {week or self.league.current_week} Waiver Wire Cheatsheet',
            'text': 'See attached HTML report for waiver wire analysis.',
            'html': html_report
        }

    def generate_post_waiver_report(self, week: int = None) -> Dict[str, str]:
        """Generate post-waiver activity report"""
        # TODO: Implement post-waiver activity analysis
        return {
            'subject': f'📈 Week {week or self.league.current_week} Waiver Results',
            'text': 'Post-waiver analysis coming soon!',
            'html': '<h1>Post-waiver analysis coming soon!</h1>'
        }

    def send_pre_waiver_report(self, week: int = None) -> bool:
        """Send automated pre-waiver report"""
        try:
            report = self.generate_pre_waiver_report(week)
            return self.send_email(report['subject'], report['text'], report['html'])
        except Exception as e:
            print(f"Failed to send pre-waiver report: {e}")
            return False

    def send_post_waiver_report(self, week: int = None) -> bool:
        """Send automated post-waiver report"""
        try:
            report = self.generate_post_waiver_report(week)
            return self.send_email(report['subject'], report['text'], report['html'])
        except Exception as e:
            print(f"Failed to send post-waiver report: {e}")
            return False


def main():
    """Example usage of the refactored FantasyManager"""
    try:
        # Initialize manager (reads from .env file)
        fm = FantasyManager()
        
        print("🏈 Fantasy Football Manager - Clean Architecture")
        print(f"League: {fm.league.settings.name}")
        print(f"Year: {fm.year}")
        print(f"Current Week: {fm.league.current_week}")
        
        # Generate waiver analysis
        print("\n📊 Generating waiver wire cheatsheet...")
        html_report = fm.generate_weekly_html_report()
        
        # Save to file
        report_filename = f'waiver_cheatsheet_week_{fm.league.current_week}.html'
        with open(report_filename, 'w') as f:
            f.write(html_report)
        
        print(f"✅ Cheatsheet saved to: {report_filename}")
        
        # Optional: Send email report
        send_email = os.getenv('SEND_EMAIL', 'false').lower() == 'true'
        if send_email:
            print("\n📧 Sending email report...")
            success = fm.send_pre_waiver_report()
            if success:
                print("✅ Email sent successfully")
            else:
                print("❌ Email failed to send")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        raise


if __name__ == "__main__":
    main()