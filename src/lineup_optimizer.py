import pandas as pd
from typing import Dict, Tuple, Set, Any
from espn_api.football import League

class LineupOptimizer:
    """Optimizes fantasy football lineups."""

    def __init__(self, league: League):
        self.league = league

    def get_optimal_lineup(self, roster_df: pd.DataFrame, week: int) -> Tuple[Dict[str, pd.DataFrame], Set[int]]:
        """
        Generates an optimal starting lineup based on Vegas-adjusted projections.
        """
        available_players = roster_df[
            (roster_df['bye_week'] != week) & 
            (~roster_df['injury_status'].isin(['OUT', 'IR', 'DOUBTFUL']))
        ].copy()

        available_players['vegas_adjusted_proj'] = (
            available_players['external_proj'].fillna(available_players['projected_points']) * 
            available_players['vegas_boost'].fillna(1.0)
        )
        available_players = available_players.sort_values('vegas_adjusted_proj', ascending=False)

        optimal_lineup = {}
        used_player_ids = set()

        lineup_slots = {
            'QB': 1, 'RB': 2, 'WR': 2, 'TE': 1, 'K': 1, 'D/ST': 1, 'FLEX': 1
        }

        for pos, count in lineup_slots.items():
            if pos == 'FLEX': continue
            
            players_for_pos = available_players[
                (available_players['position'] == pos) & 
                (~available_players['player_id'].isin(used_player_ids))
            ]
            
            for i in range(count):
                if not players_for_pos.empty:
                    player = players_for_pos.iloc[0]
                    slot_name = f"{pos}{i+1}" if count > 1 else pos
                    optimal_lineup[slot_name] = pd.DataFrame([player])
                    used_player_ids.add(player['player_id'])
                    players_for_pos = players_for_pos.iloc[1:]
                else:
                    optimal_lineup[f"{pos}{i+1}" if count > 1 else pos] = pd.DataFrame()

        flex_candidates = available_players[
            (available_players['position'].isin(['RB', 'WR', 'TE'])) & 
            (~available_players['player_id'].isin(used_player_ids))
        ].sort_values('vegas_adjusted_proj', ascending=False)

        if not flex_candidates.empty and lineup_slots['FLEX'] > 0:
            player = flex_candidates.iloc[0]
            optimal_lineup['FLEX'] = pd.DataFrame([player])
            used_player_ids.add(player['player_id'])
        else:
            optimal_lineup['FLEX'] = pd.DataFrame()

        return optimal_lineup, used_player_ids

    def get_opponent_optimal_lineup(self, my_team: Any, week: int) -> Tuple[Dict[str, pd.DataFrame], pd.DataFrame, Set[int]]:
        """
        Generates an optimal starting lineup for the opponent fantasy team.
        """
        matchup = None
        for m in self.league.scoreboard(week):
            if m.home_team.team_id == my_team.team_id:
                opponent_team = m.away_team
                break
            elif m.away_team.team_id == my_team.team_id:
                opponent_team = m.home_team
                break
        else:
            return {}, pd.DataFrame(), set()

        opponent_roster_data = []
        for player in opponent_team.roster:
            proj_points = player.projected_avg_points if hasattr(player, 'projected_avg_points') else 0.0
            opponent_roster_data.append({
                'player_id': player.playerId,
                'name': player.name,
                'position': player.position,
                'team': player.proTeam,
                'projected_points': proj_points,
                'external_proj': proj_points,
                'injury_status': player.injuryStatus if hasattr(player, 'injuryStatus') else None,
                'bye_week': player.byeWeek if hasattr(player, 'byeWeek') else None,
                'pro_opponent': player.pro_opponent if hasattr(player, 'pro_opponent') else None,
                'fd_opponent': None,
                'fd_salary': None,
                'fd_value': None,
                'recommendation': 'OPPONENT',
            })
        
        opponent_roster_df = pd.DataFrame(opponent_roster_data)
        opponent_roster_df['vegas_boost'] = 1.0

        opponent_roster_df = pd.DataFrame(opponent_roster_data)
        opponent_roster_df['vegas_boost'] = 1.0

        available_opponent_players = opponent_roster_df[
            (opponent_roster_df['bye_week'] != week) & 
            (~opponent_roster_df['injury_status'].isin(['OUT', 'IR', 'DOUBTFUL']))
        ].copy()

        available_opponent_players['sort_proj'] = available_opponent_players['external_proj'].fillna(0)
        available_opponent_players = available_opponent_players.sort_values('sort_proj', ascending=False)

        opponent_optimal_lineup, used_opponent_player_ids = self.get_optimal_lineup(available_opponent_players, week)

        return opponent_optimal_lineup, opponent_roster_df, used_opponent_player_ids
