"""
Hands the quick command.
"""

from .base import BaseCommand

class QuickCommand(BaseCommand):
    """A command to perform a quick team analysis and show top recommendations."""

    def add_arguments(self, parser):
        """Add command-specific arguments to the parser."""
        parser.add_argument("--top", type=int, default=5, help="Number of top recommendations to show")

    def handle(self, args, fm):
        """Execute the command."""
        print(f"\n⚡ Quick Team Analysis - Top {args.top} Recommendations:")
        
        my_team = fm.get_my_team()
        df = fm.waiver_analyzer.get_waiver_wire_analysis(my_team, size=20)
        
        # Team status
        roster_df = fm.player_data_reader.get_roster_data(my_team, week=args.week or fm.league.current_week)
        active_roster = roster_df[roster_df['injury_status'] != 'INJURY_RESERVE']
        ir_players = roster_df[roster_df['injury_status'] == 'INJURY_RESERVE']
        
        print(f"\n🏈 Team: {my_team.team_name} ({my_team.wins}-{my_team.losses})")
        print(f"📋 Active Roster: {len(active_roster)} players")
        if not ir_players.empty:
            print(f"🏥 IR: {', '.join(ir_players['name'].tolist())}")
        
        # Position analysis
        team_context = fm.waiver_analyzer.strategic_analyzer.analyze_team_context(roster_df)
        print("\n🎯 Position Strengths:")
        for pos, strength in team_context.position_strengths.items():
            print(f"  {pos}: {strength}")
        
        # Top recommendations
        print(f"\n💎 Top {args.top} Waiver Recommendations:")
        available = df[df['status'] == 'Available']
        top_recs = available.nlargest(args.top, 'urgency')
        
        for i, (_, player) in enumerate(top_recs.iterrows(), 1):
            urgency = player.get('urgency', 0)
            confidence = player.get('confidence', 'unknown')
            print(f"  {i}. {player['name']} ({player['position']}) - Urgency: {urgency}/10, Confidence: {confidence}")
            print(f"     {player.get('recommendation', 'No recommendation')}")