"""
Handles the start/sit analysis command.
"""

import os
import webbrowser

from .base import BaseCommand

class StartSitCommand(BaseCommand):
    """A command to generate start/sit analysis."""

    def add_arguments(self, parser):
        """Add command-specific arguments to the parser."""
        pass

    def handle(self, args, fm):
        """Execute the command."""
        print("\n📊 Generating Start/Sit Analysis...")
        
        html_report = fm.generate_start_sit_html_report(week=args.week)
        
        week = args.week or fm.league.current_week
        filename = f'start_sit_week_{week}.html'
        
        with open(filename, 'w') as f:
            f.write(html_report)
        
        print(f"✅ Start/Sit Report saved: {filename}")
        
        if args.open:
            webbrowser.open(f'file://{os.path.abspath(filename)}')
            print("🌐 Opened in browser")

