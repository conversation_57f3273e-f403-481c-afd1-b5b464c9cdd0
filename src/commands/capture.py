"""
Handles the capture command.
"""

from .base import BaseCommand
from src.projection_storage import ProjectionStorage
from src.fanduel_projections import get_fanduel_projections
from src.winwithodds_projections import WinWithOddsProjections

class CaptureCommand(BaseCommand):
    """A command to capture projections before games start (legacy JSON storage)."""

    def add_arguments(self, parser):
        """Add command-specific arguments to the parser."""
        # --week is now provided by the parent parser
        parser.add_argument("--sources", nargs="*", choices=["espn", "fanduel", "wwo"],
                               default=["espn", "fanduel"], help="Sources to capture")
        parser.add_argument("--force", action="store_true", help="Force capture even if already stored")

    def handle(self, args, fm):
        """Execute the command."""
        print(f"\n📊 Capturing Week {args.week or fm.league.current_week} Projections (Legacy JSON)")
        print(f"📁 Sources: {', '.join(args.sources)}")
        
        storage = ProjectionStorage()
        week = args.week or fm.league.current_week
        
        # Check if already captured
        if not args.force and storage.load_weekly_projections(week, fm.league.year):
            print(f"⚠️  Projections already captured for Week {week}")
            print("💡 Use --force to override existing data")
            return
        
        # Prepare external sources
        external_sources = {}
        
        if "fanduel" in args.sources:
            print("🔄 Fetching FanDuel projections...")
            try:
                fanduel_data = get_fanduel_projections()
                external_sources["FanDuel"] = fanduel_data
                print(f"✅ FanDuel: {len(fanduel_data)} projections")
            except Exception as e:
                print(f"❌ FanDuel failed: {e}")
        
        if "wwo" in args.sources:
            print("🔄 Fetching WinWithOdds projections...")
            try:
                wwo = WinWithOddsProjections()
                wwo_data = wwo.fetch_season_long_projections()
                external_sources["WinWithOdds"] = wwo_data
                print(f"✅ WinWithOdds: {len(wwo_data)} projections")
            except Exception as e:
                print(f"❌ WinWithOdds failed: {e}")
        
        # Capture and store
        success = storage.capture_and_store_all_projections(
            league=fm.league,
            week=week,
            external_sources=external_sources if external_sources else None
        )
        
        if success:
            print(f"✅ Projections captured successfully for Week {week}")
            print("💡 Use 'python cli.py postweek' to analyze accuracy after games complete")
        else:
            print("❌ Failed to capture projections")
