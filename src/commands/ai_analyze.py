"""
Handles the ai-analyze command.
"""

import os
from datetime import datetime

from .base import BaseCommand
from src.claude_analyzer import ClaudeAnalyzer

class AiAnalyzeCommand(BaseCommand):
    """A command to run AI analysis with direct model integration."""

    def add_arguments(self, parser):
        """Add command-specific arguments to the parser."""
        parser.add_argument("--model", choices=["claude", "openai", "local"], default="claude", help="AI model to use")
        parser.add_argument("--focus", choices=["waiver", "lineup", "trades", "all"], default="all", help="Analysis focus area")
        parser.add_argument("--save", type=str, help="Save AI response to file")

    def handle(self, args, fm):
        """Execute the command."""
        print(f"\n🤖 Running AI Analysis with {args.model.upper()} model...")
        
        try:
            from src.ai_integrator import AIIntegrator
            
            ai_integrator = AIIntegrator()
            claude_analyzer = ClaudeAnalyzer()
            
            # Generate enhanced prompt
            print("📊 Preparing enhanced analysis prompt...")
            clean_prompt = claude_analyzer.generate_clean_prompt(fm)
            
            if not clean_prompt:
                print("❌ Failed to generate analysis prompt")
                return
            
            # Run AI analysis 
            print(f"🧠 Sending to {args.model} for analysis...")
            ai_response = ai_integrator.analyze_with_ai(
                prompt=clean_prompt,
                model=args.model,
                focus=args.focus
            )
            
            if not ai_response:
                print("❌ AI analysis failed")
                return
            
            # Display results
            week = args.week or fm.league.current_week
            print(f"\n🎯 AI Analysis Results (Week {week}):")
            print("=" * 60)
            print(ai_response)
            
            # Save if requested
            if args.save:
                with open(args.save, 'w') as f:
                    f.write(f"# AI Fantasy Analysis - Week {week}\n\n")
                    f.write(f"**Model:** {args.model}\n")
                    f.write(f"**Focus:** {args.focus}\n")
                    f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    f.write(ai_response)
                print(f"\n💾 Analysis saved to: {args.save}")
            
            # Also save structured data
            results_dir = "ai_analysis"
            os.makedirs(results_dir, exist_ok=True)
            results_file = f"{results_dir}/analysis_week_{week}_{args.model}.md"
            
            with open(results_file, 'w') as f:
                f.write(f"# AI Fantasy Analysis - Week {week}\n\n")
                f.write(f"**Model:** {args.model}\n")
                f.write(f"**Focus:** {args.focus}\n")
                f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write(ai_response)
            
            print(f"📁 Full analysis saved to: {results_file}")
            
        except ImportError:
            print("❌ AI integration not available - install required packages")
            print("💡 Fallback: Use 'python cli.py claude --copy' for manual analysis")
        except Exception as e:
            print(f"❌ AI analysis error: {e}")
            print("💡 Fallback: Use 'python cli.py claude --copy' for manual analysis")
