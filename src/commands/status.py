"""
Handles the status command.
"""

from .base import BaseCommand

class StatusCommand(BaseCommand):
    """A command to show team and league status."""

    def add_arguments(self, parser):
        """Add command-specific arguments to the parser."""
        # --verbose is now provided by the parent parser
        pass

    def handle(self, args, fm):
        """Execute the command."""
        my_team = fm.get_my_team()
        
        print(f"🏈 Fantasy Team Status")
        print(f"═══════════════════════════════════════════════")
        print(f"🏆 League: {fm.league.settings.name}")
        print(f"📅 Year: {fm.year} | Week: {fm.league.current_week}")
        print(f"👤 Team: {my_team.team_name}")
        print(f"📈 Record: {my_team.wins}-{my_team.losses}")
        print(f"🎯 Points For: {my_team.points_for:.1f}")
        print(f"🛡️  Points Against: {my_team.points_against:.1f}")
        
        if args.verbose:
            print(f"\n📋 Roster Breakdown:")
            roster_df = fm.player_data_reader.get_roster_data(my_team, week=fm.league.current_week)
            
            for pos in ['QB', 'RB', 'WR', 'TE', 'K', 'D/ST']:
                pos_players = roster_df[roster_df['position'] == pos]
                if not pos_players.empty:
                    print(f"\n  {pos} ({len(pos_players)} players):")
                    for _, player in pos_players.iterrows():
                        status_icon = "🩹" if player['injury_status'] == 'INJURY_RESERVE' else ("⚡" if player.get('is_starting') else "📋")
                        proj = player.get('external_proj', player.get('projected_points', 0))
                        print(f"    {status_icon} {player['name']} - {proj:.1f} proj")
