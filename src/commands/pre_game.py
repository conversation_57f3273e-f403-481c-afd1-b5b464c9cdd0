"""
Handles the pre-game command.
"""

from datetime import datetime
from jinja2 import Environment, FileSystemLoader
import pandas as pd
from typing import List

from .base import BaseCommand

class PreGameCommand(BaseCommand):
    """A command to handle the pre-game lineup optimization."""

    def add_arguments(self, parser):
        """Add command-specific arguments to the parser."""
        parser.add_argument("--ai", choices=["claude", "gemini"], help="Use AI analysis")
        parser.add_argument("--lineup-only", action="store_true", help="Focus only on lineup decisions")

    def handle(self, args, fm):
        """Execute the command."""
        print(f"\n🗓️ PRE-GAME PREPARATION - Week {fm.league.current_week}")
        print("📅 Saturday Lineup Optimization")
        
        # Generate comprehensive pre-game analysis
        print("🎯 Generating comprehensive lineup optimization analysis...")
        print("📊 Collecting matchup data, opponent analysis, injury updates...")
        
        # Get comprehensive pre-game data
        my_team = fm.get_my_team()
        
        # Get enhanced roster data with projections and external context
        roster_df = fm.start_sit_analyzer.get_my_roster_analysis(my_team)
        
        # Get external projections for context  
        try:
            from src.fanduel_projections import get_fanduel_projections
            from src.winwithodds_projections import WinWithOddsProjections
            
            # Get FanDuel data
            fd_data = get_fanduel_projections("NFL_SKILL")
            
            # Get WinWithOdds data
            wwo_proj = WinWithOddsProjections()
            wwo_data = wwo_proj.fetch_season_long_projections()
            
            # Merge external data for enhanced context
            if isinstance(fd_data, pd.DataFrame) and not fd_data.empty:
                for idx, row in roster_df.iterrows():
                    player_name = row['name']
                    
                    # Add FanDuel projection if available  
                    fd_match = fd_data[fd_data['name'].str.contains(player_name.split()[0], case=False, na=False)]
                    if not fd_match.empty:
                        roster_df.at[idx, 'fanduel_proj'] = fd_match.iloc[0].get('projected_points', 0)
                        roster_df.at[idx, 'opponent'] = fd_match.iloc[0].get('opponent', 'Unknown')
            
        except Exception as e:
            print(f"⚠️ Enhanced projections unavailable: {e}")
        
        week = args.week or fm.league.current_week
        
        # Setup Jinja2 environment
        env = Environment(loader=FileSystemLoader('templates/prompts'))
        template = env.get_template('pre_game_analysis.j2')
        
        # Waiver wire context
        waiver_error = None
        available_starters = None
        try:
            waiver_df = fm.waiver_analyzer.get_waiver_wire_analysis(my_team, week=week, size=20)
            available_starters = waiver_df[waiver_df['external_proj'] > 8.0].head(10)
        except Exception as e:
            waiver_error = str(e)

        # Prepare context for the template
        context = {
            "week": week,
            "league_settings": fm.league.settings,
            "my_team": my_team,
            "roster_df": roster_df,
            "available_starters": available_starters,
            "waiver_error": waiver_error,
        }
        
        pre_game_prompt = template.render(context)
        
        if args.ai:
            # Use AI for pre-game analysis with historical context
            try:
                from src.ai_integrator import AIIntegrator
                ai_integrator = AIIntegrator()
                
                print(f"🤖 Sending to {args.ai.upper()} for lineup analysis with historical context...")
                ai_response = ai_integrator.analyze_with_historical_context(
                    pre_game_prompt, "pre_game", week, fm.league.year, args.ai
                )
                
                if ai_response:
                    print(f"\n🎯 {args.ai.upper()} LINEUP INSIGHTS:")
                    print("=" * 60)
                    print(ai_response)
                    
                    # Save analysis with traditional format
                    filename = f"pre_game_analysis_week_{week}_{args.ai}.md"
                    content = f"# Pre-Game Analysis - Week {week}\n\n"
                    content += f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                    content += f"**Model:** {args.ai}\n\n"
                    content += ai_response
                    
                    with open(filename, 'w') as f:
                        f.write(content)
                    print(f"\n📄 Analysis saved: {filename}")
                    
                    # Store in report storage system
                    try:
                        from src.report_storage_manager import ReportStorageManager, ReportType
                        storage = ReportStorageManager()
                        
                        # Extract key insights for metadata
                        key_decisions = self._extract_decisions(ai_response)
                        player_mentions = self._extract_player_mentions(ai_response)
                        strategic_insights = self._extract_insights(ai_response)
                        
                        report_id = storage.store_report(
                            content=content,
                            report_type=ReportType.PRE_GAME,
                            week=week,
                            year=fm.league.year,
                            ai_model=args.ai,
                            key_decisions=key_decisions,
                            strategic_insights=strategic_insights,
                            player_mentions=player_mentions
                        )
                        print(f"📊 Report archived: {report_id}")
                    except Exception as e:
                        print(f"⚠️ Report archiving failed: {e}")
                else:
                    print("❌ AI analysis failed")
            except Exception as e:
                print(f"❌ AI error: {e}")
                print("💡 Generating manual prompt instead...")
                args.ai = None
        
        if not args.ai:
            # Generate manual prompt
            filename = f"pre_game_prompt_week_{week}.md"
            with open(filename, 'w') as f:
                f.write(pre_game_prompt)
            print(f"📋 Manual prompt saved: {filename}")
            print("💡 Copy to your preferred AI for lineup analysis")
    
    def _extract_decisions(self, analysis: str) -> List[str]:
        """Extract key decisions from AI analysis."""
        import re
        decisions = []
        
        # Look for recommendation patterns
        recommendation_patterns = [
            r'\*\*RECOMMENDATION[:\s]*([^*\n]+)',
            r'START\s+([A-Za-z\s\.]+?)(?:\s|$)',
            r'BENCH\s+([A-Za-z\s\.]+?)(?:\s|$)',
            r'Confidence[:\s]*(\d+/10)'
        ]
        
        for pattern in recommendation_patterns:
            matches = re.findall(pattern, analysis, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                if isinstance(match, str) and len(match.strip()) > 3:
                    decisions.append(match.strip()[:100])  # Limit length
        
        return decisions[:10]  # Limit to top 10
    
    def _extract_player_mentions(self, analysis: str) -> List[str]:
        """Extract player names mentioned in analysis."""
        import re
        
        # Common player name patterns (First Last, First Middle Last, etc.)
        player_patterns = [
            r'\b[A-Z][a-z]+\s+[A-Z][a-z]+\b',  # First Last
            r'\b[A-Z][a-z]+\s+[A-Z]\.?\s+[A-Z][a-z]+\b'  # First M. Last
        ]
        
        players = set()
        for pattern in player_patterns:
            matches = re.findall(pattern, analysis)
            for match in matches:
                # Filter out common false positives
                if not any(word in match.lower() for word in ['week', 'team', 'game', 'start', 'bench']):
                    players.add(match)
        
        return list(players)[:20]  # Limit to 20 players
    
    def _extract_insights(self, analysis: str) -> List[str]:
        """Extract strategic insights from AI analysis."""
        import re
        insights = []
        
        # Look for bullet points and strategic statements
        lines = analysis.split('\n')
        for line in lines:
            line = line.strip()
            # Extract bullet points
            if line.startswith(('-', '•', '*')) and len(line) > 10:
                insight = re.sub(r'^[-•*\s]+', '', line)
                if insight and len(insight) > 20:
                    insights.append(insight[:200])  # Limit length
            
            # Extract sentences with strategic keywords
            strategic_keywords = ['stack', 'ceiling', 'floor', 'matchup', 'upside', 'risk', 'volume', 'target']
            if any(keyword in line.lower() for keyword in strategic_keywords):
                if len(line) > 30 and len(line) < 300:
                    insights.append(line[:200])
        
        return insights[:15]  # Limit to top 15 insights
