"""
Handles the email command.
"""

from .base import BaseCommand

class EmailCommand(BaseCommand):
    """A command to send automated reports via email."""

    def add_arguments(self, parser):
        """Add command-specific arguments to the parser."""
        parser.add_argument("type", choices=["pre-waiver", "post-waiver"], help="Type of email report")

    def handle(self, args, fm):
        """Execute the command."""
        print(f"\n📧 Sending {args.type} email report...")
        
        if args.type == "pre-waiver":
            success = fm.send_pre_waiver_report(week=args.week)
        else:  # post-waiver
            success = fm.send_post_waiver_report(week=args.week)
        
        if success:
            print("✅ Email sent successfully")
        else:
            print("❌ Email failed to send")
