"""
Hands the post-waiver command.
"""

from jinja2 import Environment, FileSystemLoader

from .base import BaseCommand

class PostWaiverCommand(BaseCommand):
    """A command to handle the post-waiver league analysis."""

    def add_arguments(self, parser):
        """Add command-specific arguments to the parser."""
        parser.add_argument("--ai", choices=["claude", "gemini"], help="Use AI analysis")

    def handle(self, args, fm):
        """Execute the command."""
        print(f"\n\ud83d\udcc5 POST-WAIVER ANALYSIS - Week {fm.league.current_week}")
        print("\ud83d\udcc5 Wednesday League Movement Review")
        
        # Get league waiver results and activity
        my_team = fm.get_my_team()
        current_week = fm.league.current_week
        
        # Generate basic waiver analysis
        waiver_df = fm.get_waiver_wire_analysis(week=current_week, size=50)
        
        # Setup Jinja2 environment
        env = Environment(loader=FileSystemLoader('templates/prompts'))
        template = env.get_template('post_waiver_analysis.j2')

        if args.ai:
            from src.ai_integrator import AIIntegrator
            integrator = AIIntegrator()
            
            if not integrator.claude_available and not integrator.gemini_available:
                print("❌ No AI models available (claude-code or gemini-cli not found)")
                return
                
            # Create post-waiver analysis prompt
            context = {
                "current_week": current_week,
                "league_name": fm.league.settings.name,
                "my_team_name": my_team.team_name,
                "waiver_df_string": waiver_df.head(20)[['name', 'position', 'projected_points', 'external_proj', 'percent_owned']].to_string(index=False)
            }
            prompt = template.render(context)

            print("🤖 Running AI analysis...")
            model = args.ai if args.ai in integrator.get_available_models() else integrator.get_available_models()[0]
            result = integrator.analyze_post_waiver(prompt, model)
            
            if result:
                print(f"\n\ud83e\udde0 AI Analysis Results ({model}):")
                print("=" * 60)
                print(result)
            else:
                print("❌ AI analysis failed")
        else:
            # Manual analysis mode
            print("\n\ud83c\udfaf Post-Waiver Opportunities:")
            print("- Check which players were claimed")
            print("- Identify trade opportunities") 
            print("- Plan next week's waiver targets")
            
            # Save manual prompt
            filename = f'post_waiver_analysis_week_{current_week}.md'
            context = {
                "current_week": current_week,
                "waiver_df_string": waiver_df.head(20)[['name', 'position', 'projected_points', 'external_proj']].to_string(index=False)
            }
            template = env.get_template('post_waiver_analysis_manual.j2')
            prompt = template.render(context)
            
            with open(filename, 'w') as f:
                f.write(prompt)
            print(f"\ud83d\udccb Manual analysis prompt saved: {filename}")