"""
Handles the pre-waiver command.
"""

from datetime import datetime

from .base import BaseCommand
from src.claude_analyzer import ClaudeAnalyzer

class PreWaiverCommand(BaseCommand):
    """A command to handle the pre-waiver strategic analysis."""

    def add_arguments(self, parser):
        """Add command-specific arguments to the parser."""
        parser.add_argument("--ai", choices=["claude", "gemini"], help="Use AI analysis")
        parser.add_argument("--size", type=int, default=50, help="Players to analyze")

    def handle(self, args, fm):
        """Execute the command."""
        print(f"\n🗓️ PRE-WAIVER ANALYSIS - Week {fm.league.current_week}")
        print("📅 Tuesday Strategic Planning")
        
        # Generate enhanced waiver analysis
        print(f"🔍 Analyzing {args.size} waiver targets...")
        
        claude_analyzer = ClaudeAnalyzer()
        clean_prompt = claude_analyzer.generate_clean_prompt(fm)
        
        if not clean_prompt:
            print("❌ Failed to generate analysis prompt")
            return
        
        week = args.week or fm.league.current_week
        
        if args.ai:
            # Use AI integration
            try:
                from src.ai_integrator import AIIntegrator
                ai_integrator = AIIntegrator()
                
                print(f"🤖 Sending to {args.ai.upper()} for strategic analysis...")
                ai_response = ai_integrator.analyze_pre_waiver(clean_prompt, args.ai)
                
                if ai_response:
                    print(f"\n🎯 {args.ai.upper()} PRE-WAIVER INSIGHTS:")
                    print("=" * 60)
                    print(ai_response)
                    
                    # Save analysis
                    filename = f"pre_waiver_analysis_week_{week}_{args.ai}.md"
                    with open(filename, 'w') as f:
                        f.write(f"# Pre-Waiver Analysis - Week {week}\n\n")
                        f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"**Model:** {args.ai}\n\n")
                        f.write(ai_response)
                    print(f"\n📄 Analysis saved: {filename}")
                else:
                    print("❌ AI analysis failed")
            except Exception as e:
                print(f"❌ AI error: {e}")
                print("💡 Generating manual prompt instead...")
                args.ai = None
        
        if not args.ai:
            # Generate manual prompt
            filename = f"pre_waiver_prompt_week_{week}.md"
            with open(filename, 'w') as f:
                f.write(clean_prompt)
            print(f"📋 Manual prompt saved: {filename}")
            print("💡 Copy to your preferred AI for analysis")
