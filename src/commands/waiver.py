"""
Handles the waiver wire analysis command.
"""

import os
import webbrowser

from .base import BaseCommand

class WaiverCommand(BaseCommand):
    """A command to generate waiver wire analysis."""

    def add_arguments(self, parser):
        """Add command-specific arguments to the parser."""
        parser.add_argument("--size", type=int, default=50, help="Number of players to analyze (default: 50)")

    def handle(self, args, fm):
        """Execute the command."""
        print(f"\n🧠 Generating AI-Enhanced Waiver Analysis (analyzing {args.size} players)...")
        
        # Temporarily store the size preference and use existing method
        original_get_waiver = fm.get_waiver_wire_analysis
        
        def custom_get_waiver(week=None):
            my_team = fm.get_my_team()
            return fm.waiver_analyzer.get_waiver_wire_analysis(my_team, week=week, size=args.size)
        
        fm.get_waiver_wire_analysis = custom_get_waiver
        
        try:
            html_report = fm.generate_weekly_html_report(week=args.week)
            
            week = args.week or fm.league.current_week
            filename = f'ai_waiver_analysis_week_{week}.html'
            
            with open(filename, 'w') as f:
                f.write(html_report)
            
            print(f"✅ AI-Enhanced Waiver Report saved: {filename}")
            print("📊 Includes: Strategic ADD/DROP pairs, confidence scoring, team context analysis")
            
            if args.open:
                webbrowser.open(f'file://{os.path.abspath(filename)}')
                print("🌐 Opened in browser")
        finally:
            # Restore original method
            fm.get_waiver_wire_analysis = original_get_waiver
