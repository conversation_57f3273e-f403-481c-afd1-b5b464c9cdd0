"""
Handles the capture-db command.
"""

from .base import BaseCommand
from src.enhanced_projection_storage import EnhancedProjectionStorage

class CaptureDbCommand(BaseCommand):
    """A command to capture projections and store in DuckDB."""

    def add_arguments(self, parser):
        """Add command-specific arguments to the parser."""
        # --week and --verbose are now provided by the parent parser
        parser.add_argument("--sources", nargs="*",
                           default=["espn"], help="Sources to capture (e.g., espn, draftsharks)")

    def handle(self, args, fm):
        """Execute the command."""
        print(f"\n🗃️  Capturing projections to DuckDB for Week {args.week or 'current'}")
        print(f"📊 Sources: {', '.join(args.sources)}")
        
        try:
            # Initialize enhanced storage
            storage = EnhancedProjectionStorage()
            
            # Determine week
            week = args.week or storage.league.current_week if storage.league else 1
            
            print(f"🔄 Capturing Week {week} projections...")
            
            # Capture from requested sources
            results = storage.capture_all_sources(week, args.sources)
            
            # Display results
            total_captured = sum(results.values())
            print(f"\n✅ Capture Complete: {total_captured} total projections")
            
            for source, count in results.items():
                status_icon = "✅" if count > 0 else "❌"
                print(f"   {status_icon} {source}: {count} projections")
            
            # Show analytics summary
            if total_captured > 0:
                print("\n📈 Analytics Available:")
                print("   • Cross-source consensus analysis")
                print("   • Ceiling/floor strategic plays")
                print("   • DFS value identification")
                print("   • Position-based comparisons")
                print("\n💡 Use MCP queries to analyze captured data")
            
            storage.close()
            
        except ImportError:
            print("❌ Enhanced projection storage not available")
            print("💡 Install DuckDB: pip install duckdb")
        except Exception as e:
            print(f"❌ Capture failed: {e}")
            if getattr(args, 'verbose', False):
                import traceback
                traceback.print_exc()
