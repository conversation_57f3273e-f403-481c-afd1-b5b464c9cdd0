"""
Handles the claude command.
"""

from .base import BaseCommand
from src.claude_analyzer import ClaudeAnalyzer

class ClaudeCommand(BaseCommand):
    """A command to generate Claude AI analysis prompt."""

    def add_arguments(self, parser):
        """Add command-specific arguments to the parser."""
        parser.add_argument("--copy", action="store_true", help="Copy prompt to clipboard (macOS)")

    def handle(self, args, fm):
        """Execute the command."""
        print("\n🧠 Generating Claude AI Analysis Prompt...")
        
        claude_analyzer = ClaudeAnalyzer()
        
        # Generate comprehensive weekly summary
        print("📊 Generating comprehensive weekly summary...")
        weekly_summary = claude_analyzer.generate_weekly_summary(fm)
        
        # Create structured Claude prompt
        print("📝 Creating structured Claude prompt...")
        claude_prompt = claude_analyzer.create_claude_prompt(weekly_summary)
        
        # Generate clean focused prompt
        print("🎯 Generating clean focused prompt...")
        clean_prompt = claude_analyzer.generate_clean_prompt(fm)
        
        if not clean_prompt:
            print("❌ Failed to generate Claude prompt")
            return
        
        # Save all analysis files
        week = args.week or fm.league.current_week
        output_dir = f"claude_analysis"
        claude_analyzer.save_analysis_files(weekly_summary, claude_prompt, output_dir=output_dir, clean_prompt=clean_prompt)
        
        print(f"✅ Claude Analysis Suite saved to {output_dir}/")
        print("📁 Generated files:")
        print(f"   • weekly_data.json - Structured analysis data")
        print(f"   • claude_prompt.md - Comprehensive strategic prompt")
        print(f"   • clean_prompt.md - Focused prompt for quick analysis")
        print(f"   • analyze_with_claude.sh - Automation script")
        print("🎯 Copy either prompt to Claude for strategic analysis")
        
        if args.copy:
            try:
                import subprocess
                subprocess.run(['pbcopy'], input=clean_prompt.encode(), check=True)
                print("📋 Clean prompt copied to clipboard!")
            except (ImportError, subprocess.CalledProcessError, FileNotFoundError):
                print("❌ Clipboard copy failed (pbcopy not available)")
