"""
Historical Analyzer - Extracts patterns and lessons learned from stored reports.

Integrates with ReportStorageManager to provide AI analysis with historical context,
pattern recognition, and decision tracking for continuous improvement.
"""

import re
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
from collections import defaultdict, Counter
from dataclasses import dataclass

from .report_storage_manager import ReportStorageManager, ReportType, HistoricalContext

@dataclass
class PlayerTrend:
    """Tracks a player's performance trend over time."""
    player_name: str
    weeks_tracked: List[int]
    actual_scores: List[float]
    projected_scores: List[float]
    accuracy_trend: float  # positive = consistently outperforming
    volatility: float      # higher = more boom/bust
    recommendation_success_rate: float

@dataclass
class DecisionPattern:
    """Identifies patterns in strategic decisions."""
    decision_type: str
    success_rate: float
    avg_confidence: float
    avg_points_impact: float
    best_conditions: List[str]  # When this decision type works best
    failure_patterns: List[str] # Common reasons for failure

@dataclass
class LessonLearned:
    """A specific lesson extracted from historical data."""
    lesson: str
    supporting_evidence: List[str]
    confidence_score: float
    applicable_situations: List[str]
    weeks_observed: List[int]

class HistoricalAnalyzer:
    """Analyzes historical reports to extract patterns and lessons."""
    
    def __init__(self, storage_manager: ReportStorageManager):
        self.storage = storage_manager
    
    def analyze_player_trends(self, year: int, min_weeks: int = 3) -> List[PlayerTrend]:
        """Analyze player performance trends across multiple weeks."""
        import duckdb
        
        with duckdb.connect(self.storage.db_path) as conn:
            # Get player performance data
            results = conn.execute("""
                SELECT 
                    player_name,
                    week,
                    actual_points,
                    projected_points,
                    was_recommended,
                    recommendation_confidence
                FROM player_performance_history 
                WHERE year = ? AND actual_points IS NOT NULL
                ORDER BY player_name, week
            """, (year,)).fetchall()
        
        # Group by player
        player_data = defaultdict(lambda: {
            'weeks': [], 'actual': [], 'projected': [], 
            'recommendations': [], 'confidences': []
        })
        
        for row in results:
            player_name, week, actual, projected, was_rec, confidence = row
            player_data[player_name]['weeks'].append(week)
            player_data[player_name]['actual'].append(actual)
            player_data[player_name]['projected'].append(projected or 0)
            player_data[player_name]['recommendations'].append(was_rec)
            player_data[player_name]['confidences'].append(confidence or 0)
        
        # Calculate trends
        trends = []
        for player_name, data in player_data.items():
            if len(data['weeks']) < min_weeks:
                continue
            
            # Calculate accuracy trend (actual vs projected)
            accuracy_diffs = []
            for actual, proj in zip(data['actual'], data['projected']):
                if proj > 0:
                    accuracy_diffs.append(actual - proj)
            
            accuracy_trend = sum(accuracy_diffs) / len(accuracy_diffs) if accuracy_diffs else 0
            
            # Calculate volatility (standard deviation of scores)
            avg_score = sum(data['actual']) / len(data['actual'])
            volatility = (sum((score - avg_score) ** 2 for score in data['actual']) / len(data['actual'])) ** 0.5
            
            # Calculate recommendation success rate
            rec_successes = []
            for i, was_recommended in enumerate(data['recommendations']):
                if was_recommended and i < len(data['actual']):
                    # Consider it successful if they outperformed their projection
                    actual = data['actual'][i]
                    projected = data['projected'][i]
                    if projected > 0:
                        rec_successes.append(actual >= projected)
            
            success_rate = sum(rec_successes) / len(rec_successes) if rec_successes else 0
            
            trends.append(PlayerTrend(
                player_name=player_name,
                weeks_tracked=data['weeks'],
                actual_scores=data['actual'],
                projected_scores=data['projected'],
                accuracy_trend=accuracy_trend,
                volatility=volatility,
                recommendation_success_rate=success_rate
            ))
        
        return sorted(trends, key=lambda x: abs(x.accuracy_trend), reverse=True)
    
    def analyze_decision_patterns(self, year: int) -> List[DecisionPattern]:
        """Analyze patterns in strategic decisions."""
        import duckdb
        
        with duckdb.connect(self.storage.db_path) as conn:
            results = conn.execute("""
                SELECT 
                    decision_type,
                    confidence_level,
                    was_correct,
                    points_impact,
                    week
                FROM decision_outcomes 
                WHERE year = ?
                ORDER BY decision_type, week
            """, (year,)).fetchall()
        
        # Group by decision type
        decision_data = defaultdict(lambda: {
            'outcomes': [], 'confidences': [], 'impacts': [], 'weeks': []
        })
        
        for row in results:
            decision_type, confidence, was_correct, points_impact, week = row
            decision_data[decision_type]['outcomes'].append(was_correct)
            decision_data[decision_type]['confidences'].append(confidence or 5)
            decision_data[decision_type]['impacts'].append(points_impact or 0)
            decision_data[decision_type]['weeks'].append(week)
        
        patterns = []
        for decision_type, data in decision_data.items():
            if len(data['outcomes']) < 2:
                continue
            
            success_rate = sum(data['outcomes']) / len(data['outcomes'])
            avg_confidence = sum(data['confidences']) / len(data['confidences'])
            avg_impact = sum(data['impacts']) / len(data['impacts'])
            
            # Identify best conditions (high confidence + success)
            best_conditions = []
            failure_patterns = []
            
            for i, (outcome, confidence, impact) in enumerate(zip(
                data['outcomes'], data['confidences'], data['impacts']
            )):
                if outcome and confidence >= 7:
                    best_conditions.append(f"High confidence ({confidence}/10) decisions")
                elif not outcome and confidence >= 7:
                    failure_patterns.append(f"Overconfident decisions (confidence {confidence}/10)")
                elif not outcome and abs(impact) > 5:
                    failure_patterns.append(f"High-impact failures ({impact:+.1f} pts)")
            
            # Remove duplicates and limit
            best_conditions = list(set(best_conditions))[:3]
            failure_patterns = list(set(failure_patterns))[:3]
            
            patterns.append(DecisionPattern(
                decision_type=decision_type,
                success_rate=success_rate,
                avg_confidence=avg_confidence,
                avg_points_impact=avg_impact,
                best_conditions=best_conditions,
                failure_patterns=failure_patterns
            ))
        
        return sorted(patterns, key=lambda x: x.success_rate, reverse=True)
    
    def extract_lessons_learned(self, year: int, weeks_back: int = 4) -> List[LessonLearned]:
        """Extract actionable lessons from historical analysis."""
        # Get recent reports for analysis
        import duckdb
        
        with duckdb.connect(self.storage.db_path) as conn:
            current_week = conn.execute("""
                SELECT MAX(week) FROM report_metadata WHERE year = ?
            """, (year,)).fetchone()[0]
            
            if not current_week:
                return []
            
            # Get strategic insights from recent weeks
            insights_data = conn.execute("""
                SELECT week, strategic_insights, report_type
                FROM report_metadata 
                WHERE year = ? AND week >= ? AND strategic_insights IS NOT NULL
                ORDER BY week DESC, created_at DESC
            """, (year, max(1, current_week - weeks_back))).fetchall()
        
        # Extract patterns from insights
        all_insights = []
        week_insights = defaultdict(list)
        
        for week, insights_json, report_type in insights_data:
            if insights_json:
                import json
                insights = json.loads(insights_json)
                for insight in insights:
                    all_insights.append(insight)
                    week_insights[week].append(insight)
        
        # Get decision patterns and player trends
        decision_patterns = self.analyze_decision_patterns(year)
        player_trends = self.analyze_player_trends(year)
        
        lessons = []
        
        # Generate lessons from decision patterns
        for pattern in decision_patterns[:5]:  # Top 5 patterns
            if pattern.success_rate >= 0.7:
                lesson = f"{pattern.decision_type.replace('_', ' ').title()} decisions are working well"
                evidence = [
                    f"{pattern.success_rate*100:.0f}% success rate",
                    f"Average impact: {pattern.avg_points_impact:+.1f} points"
                ]
                evidence.extend(pattern.best_conditions)
                
                lessons.append(LessonLearned(
                    lesson=lesson,
                    supporting_evidence=evidence,
                    confidence_score=pattern.success_rate,
                    applicable_situations=[pattern.decision_type],
                    weeks_observed=list(range(max(1, current_week - weeks_back), current_week + 1))
                ))
            elif pattern.success_rate <= 0.4:
                lesson = f"Reconsider {pattern.decision_type.replace('_', ' ')} strategy"
                evidence = [
                    f"Only {pattern.success_rate*100:.0f}% success rate",
                    f"Average impact: {pattern.avg_points_impact:+.1f} points"
                ]
                evidence.extend(pattern.failure_patterns)
                
                lessons.append(LessonLearned(
                    lesson=lesson,
                    supporting_evidence=evidence,
                    confidence_score=1 - pattern.success_rate,  # High confidence in avoiding bad strategies
                    applicable_situations=[pattern.decision_type],
                    weeks_observed=list(range(max(1, current_week - weeks_back), current_week + 1))
                ))
        
        # Generate lessons from player trends
        for trend in player_trends[:3]:  # Top 3 trends
            if trend.accuracy_trend > 2:  # Consistently outperforming
                lesson = f"{trend.player_name} consistently outperforms projections"
                evidence = [
                    f"Average outperformance: +{trend.accuracy_trend:.1f} points",
                    f"Tracked across {len(trend.weeks_tracked)} weeks",
                    f"Volatility: {trend.volatility:.1f} (lower is more consistent)"
                ]
                
                lessons.append(LessonLearned(
                    lesson=lesson,
                    supporting_evidence=evidence,
                    confidence_score=min(trend.accuracy_trend / 5, 0.9),  # Cap at 90%
                    applicable_situations=["lineup_decisions", "waiver_priority"],
                    weeks_observed=trend.weeks_tracked
                ))
            elif trend.accuracy_trend < -2:  # Consistently underperforming
                lesson = f"Be cautious with {trend.player_name} - underperforming projections"
                evidence = [
                    f"Average underperformance: {trend.accuracy_trend:.1f} points",
                    f"Tracked across {len(trend.weeks_tracked)} weeks"
                ]
                
                lessons.append(LessonLearned(
                    lesson=lesson,
                    supporting_evidence=evidence,
                    confidence_score=min(abs(trend.accuracy_trend) / 5, 0.9),
                    applicable_situations=["lineup_decisions", "avoid_recommendations"],
                    weeks_observed=trend.weeks_tracked
                ))
        
        # Extract common themes from strategic insights text
        insight_themes = self._extract_insight_themes(all_insights)
        for theme, examples in insight_themes.items():
            if len(examples) >= 2:  # Recurring theme
                lesson = f"Recurring insight: {theme}"
                lessons.append(LessonLearned(
                    lesson=lesson,
                    supporting_evidence=examples[:3],  # Top 3 examples
                    confidence_score=min(len(examples) / 5, 0.8),  # Based on frequency
                    applicable_situations=["strategic_planning"],
                    weeks_observed=list(week_insights.keys())
                ))
        
        return sorted(lessons, key=lambda x: x.confidence_score, reverse=True)
    
    def _extract_insight_themes(self, insights: List[str]) -> Dict[str, List[str]]:
        """Extract common themes from strategic insights."""
        themes = defaultdict(list)
        
        # Define common patterns
        patterns = {
            "Stack strategies": r"stack|correlation|connected",
            "Ceiling plays": r"ceiling|upside|boom potential",
            "Floor plays": r"floor|safe|consistent|reliable",
            "Matchup exploitation": r"matchup|opponent|defense|weakness",
            "Volume concerns": r"volume|touches|snaps|usage",
            "Injury impacts": r"injury|questionable|doubtful|IR",
            "Weather effects": r"weather|wind|rain|conditions",
            "Game script": r"game script|trailing|leading|blowout"
        }
        
        for insight in insights:
            insight_lower = insight.lower()
            for theme, pattern in patterns.items():
                if re.search(pattern, insight_lower):
                    themes[theme].append(insight[:100] + "..." if len(insight) > 100 else insight)
        
        return themes
    
    def generate_enhanced_context(self, week: int, year: int) -> str:
        """Generate enhanced historical context for AI integration."""
        # Get base historical context
        base_context = self.storage.get_historical_context(week, year)
        
        # Get lessons learned
        lessons = self.extract_lessons_learned(year)
        
        # Get decision patterns
        patterns = self.analyze_decision_patterns(year)
        
        # Get player trends
        trends = self.analyze_player_trends(year)
        
        context_parts = []
        
        # Add lessons learned
        if lessons:
            context_parts.append("## 📚 LESSONS LEARNED FROM RECENT WEEKS")
            for lesson in lessons[:5]:  # Top 5 lessons
                context_parts.append(f"- **{lesson.lesson}**")
                context_parts.append(f"  - Evidence: {', '.join(lesson.supporting_evidence[:2])}")
                if lesson.confidence_score > 0.7:
                    context_parts.append(f"  - Confidence: HIGH ({lesson.confidence_score*100:.0f}%)")
                context_parts.append("")
        
        # Add decision patterns
        if patterns:
            context_parts.append("## 🎯 STRATEGIC DECISION PERFORMANCE")
            for pattern in patterns[:3]:  # Top 3 patterns
                context_parts.append(f"- **{pattern.decision_type.replace('_', ' ').title()}**: {pattern.success_rate*100:.0f}% success rate")
                if pattern.best_conditions:
                    context_parts.append(f"  - Works best: {', '.join(pattern.best_conditions[:2])}")
                if pattern.failure_patterns:
                    context_parts.append(f"  - Avoid: {', '.join(pattern.failure_patterns[:2])}")
                context_parts.append("")
        
        # Add player trends
        significant_trends = [t for t in trends if abs(t.accuracy_trend) > 1.5]
        if significant_trends:
            context_parts.append("## 📈 NOTABLE PLAYER TRENDS")
            for trend in significant_trends[:5]:  # Top 5 trends
                direction = "outperforming" if trend.accuracy_trend > 0 else "underperforming"
                context_parts.append(f"- **{trend.player_name}**: {direction} by {abs(trend.accuracy_trend):.1f} pts/week")
                context_parts.append("")
        
        # Add previous week insights
        if base_context.previous_week_insights:
            context_parts.append("## 💡 KEY INSIGHTS FROM PREVIOUS WEEKS")
            for insight in base_context.previous_week_insights[:3]:
                context_parts.append(f"- {insight}")
            context_parts.append("")
        
        return "\n".join(context_parts)