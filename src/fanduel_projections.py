import requests
import pandas as pd
import os
import json
import time

CACHE_DIR = "cache"
CACHE_FILE = os.path.join(CACHE_DIR, "fanduel_projections_cache.json")
CACHE_DURATION = 3600 * 24  # 24 hours in seconds

def get_fanduel_projections(position="NFL_SKILL", projection_type="PPR"):
    """
    Fetches fantasy football projections from FanDuel's GraphQL API.

    Args:
        position (str): The position to get projections for (e.g., "NFL_SKILL", "NFL_KICKER").
        projection_type (str): The projection type (e.g., "PPR", "HALF", "STANDARD").

    Returns:
        pandas.DataFrame: A DataFrame containing the player projections.
    """
    # Ensure cache directory exists
    os.makedirs(CACHE_DIR, exist_ok=True)

    # Define cache file path based on position and projection type
    current_cache_file = os.path.join(CACHE_DIR, f"fanduel_projections_{position}_{projection_type}.json")

    # Check for cached data
    if os.path.exists(current_cache_file):
        file_mod_time = os.path.getmtime(current_cache_file)
        if (time.time() - file_mod_time) < CACHE_DURATION:
            print(f"Loading FanDuel projections from cache: {current_cache_file}")
            with open(current_cache_file, 'r') as f:
                data = json.load(f) # Load raw data
            
            # Process the loaded data as if it just came from the API
            projections = data.get('data', {}).get('getProjections', [])

            fanduel_projections = []
            for p in projections:
                if p.get('player') and p.get('team') and p.get('gameInfo'):
                    player_name = p['player']['name']
                    fantasy_points = p['fantasy']
                    team_abbr = p['team']['abbreviation']
                    home_team_abbr = p['gameInfo']['homeTeam']['abbreviation']
                    away_team_abbr = p['gameInfo']['awayTeam']['abbreviation']
                    
                    # Extract salary and value data
                    salary = p.get('salary', 'N/A')
                    value = p.get('value', 'N/A')
                    player_position = p.get('player', {}).get('position', '')

                    opponent = away_team_abbr if team_abbr == home_team_abbr else home_team_abbr

                    fanduel_projections.append({
                        'player': player_name,
                        'external_proj': fantasy_points,
                        'opp': opponent,
                        'fd_salary': salary,
                        'fd_value': value,
                        'fd_position': player_position,
                        'fd_position_type': position, # Add this for caching
                        'fd_projection_type': projection_type # Add this for caching
                    })
            return pd.DataFrame(fanduel_projections)

    graphql_url = "https://fdresearch-api.fanduel.com/graphql"
    graphql_payload = {
      "query": '''query GetProjections($input: ProjectionsInput!) {
      getProjections(input: $input) {
        ... on NflSkill {
          player {
            name
            position
          }
          team {
            abbreviation
          }
          gameInfo {
            homeTeam {
              abbreviation
            }
            awayTeam {
              abbreviation
            }
          }
          fantasy
          salary
          value
        }
        ... on NflKicker {
          player {
            name
            position
          }
          team {
            abbreviation
          }
          gameInfo {
            homeTeam {
              abbreviation
            }
            awayTeam {
              abbreviation
            }
          }
          fantasy
          salary
          value
        }
        ... on NflDefenseSt {
          player {
            name
            position
          }
          team {
            abbreviation
          }
          gameInfo {
            homeTeam {
              abbreviation
            }
            awayTeam {
              abbreviation
            }
          }
          fantasy
          salary
          value
        }
      }
    }''',
      "variables": {
        "input": {
          "type": projection_type,
          "position": position,
          "sport": "NFL"
        }
      },
      "operationName": "GetProjections"
    }

    try:
        response = requests.post(graphql_url, json=graphql_payload)
        response.raise_for_status()  # Raise an exception for bad status codes
        data = response.json()

        projections = data.get('data', {}).get('getProjections', [])

        fanduel_projections = []
        for p in projections:
            player_data = p.get('player')
            team_data = p.get('team')
            game_info_data = p.get('gameInfo')

            if player_data and team_data and game_info_data:
                player_name = player_data.get('name', 'UNKNOWN')
                fantasy_points = p.get('fantasy', 0)
                team_abbr = team_data.get('abbreviation', 'UNKNOWN')
                home_team_abbr = game_info_data.get('homeTeam', {}).get('abbreviation', 'UNKNOWN')
                away_team_abbr = game_info_data.get('awayTeam', {}).get('abbreviation', 'UNKNOWN')
                
                # Extract salary and value data
                salary = p.get('salary', 'N/A')
                value = p.get('value', 'N/A')
                position = player_data.get('position', '')

                opponent = away_team_abbr if team_abbr == home_team_abbr else home_team_abbr

                fanduel_projections.append({
                    'player': player_name,
                    'external_proj': fantasy_points,
                    'opp': opponent,
                    'fd_salary': salary,
                    'fd_value': value,
                    'fd_position': position,
                    'fd_position_type': position, # Add this for caching
                    'fd_projection_type': projection_type # Add this for caching
                })
            else:
                print(f"Skipping projection due to missing data: {p}")

        # Save fetched data to cache
        with open(current_cache_file, 'w') as f:
            json.dump(data, f) # Save raw data
        print(f"Saved FanDuel projections to cache: {current_cache_file}")

        return pd.DataFrame(fanduel_projections)

    except requests.exceptions.RequestException as e:
        print(f"Error fetching data from FanDuel API: {e}")
        return pd.DataFrame()
    except (KeyError, TypeError) as e:
        print(f"Error parsing FanDuel API response: {e}")
        return pd.DataFrame()

if __name__ == '__main__':
    # Example usage:
    ppr_projections = get_fanduel_projections()
    print("PPR Projections:")
    print(ppr_projections.head())

    kicker_projections = get_fanduel_projections(position="NFL_KICKER")
    print("\nKicker Projections:")
    print(kicker_projections.head())

