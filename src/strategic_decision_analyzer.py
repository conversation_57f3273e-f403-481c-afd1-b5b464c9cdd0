"""
Strategic Decision Analyzer - validates pre-game strategic decisions against actual performance.

This module parses pre-game analysis files and evaluates whether strategic recommendations
worked out, providing lessons learned for future decision-making.
"""

import re
import os
import glob
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

@dataclass
class StrategicDecision:
    """Represents a strategic start/sit decision made pre-game."""
    player_started: str
    player_benched: str
    position: str
    confidence: int
    rationale: str
    decision_type: str  # "start/sit", "stack", "ceiling", "floor"
    projected_started: Optional[float] = None
    projected_benched: Optional[float] = None
    actual_started: Optional[float] = None
    actual_benched: Optional[float] = None

@dataclass
class DecisionOutcome:
    """Represents the outcome of a strategic decision."""
    decision: StrategicDecision
    was_correct: bool
    points_gained_lost: float
    confidence_validated: bool
    lessons_learned: str

class StrategicDecisionAnalyzer:
    """Analyzes the effectiveness of pre-game strategic decisions."""
    
    def __init__(self):
        self.decisions: List[StrategicDecision] = []
        self.outcomes: List[DecisionOutcome] = []
    
    def parse_pregame_analysis_file(self, filepath: str, week: int) -> List[StrategicDecision]:
        """Parse a pre-game analysis markdown file to extract strategic decisions."""
        if not os.path.exists(filepath):
            return []
        
        with open(filepath, 'r') as f:
            content = f.read()
        
        decisions = []
        
        # Look for recommendation patterns
        recommendation_pattern = r'\*\*RECOMMENDATION:\s*START\s+([^(]+)\s*\(Confidence:\s*(\d+)/10\)\*\*'
        matches = re.finditer(recommendation_pattern, content)
        
        for match in matches:
            player_started = match.group(1).strip()
            confidence = int(match.group(2))
            
            # Find the section this recommendation belongs to
            section_start = content.rfind('###', 0, match.start())
            section_end = content.find('###', match.end())
            if section_end == -1:
                section_end = len(content)
            
            section_content = content[section_start:section_end]
            
            # Extract position from section header
            position_match = re.search(r'###\s*\*\*(\w+):', section_content)
            position = position_match.group(1) if position_match else "Unknown"
            
            # Extract vs opponent (player benched)
            vs_pattern = r'(\w+(?:\s+\w+)*)\s+vs\s+(\w+(?:\s+\w+)*)'
            vs_match = re.search(vs_pattern, section_content)
            
            if vs_match:
                # Determine which player was recommended to start
                player1, player2 = vs_match.groups()
                if player_started.lower() in player1.lower():
                    player_benched = player2.strip()
                else:
                    player_benched = player1.strip()
            else:
                player_benched = "Unknown"
            
            # Extract rationale (bullet points after recommendation)
            rationale_start = match.end()
            rationale_end = content.find('\n##', rationale_start)
            if rationale_end == -1:
                rationale_end = section_end
            
            rationale_content = content[rationale_start:rationale_end]
            rationale_lines = [line.strip() for line in rationale_content.split('\n') 
                             if line.strip().startswith('-')]
            rationale = ' | '.join([line[1:].strip() for line in rationale_lines[:3]])
            
            # Determine decision type based on rationale keywords
            decision_type = "start/sit"
            if "stack" in rationale.lower():
                decision_type = "stack"
            elif "ceiling" in rationale.lower():
                decision_type = "ceiling"
            elif "floor" in rationale.lower():
                decision_type = "floor"
            
            decision = StrategicDecision(
                player_started=player_started,
                player_benched=player_benched,
                position=position,
                confidence=confidence,
                rationale=rationale,
                decision_type=decision_type
            )
            
            decisions.append(decision)
        
        return decisions
    
    def find_pregame_analysis_files(self, week: Optional[int] = None) -> List[str]:
        """Find pre-game analysis files for a specific week or all weeks."""
        pattern = f"*pre_game_analysis*week_{week}*.md" if week else "*pre_game_analysis*.md"
        files = glob.glob(pattern)
        return sorted(files, reverse=True)  # Most recent first
    
    def evaluate_decisions(self, decisions: List[StrategicDecision], 
                          actual_scores: Dict[str, float]) -> List[DecisionOutcome]:
        """Evaluate strategic decisions against actual player performance."""
        outcomes = []
        
        for decision in decisions:
            started_score = actual_scores.get(decision.player_started, 0.0)
            benched_score = actual_scores.get(decision.player_benched, 0.0)
            
            # Update decision with actual scores
            decision.actual_started = started_score
            decision.actual_benched = benched_score
            
            # Determine if decision was correct
            was_correct = started_score >= benched_score
            points_gained_lost = started_score - benched_score
            
            # Validate confidence level
            confidence_validated = self._validate_confidence(decision, points_gained_lost)
            
            # Generate lessons learned
            lessons_learned = self._generate_lessons(decision, was_correct, points_gained_lost)
            
            outcome = DecisionOutcome(
                decision=decision,
                was_correct=was_correct,
                points_gained_lost=points_gained_lost,
                confidence_validated=confidence_validated,
                lessons_learned=lessons_learned
            )
            
            outcomes.append(outcome)
        
        return outcomes
    
    def _validate_confidence(self, decision: StrategicDecision, points_diff: float) -> bool:
        """Validate if confidence level was appropriate given outcome."""
        abs_diff = abs(points_diff)
        
        # High confidence (8-10) should have significant point differences
        if decision.confidence >= 8:
            return abs_diff >= 3.0  # High confidence justified by big difference
        
        # Medium confidence (5-7) should be close decisions
        elif decision.confidence >= 5:
            return abs_diff <= 5.0  # Medium confidence for reasonable differences
        
        # Low confidence (<5) can be any outcome
        else:
            return True  # Low confidence is always validated
    
    def _generate_lessons(self, decision: StrategicDecision, was_correct: bool, 
                         points_diff: float) -> str:
        """Generate lessons learned from the decision outcome."""
        lessons = []
        
        # Correctness lesson
        if was_correct:
            if points_diff > 5:
                lessons.append(f"✅ STRONG validation of {decision.decision_type} logic")
            else:
                lessons.append(f"✅ Correct but close call on {decision.decision_type}")
        else:
            if abs(points_diff) > 5:
                lessons.append(f"❌ {decision.decision_type} logic failed significantly")
            else:
                lessons.append(f"⚠️ Wrong but close - {decision.decision_type} logic questionable")
        
        # Confidence lesson
        if not self._validate_confidence(decision, points_diff):
            if decision.confidence >= 8:
                lessons.append(f"🎯 Overconfident - {decision.confidence}/10 too high")
            else:
                lessons.append(f"🤔 Underconfident - could have been more decisive")
        
        # Decision type specific lessons
        if decision.decision_type == "stack" and was_correct:
            lessons.append("🔗 Stack strategy paid off - correlation worked")
        elif decision.decision_type == "ceiling" and was_correct and points_diff > 3:
            lessons.append("📈 Ceiling play successful - boom achieved")
        elif decision.decision_type == "floor" and was_correct and abs(points_diff) < 2:
            lessons.append("🛡️ Floor play worked - safe choice validated")
        
        return " | ".join(lessons)
    
    def generate_weekly_decision_report(self, week: int, actual_scores: Dict[str, float]) -> str:
        """Generate a comprehensive report on strategic decision effectiveness."""
        # Find and parse pre-game analysis file
        analysis_files = self.find_pregame_analysis_files(week)
        if not analysis_files:
            return f"❌ No pre-game analysis found for Week {week}"
        
        # Use the most recent analysis file
        analysis_file = analysis_files[0]
        decisions = self.parse_pregame_analysis_file(analysis_file, week)
        
        if not decisions:
            return f"⚠️ No strategic decisions found in {analysis_file}"
        
        # Evaluate decisions
        outcomes = self.evaluate_decisions(decisions, actual_scores)
        
        # Generate report
        report_lines = []
        report_lines.append(f"📋 STRATEGIC DECISION ANALYSIS - Week {week}")
        report_lines.append("=" * 60)
        report_lines.append(f"📁 Analysis Source: {os.path.basename(analysis_file)}")
        report_lines.append(f"🎯 Decisions Evaluated: {len(outcomes)}")
        
        correct_decisions = sum(1 for o in outcomes if o.was_correct)
        total_points_impact = sum(o.points_gained_lost for o in outcomes)
        
        report_lines.append(f"✅ Correct Decisions: {correct_decisions}/{len(outcomes)} ({correct_decisions/len(outcomes)*100:.1f}%)")
        report_lines.append(f"📊 Total Points Impact: {total_points_impact:+.1f} points")
        report_lines.append("")
        
        # Individual decision analysis
        for i, outcome in enumerate(outcomes, 1):
            decision = outcome.decision
            report_lines.append(f"## DECISION {i}: {decision.position}")
            report_lines.append(f"**Choice:** {decision.player_started} over {decision.player_benched}")
            report_lines.append(f"**Confidence:** {decision.confidence}/10")
            report_lines.append(f"**Rationale:** {decision.rationale}")
            
            if decision.actual_started is not None and decision.actual_benched is not None:
                report_lines.append(f"**Results:** {decision.player_started} ({decision.actual_started:.1f}) vs {decision.player_benched} ({decision.actual_benched:.1f})")
                report_lines.append(f"**Impact:** {outcome.points_gained_lost:+.1f} points")
                report_lines.append(f"**Status:** {'✅ CORRECT' if outcome.was_correct else '❌ INCORRECT'}")
            else:
                report_lines.append("**Results:** Missing actual performance data")
            
            report_lines.append(f"**Lessons:** {outcome.lessons_learned}")
            report_lines.append("")
        
        # Summary insights
        report_lines.append("## 📈 STRATEGIC INSIGHTS")
        
        # Confidence analysis
        overconfident = sum(1 for o in outcomes if not o.confidence_validated and o.decision.confidence >= 8)
        if overconfident > 0:
            report_lines.append(f"🎯 {overconfident} decisions were overconfident - be more conservative")
        
        # Decision type analysis
        decision_types = {}
        for outcome in outcomes:
            dt = outcome.decision.decision_type
            if dt not in decision_types:
                decision_types[dt] = {'total': 0, 'correct': 0, 'points': 0}
            decision_types[dt]['total'] += 1
            if outcome.was_correct:
                decision_types[dt]['correct'] += 1
            decision_types[dt]['points'] += outcome.points_gained_lost
        
        for dt, stats in decision_types.items():
            success_rate = stats['correct'] / stats['total'] * 100
            report_lines.append(f"📊 {dt.upper()} strategy: {stats['correct']}/{stats['total']} ({success_rate:.1f}%) - {stats['points']:+.1f} pts")
        
        return "\n".join(report_lines)