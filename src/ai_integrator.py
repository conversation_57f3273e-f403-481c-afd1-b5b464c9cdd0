#!/usr/bin/env python3
"""
AI Integration via Bash Piping
Integrates with claude-code and gemini-cli for fantasy analysis
"""

import subprocess
import os
import tempfile
from typing import Optional
from datetime import datetime

class AIIntegrator:
    """Integrates with local AI CLI tools via bash piping"""
    
    def __init__(self):
        self.claude_available = self._check_claude_availability()
        self.gemini_available = self._check_gemini_availability()
        
    def _check_claude_availability(self) -> bool:
        """Check if claude is available"""
        claude_path = os.path.expanduser("~/.claude/local/claude")
        try:
            # Check direct path first
            if os.path.exists(claude_path):
                result = subprocess.run([claude_path, '--help'], capture_output=True, text=True, timeout=5)
                return result.returncode == 0
            
            # Fallback to command check
            result = subprocess.run(['claude', '--help'], capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except:
            return False
    
    def _check_gemini_availability(self) -> bool:
        """Check if gemini-cli is available"""
        try:
            result = subprocess.run(['which', 'gemini'], capture_output=True, text=True)
            return result.returncode == 0
        except:
            return False
    
    def analyze_with_ai(self, prompt: str, model: str = "claude", focus: str = "all") -> Optional[str]:
        """Send prompt to AI via bash piping"""
        
        if model == "claude" and not self.claude_available:
            print("❌ claude not found in PATH")
            return None
        elif model == "gemini" and not self.gemini_available:
            print("❌ gemini-cli not found in PATH")
            return None
            
        try:
            # Create temporary file for prompt
            with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as tmp_file:
                tmp_file.write(prompt)
                tmp_file_path = tmp_file.name
            
            # Run appropriate AI command
            if model == "claude":
                claude_path = os.path.expanduser("~/.claude/local/claude")
                if os.path.exists(claude_path):
                    cmd = f"{claude_path} < {tmp_file_path}"
                else:
                    cmd = f"claude < {tmp_file_path}"
            elif model == "gemini":
                cmd = f"gemini < {tmp_file_path}"
            else:
                return None
                
            print(f"🚀 Running: {cmd}")
            
            # Execute command
            result = subprocess.run(
                cmd, 
                shell=True, 
                capture_output=True, 
                text=True,
                timeout=120  # 2 minute timeout
            )
            
            # Clean up temp file
            os.unlink(tmp_file_path)
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                print(f"❌ AI command failed: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            print("❌ AI analysis timed out after 2 minutes")
            return None
        except Exception as e:
            print(f"❌ AI integration error: {e}")
            return None
    
    def get_available_models(self) -> list:
        """Get list of available AI models"""
        models = []
        if self.claude_available:
            models.append("claude")
        if self.gemini_available:
            models.append("gemini")
        return models
    
    def analyze_pre_waiver(self, prompt: str, model: str = "claude") -> Optional[str]:
        """Specialized pre-waiver analysis"""
        enhanced_prompt = f"""🏈 **PRE-WAIVER STRATEGIC ANALYSIS**

You are a fantasy football expert analyzing waiver wire opportunities. Focus on:

1. **Immediate Impact Players**: Who can help THIS WEEK
2. **Value vs Competition**: Players likely to be claimed vs available  
3. **Roster Construction**: Position needs and depth requirements
4. **Week-Ahead Setup**: Players with next week advantages
5. **Drop Candidates**: Safe players to release

{prompt}

**Provide specific waiver priority rankings (1-10) with reasoning for each target.**
"""
        return self.analyze_with_ai(enhanced_prompt, model)
    
    def analyze_pre_game(self, prompt: str, model: str = "gemini") -> Optional[str]:
        """Specialized pre-game lineup analysis"""
        enhanced_prompt = f"""🏈 **PRE-GAME LINEUP OPTIMIZATION**

You are a fantasy football expert analyzing start/sit decisions. Focus on:

1. **Game Script Analysis**: How games will likely unfold
2. **Ceiling vs Floor**: When to prioritize upside vs safety  
3. **Matchup Advantages**: Specific player/defense mismatches
4. **Weather/Injury Updates**: Late-breaking information impact
5. **Tournament vs Cash**: Optimal strategy based on league format

{prompt}

**Provide specific start/sit recommendations with confidence levels (1-10) and reasoning.**
"""
        return self.analyze_with_ai(enhanced_prompt, model)
    
    def analyze_post_waiver(self, prompt: str, model: str = "claude") -> Optional[str]:
        """Specialized post-waiver analysis"""
        enhanced_prompt = f"""🏈 **POST-WAIVER LEAGUE ANALYSIS**

You are a fantasy football expert analyzing waiver results and league movement. Focus on:

1. **League Winners/Losers**: Who improved most via waivers
2. **Missed Opportunities**: Players you should have targeted  
3. **Trade Windows**: New trade opportunities based on rosters
4. **Competitive Landscape**: How the league balance shifted
5. **Next Week Setup**: Early planning for upcoming waivers

{prompt}

**Provide strategic insights on league positioning and next steps.**
"""
        return self.analyze_with_ai(enhanced_prompt, model)
    
    def analyze_post_week(self, prompt: str, model: str = "claude") -> Optional[str]:
        """Specialized post-week strategic analysis"""
        enhanced_prompt = f"""🏈 **POST-WEEK STRATEGIC VALIDATION ANALYSIS**

You are a fantasy football expert analyzing the effectiveness of strategic decisions. Focus on:

1. **Strategic Decision Validation**: Were start/sit decisions correct and why?
2. **Projection Source Analysis**: Which projection sources were most reliable?
3. **Confidence Calibration**: Were confidence levels appropriate for outcomes?
4. **Strategic Pattern Recognition**: What decision types worked best?
5. **Future Improvement**: How to enhance decision-making process?
6. **Lessons Learned**: Specific actionable insights for upcoming weeks

{prompt}

**Provide specific, data-driven insights for improving future fantasy decisions. Focus on patterns, biases, and strategic adjustments rather than just recapping results.**
"""
        return self.analyze_with_ai(enhanced_prompt, model)
    
    def analyze_with_historical_context(self, prompt: str, report_type: str, week: int, year: int, model: str = "claude") -> Optional[str]:
        """Enhanced analysis with historical context integration."""
        try:
            from .report_storage_manager import ReportStorageManager
            from .historical_analyzer import HistoricalAnalyzer
            
            # Get historical context
            storage = ReportStorageManager()
            analyzer = HistoricalAnalyzer(storage)
            historical_context = analyzer.generate_enhanced_context(week, year)
            
            # Enhance prompt with historical context
            enhanced_prompt = f"""🏈 **FANTASY FOOTBALL ANALYSIS WITH HISTORICAL CONTEXT**

{historical_context}

## 📊 CURRENT WEEK ANALYSIS

{prompt}

**IMPORTANT**: Use the historical context above to inform your analysis. Reference specific lessons learned, decision patterns, and player trends when making recommendations. Focus on continuous improvement based on what has worked (and what hasn't) in recent weeks.
"""
            
            return self.analyze_with_ai(enhanced_prompt, model)
            
        except Exception as e:
            print(f"⚠️ Historical context unavailable: {e}")
            # Fall back to regular analysis
            return self.analyze_with_ai(prompt, model)


def main():
    """Test AI integration"""
    integrator = AIIntegrator()
    
    print("🤖 AI Integration Test")
    print(f"Claude available: {integrator.claude_available}")
    print(f"Gemini available: {integrator.gemini_available}")
    print(f"Available models: {integrator.get_available_models()}")
    
    if integrator.claude_available or integrator.gemini_available:
        test_prompt = "Analyze this fantasy situation: Should I start Player A (10 proj) or Player B (12 proj) this week?"
        
        model = "claude" if integrator.claude_available else "gemini"
        print(f"\n🧠 Testing {model} integration...")
        
        response = integrator.analyze_with_ai(test_prompt, model)
        if response:
            print("✅ AI integration working!")
            print(f"Response: {response[:200]}...")
        else:
            print("❌ AI integration failed")

if __name__ == "__main__":
    main()