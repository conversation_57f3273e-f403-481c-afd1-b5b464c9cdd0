"""
Report Storage Manager - Archives fantasy analysis reports with historical context integration.

Uses hybrid approach:
- DuckDB for metadata, timestamps, and queryable analysis data
- File system for actual report content organized by type/week
- Enables historical context integration and pattern analysis
"""

import os
import json
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any
from dataclasses import dataclass, asdict
from enum import Enum
import duckdb

class ReportType(Enum):
    PRE_WAIVER = "pre_waiver"
    POST_WAIVER = "post_waiver" 
    PRE_GAME = "pre_game"
    POST_WEEK = "post_week"
    QUICK = "quick"
    STATUS = "status"

@dataclass
class ReportMetadata:
    """Metadata for a stored fantasy analysis report."""
    report_id: str
    report_type: ReportType
    week: int
    year: int
    ai_model: Optional[str]
    created_at: datetime
    file_path: str
    
    # Analysis content metadata
    key_decisions: List[str]
    strategic_insights: List[str]
    player_mentions: List[str]
    
    # Performance tracking
    accuracy_data: Optional[Dict[str, float]] = None
    decision_outcomes: Optional[Dict[str, Any]] = None
    
    # Context linking
    references_reports: List[str] = None  # IDs of reports this one references
    referenced_by: List[str] = None      # IDs of reports that reference this one

@dataclass
class HistoricalContext:
    """Historical context for AI analysis integration."""
    previous_week_insights: List[str]
    recurring_patterns: List[str]
    decision_track_record: Dict[str, float]  # decision_type -> success_rate
    player_performance_history: Dict[str, List[float]]  # player -> weekly scores
    lessons_learned: List[str]

class ReportStorageManager:
    """Manages storage and retrieval of fantasy analysis reports."""
    
    def __init__(self, base_dir: str = "reports", db_path: str = "data/projections_analytics.duckdb"):
        self.base_dir = Path(base_dir)
        self.db_path = db_path
        self._ensure_directories()
        self._init_database()
    
    def _ensure_directories(self):
        """Create necessary directory structure."""
        self.base_dir.mkdir(exist_ok=True)
        
        # Create year directories for current and next year
        current_year = datetime.now().year
        for year in [current_year, current_year + 1]:
            year_dir = self.base_dir / str(year)
            year_dir.mkdir(exist_ok=True)
    
    def _init_database(self):
        """Initialize DuckDB tables for report metadata."""
        with duckdb.connect(self.db_path) as conn:
            # Reports metadata table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS report_metadata (
                    report_id VARCHAR PRIMARY KEY,
                    report_type VARCHAR NOT NULL,
                    week INTEGER NOT NULL,
                    year INTEGER NOT NULL,
                    ai_model VARCHAR,
                    created_at TIMESTAMP NOT NULL,
                    file_path VARCHAR NOT NULL,
                    
                    -- Analysis content (JSON)
                    key_decisions JSON,
                    strategic_insights JSON,
                    player_mentions JSON,
                    
                    -- Performance tracking (JSON)
                    accuracy_data JSON,
                    decision_outcomes JSON,
                    
                    -- Context linking
                    references_reports JSON,
                    referenced_by JSON
                )
            """)
            
            # Historical performance table for queryable player data
            conn.execute("""
                CREATE TABLE IF NOT EXISTS player_performance_history (
                    player_name VARCHAR NOT NULL,
                    week INTEGER NOT NULL,
                    year INTEGER NOT NULL,
                    actual_points DOUBLE,
                    projected_points DOUBLE,
                    source VARCHAR,
                    was_recommended BOOLEAN DEFAULT FALSE,
                    recommendation_confidence INTEGER,
                    created_at TIMESTAMP NOT NULL,
                    PRIMARY KEY (player_name, week, year, source)
                )
            """)
            
            # Decision outcomes tracking
            conn.execute("""
                CREATE TABLE IF NOT EXISTS decision_outcomes (
                    decision_id VARCHAR PRIMARY KEY,
                    report_id VARCHAR NOT NULL,
                    decision_type VARCHAR NOT NULL,  -- 'start_sit', 'waiver_add', 'stack', etc.
                    player_involved VARCHAR NOT NULL,
                    confidence_level INTEGER,
                    was_correct BOOLEAN,
                    points_impact DOUBLE,
                    week INTEGER NOT NULL,
                    year INTEGER NOT NULL,
                    created_at TIMESTAMP NOT NULL,
                    
                    FOREIGN KEY (report_id) REFERENCES report_metadata(report_id)
                )
            """)
    
    def store_report(self, 
                    content: str,
                    report_type: ReportType, 
                    week: int,
                    year: int,
                    ai_model: Optional[str] = None,
                    key_decisions: List[str] = None,
                    strategic_insights: List[str] = None,
                    player_mentions: List[str] = None,
                    accuracy_data: Optional[Dict[str, float]] = None,
                    decision_outcomes: Optional[Dict[str, Any]] = None) -> str:
        """Store a fantasy analysis report with metadata."""
        
        # Generate unique report ID with timestamp
        timestamp = datetime.now()
        report_id = f"{report_type.value}_{year}_w{week}_{timestamp.strftime('%Y%m%d_%H%M%S')}"
        
        # Create file path
        year_dir = self.base_dir / str(year)
        week_dir = year_dir / f"week_{week}"
        week_dir.mkdir(exist_ok=True)
        
        filename = f"{report_type.value}"
        if ai_model:
            filename += f"_{ai_model}"
        filename += f"_{timestamp.strftime('%H%M%S')}.md"
        
        file_path = week_dir / filename
        
        # Write report content to file
        with open(file_path, 'w') as f:
            f.write(content)
        
        # Store metadata in database
        metadata = ReportMetadata(
            report_id=report_id,
            report_type=report_type,
            week=week,
            year=year,
            ai_model=ai_model,
            created_at=timestamp,
            file_path=str(file_path),
            key_decisions=key_decisions or [],
            strategic_insights=strategic_insights or [],
            player_mentions=player_mentions or [],
            accuracy_data=accuracy_data,
            decision_outcomes=decision_outcomes,
            references_reports=[],
            referenced_by=[]
        )
        
        self._store_metadata(metadata)
        
        return report_id
    
    def _store_metadata(self, metadata: ReportMetadata):
        """Store report metadata in DuckDB."""
        with duckdb.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO report_metadata VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                metadata.report_id,
                metadata.report_type.value,
                metadata.week,
                metadata.year,
                metadata.ai_model,
                metadata.created_at,
                metadata.file_path,
                json.dumps(metadata.key_decisions),
                json.dumps(metadata.strategic_insights),
                json.dumps(metadata.player_mentions),
                json.dumps(metadata.accuracy_data) if metadata.accuracy_data else None,
                json.dumps(metadata.decision_outcomes) if metadata.decision_outcomes else None,
                json.dumps(metadata.references_reports),
                json.dumps(metadata.referenced_by)
            ))
    
    def get_report(self, report_id: str) -> Optional[tuple[str, ReportMetadata]]:
        """Retrieve a report by ID."""
        with duckdb.connect(self.db_path) as conn:
            result = conn.execute("""
                SELECT * FROM report_metadata WHERE report_id = ?
            """, (report_id,)).fetchone()
            
            if not result:
                return None
            
            # Parse metadata
            metadata = self._parse_metadata_row(result)
            
            # Read content from file
            if os.path.exists(metadata.file_path):
                with open(metadata.file_path, 'r') as f:
                    content = f.read()
                return content, metadata
            else:
                print(f"Warning: Report file not found: {metadata.file_path}")
                return None, metadata
    
    def get_reports_for_week(self, week: int, year: int) -> List[tuple[str, ReportMetadata]]:
        """Get all reports for a specific week."""
        with duckdb.connect(self.db_path) as conn:
            results = conn.execute("""
                SELECT * FROM report_metadata 
                WHERE week = ? AND year = ? 
                ORDER BY created_at DESC
            """, (week, year)).fetchall()
            
            reports = []
            for row in results:
                metadata = self._parse_metadata_row(row)
                
                # Read content from file
                if os.path.exists(metadata.file_path):
                    with open(metadata.file_path, 'r') as f:
                        content = f.read()
                    reports.append((content, metadata))
                else:
                    reports.append(("Content not found", metadata))
            
            return reports
    
    def get_latest_report(self, report_type: ReportType, week: int, year: int) -> Optional[tuple[str, ReportMetadata]]:
        """Get the most recent report of a specific type for a week."""
        with duckdb.connect(self.db_path) as conn:
            result = conn.execute("""
                SELECT * FROM report_metadata 
                WHERE report_type = ? AND week = ? AND year = ? 
                ORDER BY created_at DESC 
                LIMIT 1
            """, (report_type.value, week, year)).fetchone()
            
            if not result:
                return None
            
            metadata = self._parse_metadata_row(result)
            
            # Read content from file
            if os.path.exists(metadata.file_path):
                with open(metadata.file_path, 'r') as f:
                    content = f.read()
                return content, metadata
            else:
                return None, metadata
    
    def get_historical_context(self, week: int, year: int, weeks_back: int = 3) -> HistoricalContext:
        """Generate historical context for AI analysis."""
        with duckdb.connect(self.db_path) as conn:
            # Get insights from previous weeks
            previous_insights = []
            for w in range(max(1, week - weeks_back), week):
                insights = conn.execute("""
                    SELECT strategic_insights FROM report_metadata 
                    WHERE week = ? AND year = ? AND strategic_insights IS NOT NULL
                    ORDER BY created_at DESC
                """, (w, year)).fetchall()
                
                for row in insights:
                    insight_list = json.loads(row[0]) if row[0] else []
                    previous_insights.extend(insight_list)
            
            # Get decision track record
            decision_outcomes = conn.execute("""
                SELECT decision_type, 
                       AVG(CASE WHEN was_correct THEN 1.0 ELSE 0.0 END) as success_rate,
                       COUNT(*) as total_decisions
                FROM decision_outcomes 
                WHERE year = ? AND week < ?
                GROUP BY decision_type
                HAVING total_decisions >= 2
            """, (year, week)).fetchall()
            
            track_record = {}
            for row in decision_outcomes:
                track_record[row[0]] = row[1]  # decision_type -> success_rate
            
            # Get player performance history
            player_history = conn.execute("""
                SELECT player_name, actual_points
                FROM player_performance_history 
                WHERE year = ? AND week < ? AND week >= ?
                ORDER BY player_name, week
            """, (year, week, max(1, week - weeks_back))).fetchall()
            
            player_perf = {}
            for row in player_history:
                player_name, score = row
                if player_name not in player_perf:
                    player_perf[player_name] = []
                player_perf[player_name].append(score)
            
            return HistoricalContext(
                previous_week_insights=previous_insights[:10],  # Limit to top 10
                recurring_patterns=[],  # TODO: Implement pattern detection
                decision_track_record=track_record,
                player_performance_history=player_perf,
                lessons_learned=[]  # TODO: Extract from strategic insights
            )
    
    def _parse_metadata_row(self, row) -> ReportMetadata:
        """Parse a database row into ReportMetadata."""
        return ReportMetadata(
            report_id=row[0],
            report_type=ReportType(row[1]),
            week=row[2],
            year=row[3],
            ai_model=row[4],
            created_at=row[5],
            file_path=row[6],
            key_decisions=json.loads(row[7]) if row[7] else [],
            strategic_insights=json.loads(row[8]) if row[8] else [],
            player_mentions=json.loads(row[9]) if row[9] else [],
            accuracy_data=json.loads(row[10]) if row[10] else None,
            decision_outcomes=json.loads(row[11]) if row[11] else None,
            references_reports=json.loads(row[12]) if row[12] else [],
            referenced_by=json.loads(row[13]) if row[13] else []
        )
    
    def store_player_performance(self, player_name: str, week: int, year: int, 
                                actual_points: float, projected_points: float = None, 
                                source: str = "espn", was_recommended: bool = False,
                                recommendation_confidence: int = None):
        """Store player performance data for historical analysis."""
        with duckdb.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO player_performance_history 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                player_name, week, year, actual_points, projected_points,
                source, was_recommended, recommendation_confidence, datetime.now()
            ))
    
    def store_decision_outcome(self, report_id: str, decision_type: str, 
                             player_involved: str, confidence_level: int,
                             was_correct: bool, points_impact: float,
                             week: int, year: int):
        """Store the outcome of a strategic decision."""
        decision_id = f"{report_id}_{decision_type}_{player_involved}_{datetime.now().strftime('%H%M%S')}"
        
        with duckdb.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO decision_outcomes VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                decision_id, report_id, decision_type, player_involved,
                confidence_level, was_correct, points_impact,
                week, year, datetime.now()
            ))
    
    def get_report_summary(self, year: int) -> Dict[str, Any]:
        """Get summary statistics for all reports in a year."""
        with duckdb.connect(self.db_path) as conn:
            summary = conn.execute("""
                SELECT 
                    COUNT(*) as total_reports,
                    COUNT(DISTINCT week) as weeks_covered,
                    COUNT(DISTINCT report_type) as report_types,
                    MAX(week) as latest_week,
                    MIN(created_at) as first_report_date,
                    MAX(created_at) as latest_report_date
                FROM report_metadata 
                WHERE year = ?
            """, (year,)).fetchone()
            
            decision_summary = conn.execute("""
                SELECT 
                    decision_type,
                    COUNT(*) as total_decisions,
                    AVG(CASE WHEN was_correct THEN 1.0 ELSE 0.0 END) as success_rate,
                    AVG(points_impact) as avg_points_impact
                FROM decision_outcomes 
                WHERE year = ?
                GROUP BY decision_type
                ORDER BY total_decisions DESC
            """, (year,)).fetchall()
            
            return {
                'total_reports': summary[0],
                'weeks_covered': summary[1],
                'report_types': summary[2],
                'latest_week': summary[3],
                'first_report_date': summary[4],
                'latest_report_date': summary[5],
                'decision_performance': [
                    {
                        'type': row[0],
                        'total': row[1], 
                        'success_rate': f"{row[2]*100:.1f}%" if row[2] else "N/A",
                        'avg_impact': f"{row[3]:+.1f} pts" if row[3] else "N/A"
                    }
                    for row in decision_summary
                ]
            }