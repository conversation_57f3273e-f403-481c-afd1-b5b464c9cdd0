import pandas as pd
from typing import Dict, Any, Optional

class HtmlBuilder:
    """
    Builds HTML components for fantasy football reports,
    including player rows, position sections, and total points displays.
    """

    def __init__(self):
        pass

    def _generate_player_row(self, player: pd.Series, include_position: bool = False, include_salary: bool = False, is_start_sit_report: bool = False, slot_name: str = None) -> str:
        """Generate HTML table row for a single player."""
        is_on_team = player.get('on_team', False)
        
        # Determine priority class and styling
        rec = player.get('recommendation', '')
        row_class = ''
        rec_class = ''

        if is_start_sit_report:
            if 'BYE' in rec:
                row_class = 'bye-player'
                rec_class = 'rec-bye'
            elif 'INJURED' in rec:
                row_class = 'injured-player'
                rec_class = 'rec-injured'
            elif 'START' in rec:
                row_class = 'roster-starting'
                rec_class = 'rec-start'
            elif 'BENCH' in rec:
                row_class = 'roster-bench'
                rec_class = 'rec-bench'
            else: # Fallback for any unhandled start/sit recommendations
                row_class = 'roster-bench'
                rec_class = 'rec-bench'
        else: # Waiver wire report logic
            if is_on_team:
                if 'HOLD - Starting' in rec:
                    row_class = 'roster-starting'
                    rec_class = 'rec-roster'
                else:
                    row_class = 'roster-bench'
                    rec_class = 'rec-roster'
            elif 'HIGH PRIORITY' in rec:
                row_class = 'high-priority'
                rec_class = 'rec-high'
            elif 'MEDIUM PRIORITY' in rec:
                row_class = 'medium-priority'
                rec_class = 'rec-medium'
            else:
                row_class = 'low-priority'
                rec_class = 'rec-low'
        
        # Format projections
        espn_proj = player.get('projected_points', 0)
        fd_proj = player.get('external_proj', 0)
        fd_display = "-" if fd_proj == 0 else f"{fd_proj:.1f}"
        
        # Get opponent data
        opponent = player.get('fd_opponent', '') or player.get('pro_opponent', '')
        
        # Base row content
        row_content = f"""
                <tr class="{row_class}">"""
        
        # Add slot name if provided (for optimal lineup)
        if slot_name:
            row_content += f"""
                    <td>{slot_name}</td>"""

        row_content += f"""
                    <td class="player-name">{player.get('name', '')}</td>"""
        
        # Add position column if requested (for flex section)
        if include_position:
            row_content += f"""
                    <td class="team-name">{player.get('position', '')}</td>"""
        
        # Continue with standard columns
        row_content += f"""
                    <td class="team-name">{player.get('proTeam', '')}</td>
                    <td class="team-name">{opponent}</td>
                    <td class="proj-points">{espn_proj:.1f}</td>
                    <td class="proj-points">{fd_display}</td>"""
        
        # Add salary columns if requested
        if include_salary:
            fd_salary = self._format_salary(player.get('fd_salary', '-'))
            fd_value = self._format_value(player.get('fd_value', '-'))
            row_content += f"""
                    <td class="proj-points">{fd_salary}</td>
                    <td class="proj-points">{fd_value}</td>"""
        
        # Add injury and bye columns for start/sit report
        if is_start_sit_report:
            injury_status = player.get('injury_status', '')
            bye_week = player.get('bye_week', '')
            row_content += f"""
                    <td>{injury_status if injury_status else '-'}</td>
                    <td>{bye_week if bye_week else '-'}</td>"""
        else: # For waiver wire report, include ownership
            row_content += f"""
                    <td class="ownership">{player.get('percent_owned', 0):.1f}%</td>"""

        # Finish row
        row_content += f"""
                    <td><span class="recommendation {rec_class}">{rec.replace(' - ', ' • ')}</span></td>
                </tr>"""
        
        return row_content
    
    def _format_salary(self, salary) -> str:
        """Format FanDuel salary for display."""
        if salary and salary != 'N/A' and salary != '-':
            return salary.replace('$$', '$')  # Clean up double dollar signs
        return '-'
    
    def _format_value(self, value) -> str:
        """Format FanDuel value for display."""
        if value and value != 'N/A' and value != '-':
            try:
                return f"{float(value):.2f}"
            except:
                return '-'
        return '-'

    def generate_position_section_content(self, position: str, available_players: pd.DataFrame, roster_players: pd.DataFrame) -> str:
        """Generate HTML content for a single position section."""
        # Filter players by position
        pos_roster = roster_players[roster_players['position'] == position] if not roster_players.empty else pd.DataFrame()
        pos_available = available_players[available_players['position'] == position] if not available_players.empty else pd.DataFrame()
        
        # Limit available players and sort
        if not pos_available.empty:
            pos_available = pos_available.copy()
            pos_available['sort_proj'] = pos_available['external_proj'].fillna(0).where(
                pos_available['external_proj'].notna(), pos_available['projected_points'])
            pos_available = pos_available.sort_values('sort_proj', ascending=False).head(10)
        
        # Combine roster and available players
        all_pos_players = pd.concat([pos_roster, pos_available], ignore_index=True) if not pos_roster.empty or not pos_available.empty else pd.DataFrame()
        
        if all_pos_players.empty:
            return ""
        
        # Sort by projection
        all_pos_players = all_pos_players.copy()
        all_pos_players['sort_proj'] = all_pos_players['external_proj'].fillna(0).where(
            all_pos_players['external_proj'].notna(), all_pos_players['projected_points'])
        all_pos_players = all_pos_players.sort_values('sort_proj', ascending=False)
        
        # Generate section HTML with salary columns for all positions
        html_content = f"""
    <div class="position-section">
        <h3 class="position-header">{position}</h3>
        <table>
            <thead>
                <tr>
                    <th>Player</th>
                    <th>Team</th>
                    <th>Opp</th>
                    <th>ESPN</th>
                    <th>FD</th>
                    <th>FD Salary</th>
                    <th>FD Value</th>
                    <th>Own %</th>
                    <th>Recommendation</th>
                </tr>
            </thead>
            <tbody>
"""
        
        for _, player in all_pos_players.iterrows():
            html_content += self._generate_player_row(player, include_position=False, include_salary=True)
        
        html_content += """
            </tbody>
        </table>
    </div>
"""
        return html_content

    def generate_flex_section_content(self, available_players: pd.DataFrame, roster_players: pd.DataFrame) -> str:
        """Generate unified FLEX section for RB/WR/TE comparison."""
        flex_positions = ['RB', 'WR', 'TE']
        flex_roster = roster_players[roster_players['position'].isin(flex_positions)] if not roster_players.empty else pd.DataFrame()
        flex_available = available_players[available_players['position'].isin(flex_positions)] if not available_players.empty else pd.DataFrame()
        
        if not flex_available.empty:
            # Sort by projection and limit to top 20 available flex players
            flex_available = flex_available.copy()
            flex_available['sort_proj'] = flex_available['external_proj'].fillna(0).where(
                flex_available['external_proj'].notna(), flex_available['projected_points'])
            flex_available = flex_available.sort_values('sort_proj', ascending=False).head(20)
        
        # Combine roster and available flex players
        all_flex_players = pd.concat([flex_roster, flex_available], ignore_index=True) if not flex_roster.empty or not flex_available.empty else pd.DataFrame()
        
        if all_flex_players.empty:
            return ""
        
        # Sort by projection
        all_flex_players = all_flex_players.copy()
        all_flex_players['sort_proj'] = all_flex_players['external_proj'].fillna(0).where(
            all_flex_players['external_proj'].notna(), all_flex_players['projected_points'])
        all_flex_players = all_flex_players.sort_values('sort_proj', ascending=False)
        
        html_content = """
    <div class="position-section">
        <h3 class="position-header"> FLEX (RB/WR/TE) - Cross-Position Rankings</h3>
        <table>
            <thead>
                <tr>
                    <th>Player</th>
                    <th>Pos</th>
                    <th>Team</th>
                    <th>Opp</th>
                    <th>ESPN</th>
                    <th>FD</th>
                    <th>FD Salary</th>
                    <th>FD Value</th>
                    <th>Own %</th>
                    <th>Recommendation</th>
                </tr>
            </thead>
            <tbody>
"""
        
        for _, player in all_flex_players.iterrows():
            html_content += self._generate_player_row(player, include_position=True, include_salary=True)
        
        html_content += """
            </tbody>
        </table>
    </div>
"""
        return html_content

    def generate_optimal_lineup_section_content(self, optimal_lineup: Dict[str, pd.DataFrame], title: str) -> str:
        """Generate sections for the optimal lineup."""
        html_content = f"""
    <div class="position-section">
        <h3 class="position-header">{title}</h3>
        <table>
            <thead>
                <tr>
                    <th>Slot</th>
                    <th>Player</th>
                    <th>Team</th>
                    <th>Opp</th>
                    <th>ESPN</th>
                    <th>FD</th>
                    <th>FD Salary</th>
                    <th>FD Value</th>
                    <th>Injury</th>
                    <th>Bye</th>
                    <th>Recommendation</th>
                </tr>
            </thead>
            <tbody>
"""
        # Define the order of slots for display
        display_order = ['QB', 'RB1', 'RB2', 'WR1', 'WR2', 'TE', 'FLEX', 'K', 'D/ST']

        for slot_name in display_order:
            player_df = optimal_lineup.get(slot_name)
            if player_df is not None and not player_df.empty:
                player = player_df.iloc[0].copy() # Create a copy
                # Override recommendation for display in optimal lineup
                player['recommendation'] = f"START • {slot_name}"
                html_content += self._generate_player_row(player, include_position=False, include_salary=True, is_start_sit_report=True, slot_name=slot_name)
            else:
                html_content += f"""
                <tr>
                    <td>{slot_name}</td>
                    <td colspan="10">No player recommended for this slot.</td>
                </tr>
"""
        html_content += """
            </tbody>
        </table>
    </div>
"""
        return html_content

    def generate_bench_section_content(self, bench_players: pd.DataFrame, title: str) -> str:
        """Generate sections for bench players."""
        if bench_players.empty:
            return ""

        html_content = f"""
    <div class="position-section">
        <h3 class="position-header">{title}</h3>
        <table>
            <thead>
                <tr>
                    <th>Player</th>
                    <th>Pos</th>
                    <th>Team</th>
                    <th>Opp</th>
                    <th>ESPN</th>
                    <th>FD</th>
                    <th>FD Salary</th>
                    <th>FD Value</th>
                    <th>Injury</th>
                    <th>Bye</th>
                    <th>Recommendation</th>
                </tr>
            </thead>
            <tbody>
"""
        # Sort bench players by projected points
        bench_players = bench_players.copy()
        bench_players['sort_proj'] = bench_players['external_proj'].fillna(0).where(
            bench_players['external_proj'].notna(), bench_players['projected_points'])
        bench_players = bench_players.sort_values('sort_proj', ascending=False)

        for _, player in bench_players.iterrows():
            # Override recommendation for display in bench section
            player['recommendation'] = "BENCH"
            html_content += self._generate_player_row(player, include_position=True, include_salary=True, is_start_sit_report=True)

        html_content += """
            </tbody>
        </table>
    </div>
"""
        return html_content

    def generate_total_points_section_content(self, total_points: float, title: str) -> str:
        """Generate a section displaying the total projected points."""
        return f"""
    <div class="position-section">
        <h3 class="position-header">{title}</h3>
        <div style="padding: 15px 20px; font-size: 24px; font-weight: bold; text-align: center; color: #007bff;">
            {total_points:.1f} Points
        </div>
    </div>
"""