import requests
import pandas as pd
from typing import Dict, List, Optional
import os
from datetime import datetime
import json

class WinWithOddsProjections:
    """
    Fetches season-long projections from WinWithOdds.com public data.
    This provides comprehensive statistical projections for all positions.
    """
    
    def __init__(self):
        self.base_url = "https://winwithodds.com"
        self.cache_dir = "cache"
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Position mappings for consistency
        self.position_map = {
            'QB': 'QB',
            'RB': 'RB', 
            'WR': 'WR',
            'TE': 'TE',
            'K': 'K',
            'DEF': 'D/ST',
            'DST': 'D/ST'
        }
        
        # Cache the loaded projections to avoid repeated calls
        self._cached_projections = None
    
    def fetch_season_long_projections(self, force_refresh: bool = False) -> pd.DataFrame:
        """
        Fetch season-long projections from WinWithOdds.
        
        Args:
            force_refresh: If True, bypass cache and fetch fresh data
            
        Returns:
            DataFrame with columns: name, position, various statistical projections
        """
        # Return cached projections if available and not forcing refresh
        if not force_refresh and self._cached_projections is not None:
            return self._cached_projections
            
        cache_file = f"{self.cache_dir}/winwithodds_season_projections.json"
        
        # Check cache first
        if not force_refresh and os.path.exists(cache_file):
            try:
                cache_age_hours = (datetime.now().timestamp() - os.path.getmtime(cache_file)) / 3600
                if cache_age_hours < 24:  # Cache for 24 hours
                    print(f"Loading WinWithOdds projections from cache (age: {cache_age_hours:.1f} hours)")
                    with open(cache_file, 'r') as f:
                        data = json.load(f)
                    df = pd.DataFrame(data)
                    # Ensure cached data is cleaned and normalized
                    df = self._clean_projections_data(df)
                    self._cached_projections = df  # Cache in memory
                    return df
            except Exception as e:
                print(f"Cache read error: {e}")
        
        print("Fetching fresh WinWithOdds season projections...")
        
        try:
            # Try to fetch the CSV data directly
            csv_url = f"{self.base_url}/season_long_full_stats.csv"
            
            # First try direct CSV download
            response = requests.get(csv_url, timeout=15)
            if response.status_code == 200:
                # Parse CSV content
                from io import StringIO
                df = pd.read_csv(StringIO(response.text))
                print(f"✅ Successfully fetched {len(df)} WinWithOdds projections from CSV")
            else:
                # Fallback: scrape the table from the page
                df = self._scrape_projections_table()
                
            if df.empty:
                print("❌ No WinWithOdds data retrieved")
                return pd.DataFrame()
                
            # Clean and standardize the data (apply to both CSV and scraped data)
            df = self._clean_projections_data(df)
            
            # Cache the results
            try:
                with open(cache_file, 'w') as f:
                    json.dump(df.to_dict('records'), f, indent=2)
                print(f"📁 Cached WinWithOdds projections to {cache_file}")
            except Exception as e:
                print(f"Cache write warning: {e}")
            
            # Cache in memory
            self._cached_projections = df
            return df
            
        except Exception as e:
            print(f"❌ Error fetching WinWithOdds projections: {e}")
            return pd.DataFrame()
    
    def _scrape_projections_table(self) -> pd.DataFrame:
        """Scrape the projections table from the webpage"""
        try:
            import requests
            from bs4 import BeautifulSoup
            
            response = requests.get(f"{self.base_url}/season_long_full_stats", timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for the data table
            table = soup.find('table')
            if not table:
                print("❌ Could not find data table on WinWithOdds page")
                return pd.DataFrame()
            
            # Extract table data
            headers = []
            header_row = table.find('thead') or table.find('tr')
            if header_row:
                headers = [th.get_text().strip() for th in header_row.find_all(['th', 'td'])]
            
            rows = []
            tbody = table.find('tbody')
            if tbody:
                for row in tbody.find_all('tr'):
                    row_data = [td.get_text().strip() for td in row.find_all(['td', 'th'])]
                    if row_data:
                        rows.append(row_data)
            
            if not rows:
                print("❌ No data rows found in WinWithOdds table")
                return pd.DataFrame()
            
            df = pd.DataFrame(rows, columns=headers if headers else None)
            print(f"✅ Scraped {len(df)} rows from WinWithOdds table")
            return df
            
        except ImportError:
            print("❌ BeautifulSoup not available for web scraping")
            return pd.DataFrame()
        except Exception as e:
            print(f"❌ Error scraping WinWithOdds table: {e}")
            return pd.DataFrame()
    
    def _normalize_name(self, name: str) -> str:
        """
        Normalizes a player name by converting to lowercase, removing punctuation,
        and removing common suffixes (Jr., Sr., III, etc.).
        """
        if not isinstance(name, str):
            return ""
        
        # Convert to lowercase
        name = name.lower()
        
        # Remove punctuation
        name = name.replace('.', '')
        name = name.replace('-', '')
        name = name.replace("'", '')
        
        # Remove common suffixes
        suffixes = [' jr', ' sr', ' ii', ' iii', ' iv', ' v']
        for suffix in suffixes:
            if name.endswith(suffix):
                name = name[:-len(suffix)]
                break
        
        # Remove extra spaces and strip
        name = ' '.join(name.split())
        return name.strip()

    def _clean_projections_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and standardize the projections data"""
        if df.empty:
            return df
            
        # Create a standardized DataFrame
        cleaned_data = []
        
        for _, row in df.iterrows():
            try:
                # Extract basic player info (adjust column names as needed)
                player_name = self._extract_player_name(row)
                player_data = {
                    'name': player_name,
                    'normalized_name': self._normalize_name(player_name),
                    'position': self._extract_position(row),
                    'team': self._extract_team(row),
                    'fantasy_points': 0.0 # Initialize with a default value
                }
                
                # Extract statistical projections
                player_data.update(self._extract_statistics(row))
                
                # Calculate fantasy points if not provided
                # This logic is now handled by prioritizing 'Projections' in _extract_statistics
                # and ensuring it's always a float.
                
                cleaned_data.append(player_data)
                
            except Exception as e:
                print(f"Warning: Error processing row {row}: {e}")
                continue
        
        if not cleaned_data:
            return pd.DataFrame()
            
        result_df = pd.DataFrame(cleaned_data)
        
        # Standardize position names
        if 'position' in result_df.columns:
            result_df['position'] = result_df['position'].map(self.position_map).fillna(result_df['position'])
        
        # Remove rows with missing essential data
        result_df = result_df.dropna(subset=['name'])
        
        print(f"✅ Cleaned WinWithOdds data: {len(result_df)} players")
        return result_df
    
    def _extract_player_name(self, row) -> str:
        """Extract player name from row data"""
        # Try common column names for player names
        name_columns = ['Player', 'Name', 'player', 'name', 'PLAYER']
        
        for col in name_columns:
            if col in row.index and pd.notna(row[col]):
                return str(row[col]).strip()
        
        # If no specific column, try the first non-numeric column
        for col in row.index:
            if pd.notna(row[col]) and not str(row[col]).replace('.', '').isdigit():
                return str(row[col]).strip()
                
        return "Unknown"
    
    def _extract_position(self, row) -> str:
        """Extract position from row data"""
        pos_columns = ['Position', 'Pos', 'position', 'pos', 'POS']
        
        for col in pos_columns:
            if col in row.index and pd.notna(row[col]):
                return str(row[col]).strip().upper()
                
        return "UNKNOWN"
    
    def _extract_team(self, row) -> str:
        """Extract team from row data"""
        team_columns = ['Team', 'Tm', 'team', 'tm', 'TEAM']
        
        for col in team_columns:
            if col in row.index and pd.notna(row[col]):
                return str(row[col]).strip().upper()
                
        return "UNK"
    
    def _extract_statistics(self, row) -> Dict:
        """Extract statistical projections from row data"""
        stats = {}
        
        # Common statistical categories with various possible column names
        stat_mappings = {
            'passing_yards': ['Pass Yds', 'Passing Yards', 'PassYds', 'PY'],
            'passing_tds': ['Pass TDs', 'Passing TDs', 'PassTDs', 'PTD'],
            'passing_attempts': ['Pass Att', 'Passing Attempts', 'PassAtt', 'PA'],
            'passing_completions': ['Pass Comp', 'Completions', 'PassComp', 'PC'],
            'interceptions': ['INT', 'Interceptions', 'Int'],
            
            'rushing_yards': ['Rush Yds', 'Rushing Yards', 'RushYds', 'RY'],
            'rushing_tds': ['Rush TDs', 'Rushing TDs', 'RushTDs', 'RTD'],
            'rushing_attempts': ['Rush Att', 'Rushing Attempts', 'RushAtt', 'RA'],
            
            'receiving_yards': ['Rec Yds', 'Receiving Yards', 'RecYds', 'ReY'],
            'receiving_tds': ['Rec TDs', 'Receiving TDs', 'RecTDs', 'ReTD'],
            'receptions': ['Rec', 'Receptions', 'Targets', 'TAR'],
            
            'field_goals': ['FG', 'Field Goals', 'FGs'],
            'extra_points': ['XP', 'Extra Points', 'XPs'],
            
            'fantasy_points': ['Projections', 'Fantasy Points', 'FP', 'Points', 'Proj Points']
        }
        
        for stat_key, possible_columns in stat_mappings.items():
            for col in possible_columns:
                if col in row.index and pd.notna(row[col]):
                    try:
                        stats[stat_key] = float(row[col])
                        break
                    except (ValueError, TypeError):
                        continue
        
        return stats
    
    def get_player_season_projection(self, player_name: str) -> Optional[Dict]:
        """
        Get season-long projection for a specific player.
        
        Args:
            player_name: Name of the player to look up
            
        Returns:
            Dictionary with player's season projections or None if not found
        """
        df = self.fetch_season_long_projections()  # Will use cached version
        
        if df.empty:
            return None
            
        # Try exact match first
        normalized_player_name = self._normalize_name(player_name)
        exact_match = df[df['normalized_name'] == normalized_player_name]
        if not exact_match.empty:
            return exact_match.iloc[0].to_dict()
            
        # Try partial match (on normalized name)
        partial_match = df[df['normalized_name'].str.contains(normalized_player_name, case=False, na=False)]
        if not partial_match.empty:
            return partial_match.iloc[0].to_dict()
            
        return None
    
    def get_position_projections(self, position: str) -> pd.DataFrame:
        """Get all projections for a specific position"""
        df = self.fetch_season_long_projections()  # Will use cached version
        
        if df.empty:
            return pd.DataFrame()
            
        # Standardize position
        position = self.position_map.get(position, position)
        
        return df[df['position'] == position].sort_values('fantasy_points', ascending=False)