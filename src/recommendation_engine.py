import pandas as pd

class RecommendationEngine:
    """Encapsulates the logic for generating player recommendations."""

    def get_enhanced_roster_recommendation(self, player_row: pd.Series, current_week: int) -> str:
        """
        Enhanced recommendation system that considers Vegas context.
        """
        if player_row['bye_week'] == current_week:
            return 'BYE • Cannot play'
        if player_row['injury_status'] in ['OUT', 'IR', 'DOUBTFUL']:
            status_text = player_row['injury_status_full'] if player_row['injury_status_full'] else player_row['injury_status']
            return f'INJURED • {status_text}'
        
        best_proj = player_row['external_proj'] if pd.notna(player_row['external_proj']) else player_row['projected_points']
        vegas_boost = self._calculate_vegas_boost(
            player_row['position'], 
            player_row['spread'], 
            player_row['implied_total'],
            player_row['game_script']
        )
        game_script = player_row.get('game_script', 'average_game')
        season_rank = player_row.get('season_rank', 999)
        
        context_adjusted_proj = best_proj * vegas_boost
        talent_boost = self._get_talent_boost(season_rank, player_row['position'])
        final_proj = context_adjusted_proj * talent_boost
        
        if player_row['is_starting']:
            confidence = self._get_confidence_level(final_proj, vegas_boost, game_script, season_rank)
            return f'START • {confidence}'
        
        if final_proj >= 12:
            context_msg = self._get_context_message(game_script, vegas_boost, season_rank)
            return f'BENCH • High Upside {context_msg}'
        elif final_proj >= 8:
            return 'BENCH • Solid Depth'
        elif final_proj >= 5:
            return 'BENCH • Deep Option'
        else:
            return 'BENCH • Low Projection'

    def _get_confidence_level(self, proj: float, vegas_boost: float, game_script: str, season_rank: int = 999) -> str:
        """Determine confidence level for starting players"""
        if proj >= 15 and vegas_boost >= 1.1 and season_rank <= 12:
            return 'Elite Play'
        elif proj >= 15 and vegas_boost >= 1.1:
            return 'High Confidence'
        elif proj >= 12 or vegas_boost >= 1.05:
            return 'Good Play'
        elif proj >= 8:
            return 'Decent Floor'
        else:
            return 'Risky Play'

    def _get_context_message(self, game_script: str, vegas_boost: float, season_rank: int = 999) -> str:
        """Get contextual message based on game script and talent"""
        if season_rank <= 5:
            talent_msg = " (Elite Talent)"
        elif season_rank <= 12:
            talent_msg = " (Top Tier)"
        elif season_rank <= 24:
            talent_msg = " (Solid)"
        else:
            talent_msg = ""
            
        if vegas_boost >= 1.15:
            return f'(Elite Spot){talent_msg}'
        elif vegas_boost >= 1.1:
            return f'(Great Matchup){talent_msg}'
        elif vegas_boost >= 1.05:
            return f'(Good Spot){talent_msg}'
        elif vegas_boost <= 0.9:
            return f'(Tough Spot){talent_msg}'
        else:
            return talent_msg

    def _get_talent_boost(self, season_rank: int, position: str) -> float:
        """
        Apply talent-based boost/penalty based on season-long ranking.
        """
        if season_rank is None or pd.isna(season_rank):
            return 1.0
            
        season_rank = int(season_rank)
        
        if season_rank <= 5:
            return 1.08
        elif season_rank <= 12:
            return 1.04
        elif season_rank <= 24:
            return 1.02
        elif season_rank <= 36:
            return 1.0
        elif season_rank <= 48:
            return 0.97
        else:
            return 0.94

    def _calculate_vegas_boost(self, position: str, spread: float, implied_total: float, game_script: str) -> float:
        """
        Calculate how Vegas context should adjust a player's projection.
        """
        boost = 1.0
        
        if implied_total >= 28:
            boost += 0.15
        elif implied_total >= 24:
            boost += 0.08
        elif implied_total >= 20:
            boost += 0.03
        elif implied_total <= 17:
            boost -= 0.1
        elif implied_total <= 14:
            boost -= 0.2
            
        if position == 'QB':
            if spread >= 7:
                boost += 0.12
            elif spread >= 3:
                boost += 0.06
            elif spread <= -7:
                boost -= 0.05
                
        elif position == 'RB':
            if spread <= -7:
                boost += 0.1
            elif spread <= -3:
                boost += 0.05
            elif spread >= 7:
                boost -= 0.08
            if 'high_scoring' in game_script and 'close' in game_script:
                boost += 0.08
                
        elif position in ['WR', 'TE']:
            if spread >= 3:
                boost += 0.08
            if 'high_scoring' in game_script:
                boost += 0.1
            if spread <= -7:
                boost -= 0.05
                
        elif position == 'K':
            if implied_total >= 26:
                boost += 0.1
            if -6 <= spread <= -1:
                boost += 0.05
                
        elif position == 'D/ST':
            opponent_implied = 44 - implied_total
            if opponent_implied <= 17:
                boost += 0.15
            elif opponent_implied <= 20:
                boost += 0.08
            elif opponent_implied >= 28:
                boost -= 0.1
                
        if 'blowout' in game_script:
            if position == 'RB' and spread <= -7:
                boost += 0.12
            elif position in ['WR', 'TE'] and spread >= 7:
                boost += 0.1
        elif 'close' in game_script:
            if position in ['QB', 'RB', 'WR', 'TE']:
                boost += 0.05
                
        return max(0.7, min(1.4, boost))
