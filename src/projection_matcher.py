"""
Projection Matcher for Fantasy Football Data Integration

Handles advanced name matching and integration between ESPN and FanDuel projections,
including fuzzy matching, nickname resolution, and data merging.
"""

import pandas as pd
import re
from typing import List, Tuple
from .fanduel_projections import get_fanduel_projections


class ProjectionMatcher:
    """Matches and integrates projections from multiple sources."""
    
    def __init__(self):
        """Initialize the projection matcher."""
        # Common nickname mappings for better matching
        self.nickname_map = {
            'chig': 'chigoziem',
            'aj': 'a j',
            'cj': 'c j', 
            'dj': 'd j',
            'tj': 't j',
            'jj': 'j j',
            'pj': 'p j'
        }
        
        # NFL team nickname to city name mapping for defenses
        self.team_defense_map = {
            # Team nicknames (from ESPN) -> city names (for FanDuel)
            'bills': 'buffalo', 'dolphins': 'miami', 'patriots': 'new england', 'jets': 'new york jets',
            'ravens': 'baltimore', 'bengals': 'cincinnati', 'browns': 'cleveland', 'steelers': 'pittsburgh', 
            'texans': 'houston', 'colts': 'indianapolis', 'jaguars': 'jacksonville', 'titans': 'tennessee',
            'broncos': 'denver', 'chiefs': 'kansas city', 'raiders': 'las vegas', 'chargers': 'los angeles chargers',
            'cowboys': 'dallas', 'giants': 'new york giants', 'eagles': 'philadelphia', 'commanders': 'washington',
            'bears': 'chicago', 'lions': 'detroit', 'packers': 'green bay', 'vikings': 'minnesota',
            'falcons': 'atlanta', 'panthers': 'carolina', 'saints': 'new orleans', 'buccaneers': 'tampa bay',
            'cardinals': 'arizona', 'rams': 'los angeles rams', '49ers': 'san francisco', 'seahawks': 'seattle'
        }
    
    def get_external_projections(self) -> pd.DataFrame:
        """
        Fetch and combine external projections from FanDuel API.
        
        Returns:
            Combined DataFrame with all FanDuel projections including salary data
        """
        print("Fetching FantasyDuel projections...")
        
        try:
            # Fetch projections for all relevant NFL positions
            skill_df = get_fanduel_projections(position="NFL_SKILL", projection_type="WEEKLY")
            kicker_df = get_fanduel_projections(position="NFL_KICKER", projection_type="WEEKLY")
            dst_df = get_fanduel_projections(position="NFL_D_ST", projection_type="WEEKLY")

            # Combine the dataframes
            projections_df = pd.concat([skill_df, kicker_df, dst_df], ignore_index=True)
            
            print(f"Successfully fetched {len(projections_df)} total projections from FantasyDuel.")
            return projections_df
            
        except Exception as e:
            print(f"Error fetching external projections: {e}")
            return pd.DataFrame()
    
    def integrate_projections(self, waiver_df: pd.DataFrame) -> pd.DataFrame:
        """
        Integrate FanDuel projections with ESPN waiver analysis data.
        
        Args:
            waiver_df: DataFrame with ESPN waiver wire analysis
            
        Returns:
            Enhanced DataFrame with FanDuel projections, opponent data, and salary info
        """
        external_df = self.get_external_projections()
        
        if external_df.empty:
            # Add empty columns if no external data
            waiver_df['external_proj'] = 0
            waiver_df['fd_opponent'] = ''
            waiver_df['fd_salary'] = ''
            waiver_df['fd_value'] = ''
            return waiver_df
        
        # Apply advanced name matching
        projections, opponents, salaries, values = self._advanced_name_matching(
            waiver_df['name'].tolist(), external_df)
        
        # Add FanDuel data to waiver analysis
        waiver_df['external_proj'] = projections
        waiver_df['fd_opponent'] = opponents
        waiver_df['fd_salary'] = salaries
        waiver_df['fd_value'] = values
        
        return waiver_df
    
    def _advanced_name_matching(self, espn_names: List[str], fd_df: pd.DataFrame) -> Tuple[List, List, List, List]:
        """
        Advanced name matching between ESPN and FanDuel data.
        
        Handles:
        - Direct name matches
        - Nickname variations 
        - Punctuation differences
        - Partial name matching
        
        Args:
            espn_names: List of ESPN player names
            fd_df: FanDuel projections DataFrame
            
        Returns:
            Tuple of matched projections, opponents, salaries, values
        """
        matched_results = []
        matched_opponents = []
        matched_salaries = []
        matched_values = []
        
        # Pre-normalize all FanDuel names for faster lookup
        fd_normalized = fd_df['player'].apply(self._normalize_name).tolist()
        
        for espn_name in espn_names:
            espn_norm = self._normalize_name(espn_name)
            matched_data = self._find_best_match(espn_norm, fd_normalized, fd_df)
            
            matched_results.append(matched_data.get('projection', 0))
            matched_opponents.append(matched_data.get('opponent', ''))
            matched_salaries.append(matched_data.get('salary', ''))
            matched_values.append(matched_data.get('value', ''))
        
        return matched_results, matched_opponents, matched_salaries, matched_values
    
    def _find_best_match(self, espn_norm: str, fd_normalized: List[str], fd_df: pd.DataFrame) -> dict:
        """
        Find the best matching FanDuel player for an ESPN player.
        
        Args:
            espn_norm: Normalized ESPN player name
            fd_normalized: List of normalized FanDuel names
            fd_df: FanDuel DataFrame for data extraction
            
        Returns:
            Dictionary with matched player data
        """
        # Try direct match first
        if espn_norm in fd_normalized:
            idx = fd_normalized.index(espn_norm)
            return self._extract_player_data(fd_df.iloc[idx])
        
        # Try defense team name mapping for D/ST players
        if 'd st' in espn_norm or 'dst' in espn_norm:
            match_data = self._try_defense_matching(espn_norm, fd_normalized, fd_df)
            if match_data:
                return match_data
        
        # Try nickname mapping
        match_data = self._try_nickname_matching(espn_norm, fd_normalized, fd_df)
        if match_data:
            return match_data
        
        # Try partial matching as last resort
        return self._try_partial_matching(espn_norm, fd_normalized, fd_df)
    
    def _try_defense_matching(self, espn_norm: str, fd_normalized: List[str], fd_df: pd.DataFrame) -> dict:
        """Try matching defense teams using team nickname to city name mapping."""
        # Extract team nickname from ESPN name (e.g., "bills d st" -> "bills")  
        team_part = espn_norm.replace(' d st', '').replace(' dst', '').replace('/st', '').strip()
        
        # Look up the city name for this team nickname
        city_name = self.team_defense_map.get(team_part.lower())
        if not city_name:
            return {}
        
        # Try to find matching FanDuel defense name
        # Try multiple possible normalized formats
        possible_targets = [
            f"{city_name} d st",
            f"{city_name} dst", 
            city_name  # Some get truncated to just the city name
        ]
        
        for target_name in possible_targets:
            if target_name in fd_normalized:
                idx = fd_normalized.index(target_name)
                return self._extract_player_data(fd_df.iloc[idx])
        
        return {}
    
    def _try_nickname_matching(self, espn_norm: str, fd_normalized: List[str], fd_df: pd.DataFrame) -> dict:
        """Try matching using nickname mappings."""
        for nick, full in self.nickname_map.items():
            if espn_norm.startswith(nick + ' '):
                alt_name = espn_norm.replace(nick + ' ', full + ' ', 1)
                if alt_name in fd_normalized:
                    idx = fd_normalized.index(alt_name)
                    return self._extract_player_data(fd_df.iloc[idx])
        return {}
    
    def _try_partial_matching(self, espn_norm: str, fd_normalized: List[str], fd_df: pd.DataFrame) -> dict:
        """Try partial matching using first name + partial last name."""
        espn_parts = espn_norm.split()
        if len(espn_parts) < 2:
            return {}
        
        first_name = espn_parts[0]
        last_start = espn_parts[1][:3] if len(espn_parts[1]) >= 3 else espn_parts[1]
        
        for i, fd_norm in enumerate(fd_normalized):
            fd_parts = fd_norm.split()
            if len(fd_parts) >= 2:
                if (fd_parts[0] == first_name and fd_parts[1].startswith(last_start)):
                    return self._extract_player_data(fd_df.iloc[i])
        
        return {}
    
    def _extract_player_data(self, player_row: pd.Series) -> dict:
        """Extract all relevant data from a FanDuel player row."""
        return {
            'projection': player_row.get('external_proj', 0),
            'opponent': player_row.get('opp', ''),
            'salary': player_row.get('fd_salary', ''),
            'value': player_row.get('fd_value', '')
        }
    
    def _normalize_name(self, name) -> str:
        """
        Normalize player names for consistent matching.
        
        Removes punctuation, takes first two words, converts to lowercase.
        """
        if pd.isna(name):
            return ""
        
        # Remove all punctuation and extra spaces, take first two words
        clean_name = re.sub(r'[^\w\s]', '', str(name))
        words = clean_name.split()
        return ' '.join(words[:2]).lower() if len(words) >= 2 else clean_name.lower()