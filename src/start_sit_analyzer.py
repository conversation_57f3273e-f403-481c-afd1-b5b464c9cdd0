import os
import pandas as pd
from typing import Any, Dict, <PERSON><PERSON>
from espn_api.football import League
from dotenv import load_dotenv
from .player_data_reader import <PERSON><PERSON>ata<PERSON>eader
from .recommendation_engine import RecommendationEngine
from .lineup_optimizer import LineupOptimizer
from .trade_analyzer import TradeAnalyzer

load_dotenv()

class StartSitAnalyzer:
    """
    Orchestrates the analysis of start/sit decisions, delegating tasks to specialized modules.
    """
    def __init__(self, league: League, player_data_reader: PlayerDataReader):
        self.league = league
        self.player_data_reader = player_data_reader
        self.recommendation_engine = RecommendationEngine()
        self.lineup_optimizer = LineupOptimizer(league)
        self.trade_analyzer = TradeAnalyzer()

    def get_my_roster_analysis(self, my_team: Any, week: int = None) -> pd.DataFrame:
        """
        Get comprehensive analysis for a given team's roster.
        """
        if week is None:
            week = self.league.current_week

        roster_df = self.player_data_reader.get_roster_data(my_team, week)

        roster_df['recommendation'] = roster_df.apply(
            lambda row: self.recommendation_engine.get_enhanced_roster_recommendation(row, week), axis=1
        )
        
        return roster_df

    def get_optimal_lineup_recommendations(self, my_team: Any, week: int = None) -> Tuple[Dict[str, pd.DataFrame], Dict[str, pd.DataFrame], pd.DataFrame, pd.DataFrame]:
        """
        Generate optimal lineup recommendations for the user and their opponent.
        """
        if week is None:
            week = self.league.current_week

        roster_df = self.get_my_roster_analysis(my_team, week)
        
        optimal_lineup, used_player_ids = self.lineup_optimizer.get_optimal_lineup(roster_df, week)
        
        opponent_optimal_lineup, opponent_roster_df, used_opponent_player_ids = self.lineup_optimizer.get_opponent_optimal_lineup(my_team, week)

        my_team_bench = roster_df[~roster_df['player_id'].isin(used_player_ids)].copy()
        opponent_team_bench = opponent_roster_df[~opponent_roster_df['player_id'].isin(used_opponent_player_ids)].copy()

        return optimal_lineup, opponent_optimal_lineup, my_team_bench, opponent_team_bench

    def get_trade_insights(self, my_team: Any) -> Dict[str, any]:
        """
        Generate trade insights for roster optimization.
        """
        roster_df = self.get_my_roster_analysis(my_team)
        return self.trade_analyzer.get_trade_insights(roster_df)
