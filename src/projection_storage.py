"""
Projection Storage Module

Captures and stores projections before games start to enable accurate
post-week analysis. Designed to run before first game of each week.

Key Features:
- Pre-game projection capture from multiple sources
- Structured storage for historical analysis
- Automatic week detection and storage
- Data validation and integrity checks
"""

import json
import os
import pandas as pd
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from espn_api.football import League
import logging

@dataclass
class StoredProjection:
    """Data class for stored projections"""
    week: int
    player_name: str
    player_id: Optional[str]
    position: str
    team: str
    source: str  # ESPN, FanDuel, WinWithOdds
    projected_points: float
    projected_breakdown: Optional[Dict] = None
    capture_timestamp: str = None
    
    def __post_init__(self):
        if self.capture_timestamp is None:
            self.capture_timestamp = datetime.now().isoformat()

class ProjectionStorage:
    """
    Manages storage and retrieval of weekly projections.
    
    This class captures projections before games start and stores them
    for later analysis after the week completes.
    """
    
    def __init__(self, storage_dir: str = "projection_storage"):
        """
        Initialize projection storage
        
        Args:
            storage_dir: Directory to store projection files
        """
        self.storage_dir = storage_dir
        self.logger = logging.getLogger(__name__)
        
        # Create storage directory if it doesn't exist
        os.makedirs(storage_dir, exist_ok=True)
    
    def get_storage_path(self, week: int, year: int) -> str:
        """Get file path for storing week projections"""
        return os.path.join(self.storage_dir, f"projections_week_{week}_{year}.json")
    
    def capture_espn_projections(self, league: League, week: int) -> List[StoredProjection]:
        """
        Capture ESPN projections for all league players
        
        Args:
            league: ESPN League instance
            week: Week number to capture projections for
            
        Returns:
            List of StoredProjection objects
        """
        projections = []
        
        self.logger.info(f"Capturing ESPN projections for week {week}")
        
        # Get all teams in the league
        for team in league.teams:
            for player in team.roster:
                # Get week-specific projections
                if hasattr(player, 'stats') and player.stats and week in player.stats:
                    week_stats = player.stats[week]
                    projected_points = week_stats.get('projected_points', 0)
                    projected_breakdown = week_stats.get('projected_breakdown', {})
                    
                    # Only store if there's a meaningful projection
                    if projected_points > 0:
                        projection = StoredProjection(
                            week=week,
                            player_name=player.name,
                            player_id=getattr(player, 'playerId', None),
                            position=player.position,
                            team=player.proTeam if hasattr(player, 'proTeam') else '',
                            source='ESPN',
                            projected_points=projected_points,
                            projected_breakdown=projected_breakdown
                        )
                        projections.append(projection)
        
        self.logger.info(f"Captured {len(projections)} ESPN projections")
        return projections
    
    def capture_external_projections(
        self, 
        week: int,
        external_data: Dict[str, pd.DataFrame]
    ) -> List[StoredProjection]:
        """
        Capture external projections (FanDuel, WinWithOdds, etc.)
        
        Args:
            week: Week number
            external_data: Dict of source_name -> DataFrame with projections
            
        Returns:
            List of StoredProjection objects
        """
        projections = []
        
        for source_name, df in external_data.items():
            self.logger.info(f"Capturing {source_name} projections for week {week}")
            
            for _, row in df.iterrows():
                # Handle different column names
                player_name = row.get('name', row.get('player_name', ''))
                position = row.get('position', row.get('pos', ''))
                team = row.get('team', row.get('proTeam', ''))
                
                # Get projected points (different sources use different column names)
                projected_points = (
                    row.get('fantasy', 0) or 
                    row.get('projected_points', 0) or
                    row.get('projection', 0)
                )
                
                if projected_points > 0 and player_name:
                    projection = StoredProjection(
                        week=week,
                        player_name=player_name,
                        player_id=None,  # External sources typically don't have ESPN IDs
                        position=position,
                        team=team,
                        source=source_name,
                        projected_points=float(projected_points)
                    )
                    projections.append(projection)
            
            self.logger.info(f"Captured {len([p for p in projections if p.source == source_name])} {source_name} projections")
        
        return projections
    
    def store_weekly_projections(self, week: int, year: int, projections: List[StoredProjection]) -> bool:
        """
        Store projections for a specific week
        
        Args:
            week: Week number
            year: Season year
            projections: List of projections to store
            
        Returns:
            True if successful, False otherwise
        """
        try:
            storage_path = self.get_storage_path(week, year)
            
            # Convert to serializable format
            projection_data = {
                'week': week,
                'year': year,
                'capture_time': datetime.now().isoformat(),
                'total_projections': len(projections),
                'sources': list(set(p.source for p in projections)),
                'projections': [asdict(p) for p in projections]
            }
            
            with open(storage_path, 'w') as f:
                json.dump(projection_data, f, indent=2)
            
            self.logger.info(f"Stored {len(projections)} projections to {storage_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to store projections: {e}")
            return False
    
    def load_weekly_projections(self, week: int, year: int) -> Optional[List[StoredProjection]]:
        """
        Load stored projections for a specific week
        
        Args:
            week: Week number
            year: Season year
            
        Returns:
            List of StoredProjection objects or None if not found
        """
        try:
            storage_path = self.get_storage_path(week, year)
            
            if not os.path.exists(storage_path):
                self.logger.warning(f"No stored projections found for week {week} {year}")
                return None
            
            with open(storage_path, 'r') as f:
                data = json.load(f)
            
            # Convert back to StoredProjection objects
            projections = []
            for proj_data in data['projections']:
                projection = StoredProjection(**proj_data)
                projections.append(projection)
            
            self.logger.info(f"Loaded {len(projections)} stored projections for week {week}")
            return projections
            
        except Exception as e:
            self.logger.error(f"Failed to load projections: {e}")
            return None
    
    def capture_and_store_all_projections(
        self, 
        league: League, 
        week: int,
        external_sources: Optional[Dict[str, pd.DataFrame]] = None
    ) -> bool:
        """
        Capture and store all available projections for a week
        
        Args:
            league: ESPN League instance
            week: Week number to capture
            external_sources: Optional external projection sources
            
        Returns:
            True if successful, False otherwise
        """
        all_projections = []
        
        # Capture ESPN projections
        espn_projections = self.capture_espn_projections(league, week)
        all_projections.extend(espn_projections)
        
        # Capture external projections if provided
        if external_sources:
            external_projections = self.capture_external_projections(week, external_sources)
            all_projections.extend(external_projections)
        
        # Store all projections
        success = self.store_weekly_projections(week, league.year, all_projections)
        
        if success:
            self.logger.info(f"Successfully captured and stored {len(all_projections)} projections for week {week}")
        else:
            self.logger.error(f"Failed to store projections for week {week}")
        
        return success
    
    def get_stored_projections_summary(self) -> Dict[str, Any]:
        """
        Get summary of all stored projections
        
        Returns:
            Dictionary with summary information
        """
        summary = {
            'weeks_stored': [],
            'total_files': 0,
            'sources_by_week': {}
        }
        
        if not os.path.exists(self.storage_dir):
            return summary
        
        for filename in os.listdir(self.storage_dir):
            if filename.startswith('projections_week_') and filename.endswith('.json'):
                try:
                    filepath = os.path.join(self.storage_dir, filename)
                    with open(filepath, 'r') as f:
                        data = json.load(f)
                    
                    week = data['week']
                    year = data['year']
                    sources = data['sources']
                    
                    week_key = f"Week {week} {year}"
                    summary['weeks_stored'].append(week_key)
                    summary['sources_by_week'][week_key] = sources
                    summary['total_files'] += 1
                    
                except Exception as e:
                    self.logger.warning(f"Could not read {filename}: {e}")
        
        return summary
    
    def convert_to_analysis_format(self, projections: List[StoredProjection]) -> Dict[str, pd.DataFrame]:
        """
        Convert stored projections to format expected by PostWeekAnalyzer
        
        Args:
            projections: List of stored projections
            
        Returns:
            Dict of source_name -> DataFrame
        """
        analysis_data = {}
        
        # Group by source
        sources = set(p.source for p in projections)
        
        for source in sources:
            source_projections = [p for p in projections if p.source == source]
            
            # Convert to DataFrame
            df_data = []
            for proj in source_projections:
                df_data.append({
                    'name': proj.player_name,
                    'position': proj.position,
                    'team': proj.team,
                    'projected_points': proj.projected_points,
                    'fantasy': proj.projected_points  # Alternative column name
                })
            
            analysis_data[source] = pd.DataFrame(df_data)
        
        return analysis_data
    
    def is_week_ready_for_capture(self, league: League, week: int) -> bool:
        """
        Check if a week is ready for projection capture
        
        Args:
            league: ESPN League instance  
            week: Week number to check
            
        Returns:
            True if ready for capture, False otherwise
        """
        # Check if we already have projections stored
        existing = self.load_weekly_projections(week, league.year)
        if existing:
            self.logger.info(f"Projections already captured for week {week}")
            return False
        
        # Check if it's the right time (before first game of the week)
        # This is a simple check - could be enhanced with game schedule data
        current_week = league.current_week
        
        if week > current_week:
            self.logger.info(f"Week {week} is in the future (current: {current_week})")
            return False
        
        if week < current_week:
            self.logger.warning(f"Week {week} is in the past - projections may have changed")
        
        return True