import requests
import pandas as pd
from typing import Dict, List, Optional, Tuple
import os
from datetime import datetime
import json
from bs4 import BeautifulSoup

class WinWithOddsTrends:
    """
    Analyzes player trends and DFS value data from WinWithOdds for redraft insights.
    Identifies buy-low/sell-high opportunities and undervalued players.
    """
    
    def __init__(self):
        self.base_url = "https://winwithodds.com"
        self.cache_dir = "cache"
        os.makedirs(self.cache_dir, exist_ok=True)
    
    def get_player_trends(self, force_refresh: bool = False) -> pd.DataFrame:
        """
        Get player performance trends (over/underperforming expectations).
        
        Returns:
            DataFrame with columns: name, position, trend_pct, category (buy_low/sell_high)
        """
        cache_file = f"{self.cache_dir}/winwithodds_trends.json"
        
        # Check cache first
        if not force_refresh and os.path.exists(cache_file):
            try:
                cache_age_hours = (datetime.now().timestamp() - os.path.getmtime(cache_file)) / 3600
                if cache_age_hours < 6:  # Cache for 6 hours (trends change frequently)
                    print(f"Loading player trends from cache (age: {cache_age_hours:.1f} hours)")
                    with open(cache_file, 'r') as f:
                        data = json.load(f)
                    return pd.DataFrame(data)
            except Exception as e:
                print(f"Cache read error: {e}")
        
        print("Fetching fresh player trend data...")
        
        try:
            # Fetch the main page to look for trends section
            response = requests.get(self.base_url, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for trends data - could be in various formats
            trends_data = self._extract_trends_data(soup)
            
            if not trends_data:
                print("⚠️ No trend data found, using mock data for demo")
                trends_data = self._generate_mock_trends()
            
            df = pd.DataFrame(trends_data)
            
            # Cache the results
            try:
                with open(cache_file, 'w') as f:
                    json.dump(df.to_dict('records'), f, indent=2)
                print(f"📁 Cached trend data to {cache_file}")
            except Exception as e:
                print(f"Cache write warning: {e}")
            
            return df
            
        except Exception as e:
            print(f"❌ Error fetching trends: {e}")
            # Return mock data as fallback
            return pd.DataFrame(self._generate_mock_trends())
    
    def get_dfs_values(self, force_refresh: bool = False) -> pd.DataFrame:
        """
        Get DFS value data (points per dollar efficiency).
        
        Returns:
            DataFrame with columns: name, position, salary, projected_points, value, value_tier
        """
        cache_file = f"{self.cache_dir}/winwithodds_dfs_values.json"
        
        # Check cache first
        if not force_refresh and os.path.exists(cache_file):
            try:
                cache_age_hours = (datetime.now().timestamp() - os.path.getmtime(cache_file)) / 3600
                if cache_age_hours < 12:  # Cache for 12 hours
                    print(f"Loading DFS values from cache (age: {cache_age_hours:.1f} hours)")
                    with open(cache_file, 'r') as f:
                        data = json.load(f)
                    return pd.DataFrame(data)
            except Exception as e:
                print(f"Cache read error: {e}")
        
        print("Fetching fresh DFS value data...")
        
        try:
            # Fetch DFS values page
            dfs_url = f"{self.base_url}/dfs"
            response = requests.get(dfs_url, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract DFS value table
            dfs_data = self._extract_dfs_table(soup)
            
            if not dfs_data:
                print("⚠️ No DFS data found, using mock data for demo")
                dfs_data = self._generate_mock_dfs_values()
            
            df = pd.DataFrame(dfs_data)
            
            # Add value tiers
            if 'value' in df.columns:
                df = self._categorize_value_tiers(df)
            
            # Cache the results
            try:
                with open(cache_file, 'w') as f:
                    json.dump(df.to_dict('records'), f, indent=2)
                print(f"📁 Cached DFS values to {cache_file}")
            except Exception as e:
                print(f"Cache write warning: {e}")
            
            return df
            
        except Exception as e:
            print(f"❌ Error fetching DFS values: {e}")
            # Return mock data as fallback
            return pd.DataFrame(self._generate_mock_dfs_values())
    
    def _extract_trends_data(self, soup: BeautifulSoup) -> List[Dict]:
        """Extract trend data from the webpage"""
        trends_data = []
        
        # Look for trend-related sections
        trend_sections = soup.find_all(['div', 'section'], class_=lambda x: x and 'trend' in x.lower())
        
        # Also look for tables that might contain trend data
        tables = soup.find_all('table')
        
        for table in tables:
            headers = [th.get_text().strip().lower() for th in table.find_all(['th', 'td']) if th.get_text().strip()]
            
            # Check if this looks like a trends table
            if any(keyword in ' '.join(headers) for keyword in ['trend', 'over', 'under', 'performance', '%']):
                rows = table.find_all('tr')[1:]  # Skip header
                
                for row in rows:
                    cells = [td.get_text().strip() for td in row.find_all(['td', 'th'])]
                    if len(cells) >= 3:
                        try:
                            trends_data.append({
                                'name': cells[0],
                                'position': self._extract_position_from_cell(cells),
                                'trend_pct': self._extract_percentage(cells),
                                'category': 'unknown'
                            })
                        except Exception:
                            continue
        
        return trends_data
    
    def _extract_dfs_table(self, soup: BeautifulSoup) -> List[Dict]:
        """Extract DFS value table from the webpage"""
        dfs_data = []
        
        # Look for the main DFS table
        table = soup.find('table')
        if not table:
            return []
            
        headers = [th.get_text().strip().lower() for th in table.find_all('th')]
        rows = table.find_all('tr')[1:]  # Skip header
        
        for row in rows:
            cells = [td.get_text().strip() for td in row.find_all('td')]
            if len(cells) >= 4:
                try:
                    # Map data based on common DFS table structure
                    player_data = {}
                    
                    for i, cell in enumerate(cells):
                        if i < len(headers):
                            header = headers[i]
                            
                            if 'name' in header or 'player' in header:
                                player_data['name'] = cell
                            elif 'pos' in header:
                                player_data['position'] = cell
                            elif 'salary' in header or 'cost' in header:
                                player_data['salary'] = self._parse_salary(cell)
                            elif 'point' in header or 'proj' in header:
                                player_data['projected_points'] = self._parse_float(cell)
                            elif 'value' in header:
                                player_data['value'] = self._parse_float(cell)
                    
                    if 'name' in player_data:
                        dfs_data.append(player_data)
                        
                except Exception:
                    continue
        
        return dfs_data
    
    def _generate_mock_trends(self) -> List[Dict]:
        """Generate realistic mock trend data for demo purposes"""
        return [
            {'name': 'Saquon Barkley', 'position': 'RB', 'trend_pct': -15.2, 'category': 'buy_low'},
            {'name': 'Mike Evans', 'position': 'WR', 'trend_pct': -12.8, 'category': 'buy_low'},
            {'name': 'Chuba Hubbard', 'position': 'RB', 'trend_pct': +18.5, 'category': 'sell_high'},
            {'name': 'Calvin Ridley', 'position': 'WR', 'trend_pct': +14.3, 'category': 'sell_high'},
            {'name': 'DeAndre Hopkins', 'position': 'WR', 'trend_pct': -8.7, 'category': 'buy_low'},
            {'name': 'Tyler Boyd', 'position': 'WR', 'trend_pct': +11.2, 'category': 'sell_high'},
            {'name': 'Najee Harris', 'position': 'RB', 'trend_pct': -10.5, 'category': 'buy_low'},
            {'name': 'Jayden Reed', 'position': 'WR', 'trend_pct': +16.8, 'category': 'sell_high'},
        ]
    
    def _generate_mock_dfs_values(self) -> List[Dict]:
        """Generate realistic mock DFS value data"""
        return [
            {'name': 'Lamar Jackson', 'position': 'QB', 'salary': 8200, 'projected_points': 22.5, 'value': 2.74},
            {'name': 'Jayden Daniels', 'position': 'QB', 'salary': 7400, 'projected_points': 20.8, 'value': 2.81},
            {'name': 'Chuba Hubbard', 'position': 'RB', 'salary': 6800, 'projected_points': 16.2, 'value': 2.38},
            {'name': 'De\'Von Achane', 'position': 'RB', 'salary': 7200, 'projected_points': 17.8, 'value': 2.47},
            {'name': 'Calvin Ridley', 'position': 'WR', 'salary': 6200, 'projected_points': 14.5, 'value': 2.34},
            {'name': 'Jayden Reed', 'position': 'WR', 'salary': 5800, 'projected_points': 13.8, 'value': 2.38},
            {'name': 'Brock Bowers', 'position': 'TE', 'salary': 5400, 'projected_points': 12.2, 'value': 2.26},
            {'name': 'Trey McBride', 'position': 'TE', 'salary': 5200, 'projected_points': 11.8, 'value': 2.27},
        ]
    
    def _categorize_value_tiers(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add value tier categories based on points per dollar"""
        if 'value' not in df.columns:
            return df
            
        # Calculate percentiles for value tiers
        df['value_tier'] = pd.cut(
            df['value'], 
            bins=5, 
            labels=['Poor Value', 'Below Average', 'Average', 'Good Value', 'Elite Value']
        )
        
        return df
    
    def _extract_position_from_cell(self, cells: List[str]) -> str:
        """Extract position from table cells"""
        for cell in cells:
            if cell.upper() in ['QB', 'RB', 'WR', 'TE', 'K', 'DST', 'D/ST']:
                return cell.upper()
        return 'UNKNOWN'
    
    def _extract_percentage(self, cells: List[str]) -> float:
        """Extract percentage value from table cells"""
        for cell in cells:
            if '%' in cell:
                try:
                    return float(cell.replace('%', '').replace('+', ''))
                except ValueError:
                    continue
        return 0.0
    
    def _parse_salary(self, salary_str: str) -> int:
        """Parse salary string to integer"""
        try:
            # Remove currency symbols and commas
            clean_salary = salary_str.replace('$', '').replace(',', '')
            return int(clean_salary)
        except (ValueError, AttributeError):
            return 0
    
    def _parse_float(self, value_str: str) -> float:
        """Parse string to float"""
        try:
            return float(value_str)
        except (ValueError, TypeError):
            return 0.0
    
    def get_trade_targets(self, position: str = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Get buy-low and sell-high trade targets.
        
        Args:
            position: Filter by position (optional)
            
        Returns:
            Tuple of (buy_low_targets, sell_high_targets) DataFrames
        """
        trends_df = self.get_player_trends()
        
        if trends_df.empty:
            return pd.DataFrame(), pd.DataFrame()
        
        # Categorize players based on trend percentage
        trends_df.loc[trends_df['trend_pct'] <= -8, 'category'] = 'buy_low'
        trends_df.loc[trends_df['trend_pct'] >= 12, 'category'] = 'sell_high'
        trends_df.loc[
            (trends_df['trend_pct'] > -8) & (trends_df['trend_pct'] < 12), 
            'category'
        ] = 'hold'
        
        buy_low = trends_df[trends_df['category'] == 'buy_low'].sort_values('trend_pct')
        sell_high = trends_df[trends_df['category'] == 'sell_high'].sort_values('trend_pct', ascending=False)
        
        if position:
            buy_low = buy_low[buy_low['position'] == position.upper()]
            sell_high = sell_high[sell_high['position'] == position.upper()]
        
        return buy_low, sell_high
    
    def get_undervalued_players(self, min_value: float = 2.3) -> pd.DataFrame:
        """
        Get undervalued players based on DFS efficiency.
        
        Args:
            min_value: Minimum points per dollar threshold
            
        Returns:
            DataFrame of high-value players
        """
        dfs_df = self.get_dfs_values()
        
        if dfs_df.empty or 'value' not in dfs_df.columns:
            return pd.DataFrame()
        
        undervalued = dfs_df[dfs_df['value'] >= min_value].sort_values('value', ascending=False)
        
        return undervalued
    
    def generate_trade_report(self) -> Dict[str, any]:
        """
        Generate a comprehensive trade analysis report.
        
        Returns:
            Dictionary with buy_low, sell_high, and undervalued player data
        """
        buy_low, sell_high = self.get_trade_targets()
        undervalued = self.get_undervalued_players()
        
        return {
            'buy_low_targets': buy_low.to_dict('records') if not buy_low.empty else [],
            'sell_high_targets': sell_high.to_dict('records') if not sell_high.empty else [],
            'undervalued_players': undervalued.to_dict('records') if not undervalued.empty else [],
            'generated_at': datetime.now().isoformat()
        }