import pandas as pd
from typing import Any, Dict
from espn_api.football import League
from .projection_matcher import ProjectionMatcher
from .vegas_data import <PERSON>Data<PERSON>etcher
from .winwithodds_projections import WinWithOddsProjections

class PlayerDataReader:
    """Reads and enriches player data from various sources."""

    def __init__(self, league: League, vegas_api_key: str):
        self.league = league
        self.projection_matcher = ProjectionMatcher()
        self.vegas_data = VegasDataFetcher(api_key=vegas_api_key)
        self.wwo_projections = WinWithOddsProjections()

    def get_player_details(self, player: Any) -> Dict[str, Any]:
        """
        Extracts relevant details from an ESPN player object.
        """
        return {
            'player_id': player.playerId,
            'name': player.name,
            'position': player.position,
            'team': player.proTeam,
            'projected_points': player.projected_avg_points,
            'injury_status': player.injuryStatus if hasattr(player, 'injuryStatus') else None,
            'injury_status_full': player.injuryStatusFull if hasattr(player, 'injuryStatusFull') else None,
            'bye_week': player.byeWeek if hasattr(player, 'byeWeek') else None,
            'on_team': True,
            'is_starting': False,
            'percent_owned': player.percent_owned,
            'percent_rostered': player.percent_started,
            'acquisition_type': player.acquisitionType,
            'pro_opponent': player.pro_opponent if hasattr(player, 'pro_opponent') else None,
            'external_proj': None,
            'fd_salary': None,
            'fd_value': None,
            'fd_opponent': None,
            'implied_total': None,
            'spread': None,
            'game_total': None,
            'game_script': None,
            'vegas_boost': 1.0,
            'season_projection': None,
            'season_rank': None,
        }

    def get_roster_data(self, team: Any, week: int) -> pd.DataFrame:
        """
        Get and enrich a team's roster data.
        """
        roster_players_data = [self.get_player_details(p) for p in team.roster]
        roster_df = pd.DataFrame(roster_players_data)

        roster_df = self.projection_matcher.integrate_projections(roster_df)
        roster_df = self._add_vegas_context(roster_df, week)
        roster_df = self._add_season_context(roster_df)

        for player_obj in team.roster:
            if hasattr(player_obj, 'lineupSlot') and player_obj.lineupSlot != 'BE':
                if player_obj.playerId is not None:
                    roster_df.loc[roster_df['player_id'] == player_obj.playerId, 'is_starting'] = True
        
        return roster_df

    def _add_vegas_context(self, roster_df: pd.DataFrame, week: int) -> pd.DataFrame:
        """
        Add Vegas betting context to each player.
        """
        for idx, player in roster_df.iterrows():
            game_context = self.vegas_data.get_team_game_context(player['team'], week)
            roster_df.at[idx, 'implied_total'] = game_context['implied_total']
            roster_df.at[idx, 'spread'] = game_context['spread']
            roster_df.at[idx, 'game_total'] = game_context['game_total']
            roster_df.at[idx, 'game_script'] = game_context['game_script']
        return roster_df

    def _add_season_context(self, roster_df: pd.DataFrame) -> pd.DataFrame:
        """
        Add season-long projection context from WinWithOdds, with ESPN as fallback.
        """
        season_projections_wwo = self.wwo_projections.fetch_season_long_projections()
        
        # Fetch all players from ESPN once for efficient lookup
        all_espn_players = {}
        for team in self.league.teams:
            for player in team.roster:
                all_espn_players[player.name] = player
        for player in self.league.free_agents():
            all_espn_players[player.name] = player

        for idx, player_row in roster_df.iterrows():
            player_name = player_row['name']
            position = player_row['position']
            
            # Initialize season_projection and season_rank with default values
            roster_df.at[idx, 'season_projection'] = 0
            roster_df.at[idx, 'season_rank'] = 999

            # Try WinWithOdds first
            season_data_wwo = None
            normalized_player_name_for_wwo = self.wwo_projections._normalize_name(player_name)
            if not season_projections_wwo.empty:
                season_data_wwo = self.wwo_projections.get_player_season_projection(normalized_player_name_for_wwo)

            if season_data_wwo:
                roster_df.at[idx, 'season_projection'] = season_data_wwo.get('fantasy_points', 0)
                position_rankings = self.wwo_projections.get_position_projections(position)
                if not position_rankings.empty:
                    try:
                        player_rank = position_rankings[
                            position_rankings['name'].str.contains(player_name, case=False, na=False)
                        ].index[0] + 1
                        roster_df.at[idx, 'season_rank'] = player_rank
                    except (IndexError, KeyError):
                        roster_df.at[idx, 'season_rank'] = 999
            else:
                # Fallback to ESPN season projection if WinWithOdds not found
                espn_player_obj = all_espn_players.get(player_name) # Use the pre-fetched dictionary
                if espn_player_obj and hasattr(espn_player_obj, 'stats') and 0 in espn_player_obj.stats:
                    espn_season_proj = espn_player_obj.stats[0].get('projected_points', 0)
                    if espn_season_proj is not None:
                        roster_df.at[idx, 'season_projection'] = float(espn_season_proj)
                        # For ESPN, we don't have a direct rank, so we can assign a default or calculate a rough one
                        # For now, we'll leave rank as 999 if WWO didn't provide one
        
        return roster_df

    def add_season_context_to_df(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Public method to add season-long projection context to any DataFrame.
        """
        return self._add_season_context(df)
