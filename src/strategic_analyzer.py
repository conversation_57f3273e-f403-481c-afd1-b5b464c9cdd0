#!/usr/bin/env python3
"""
Strategic Fantasy Football Analyzer
Provides Claude-style strategic insights and enhanced recommendations.
"""

import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import numpy as np

@dataclass
class TeamContext:
    """Context about the user's team for strategic analysis."""
    position_strengths: Dict[str, str]  # 'Strong', 'Adequate', 'Weak'
    bye_week_needs: List[str]
    injury_concerns: List[str]
    starter_weaknesses: List[str]
    depth_issues: List[str]
    trade_assets: List[str]

@dataclass 
class PlayerInsight:
    """Strategic insight about a specific player."""
    player_name: str
    position: str
    insight_type: str  # 'must_add', 'value_play', 'avoid', 'trade_target', 'sleeper'
    confidence: str    # 'high', 'medium', 'low'
    reasoning: str
    action: str       # specific recommendation
    urgency: int      # 1-10 scale
    
class StrategicAnalyzer:
    """
    Provides advanced strategic analysis with Claude-style insights.
    Goes beyond basic projections to consider team context, market trends,
    positional scarcity, and strategic timing.
    """
    
    def __init__(self):
        # Position scarcity weights (higher = more scarce/valuable)
        self.position_scarcity = {
            'QB': 0.7,  # Deep position
            'RB': 1.2,  # Moderate scarcity
            'WR': 0.9,  # Moderate depth  
            'TE': 1.4,  # Very scarce
            'K': 0.3,   # Streamer position
            'D/ST': 0.5 # Streamer position
        }
        
        # Week-specific context (Week 1 considerations)
        self.week_context = {
            'week': 1,
            'preseason_hype_discount': 0.1,  # Discount overhyped preseason players
            'proven_veteran_bonus': 0.15,    # Bonus for proven players
            'rookie_uncertainty': 0.2,       # Extra uncertainty for rookies
            'injury_risk_early': 0.05        # Lower injury concerns early
        }
    
    def analyze_team_context(self, roster_df: pd.DataFrame, week: int = 1) -> TeamContext:
        """Analyze team context to understand strategic needs."""
        # Filter out IR players for active roster analysis
        active_roster = roster_df[roster_df['injury_status'] != 'INJURY_RESERVE'].copy()
        
        position_strengths = {}
        starter_weaknesses = []
        depth_issues = []
        trade_assets = []
        
        for pos in ['QB', 'RB', 'WR', 'TE', 'K', 'D/ST']:
            pos_players = active_roster[active_roster['position'] == pos]
            
            if pos_players.empty:
                position_strengths[pos] = 'Critical'
                depth_issues.append(pos)
                continue
            
            # Analyze position strength
            player_count = len(pos_players)
            avg_proj = pos_players['external_proj'].fillna(pos_players['projected_points']).mean()
            top_proj = pos_players['external_proj'].fillna(pos_players['projected_points']).max()
            
            # Position-specific thresholds (count, avg_projection)
            thresholds = {
                'QB': {'strong': (1, 15), 'adequate': (1, 12)},
                'RB': {'strong': (3, 9), 'adequate': (2, 7)},
                'WR': {'strong': (3, 8), 'adequate': (2, 6)},  # More lenient for WR
                'TE': {'strong': (2, 8), 'adequate': (1, 6)},
                'K': {'strong': (1, 8), 'adequate': (1, 6)},
                'D/ST': {'strong': (1, 7), 'adequate': (1, 5)}  # More lenient for D/ST
            }
            
            pos_threshold = thresholds.get(pos, {'strong': (2, 10), 'adequate': (1, 8)})
            
            if (player_count >= pos_threshold['strong'][0] and 
                avg_proj >= pos_threshold['strong'][1]):
                position_strengths[pos] = 'Strong'
                # Strong positions are potential trade assets
                if pos in ['RB', 'WR'] and player_count >= 3:  # Lower threshold for trade assets
                    # Get top 2 players for potential trades, but only if they're genuinely good
                    best_players = pos_players.nlargest(2, 'external_proj')
                    for _, player in best_players.iterrows():
                        player_proj = player.get('external_proj', player.get('projected_points', 0))
                        if player_proj >= 10:  # Only consider players with decent projections as trade assets
                            trade_assets.append(player['name'])
                    
            elif (player_count >= pos_threshold['adequate'][0] and 
                  avg_proj >= pos_threshold['adequate'][1]):
                position_strengths[pos] = 'Adequate'
            else:
                position_strengths[pos] = 'Weak'
                depth_issues.append(pos)
                
                # Identify specific starter weaknesses
                starters = pos_players[pos_players.get('is_starting', False)]
                if not starters.empty and starters['external_proj'].fillna(starters['projected_points']).max() < pos_threshold['adequate'][1]:
                    starter_weaknesses.append(pos)
        
        return TeamContext(
            position_strengths=position_strengths,
            bye_week_needs=[],  # TODO: Calculate bye week needs
            injury_concerns=[],  # TODO: Identify injury-prone players
            starter_weaknesses=starter_weaknesses,
            depth_issues=depth_issues,
            trade_assets=trade_assets
        )
    
    def generate_strategic_insights(self, 
                                   waiver_df: pd.DataFrame, 
                                   team_context: TeamContext,
                                   roster_df: pd.DataFrame) -> List[PlayerInsight]:
        """Generate strategic insights for waiver wire players."""
        insights = []
        
        # Filter to available players only
        available_players = waiver_df[waiver_df['status'] == 'Available'].copy()
        
        for _, player in available_players.iterrows():
            insight = self._analyze_player_strategically(player, team_context, roster_df)
            if insight:
                insights.append(insight)
        
        # Sort insights by urgency and confidence
        insights.sort(key=lambda x: (x.urgency, x.confidence == 'high'), reverse=True)
        
        return insights[:15]  # Return top 15 insights
    
    def _analyze_player_strategically(self, 
                                     player: pd.Series, 
                                     team_context: TeamContext,
                                     roster_df: pd.DataFrame) -> Optional[PlayerInsight]:
        """Analyze a single player with strategic context."""
        
        name = player['name']
        position = player['position']
        
        # Skip invalid players
        if not name or name == 'UNKNOWN' or pd.isna(name):
            return None
        
        weekly_proj = float(player.get('external_proj', player.get('projected_points', 0)))
        season_proj = float(player.get('season_projection', 0))
        ownership = float(player.get('percent_owned', 0))
        trend_cat = player.get('trend_category', 'neutral')
        
        # Calculate strategic scores
        positional_need_score = self._calculate_positional_need(position, team_context)
        value_score = self._calculate_player_value(player, roster_df, position)
        market_timing_score = self._calculate_market_timing(ownership, trend_cat)
        upside_score = self._calculate_upside_potential(player)
        
        # Overall strategic score (0-100)
        strategic_score = (
            positional_need_score * 0.3 +
            value_score * 0.25 +
            market_timing_score * 0.2 +
            upside_score * 0.25
        )
        
        # Generate insight based on score and context
        return self._create_player_insight(
            player, strategic_score, positional_need_score, 
            value_score, market_timing_score, team_context, roster_df
        )
    
    def _calculate_positional_need(self, position: str, team_context: TeamContext) -> float:
        """Calculate how badly this position is needed (0-100)."""
        position_strength = team_context.position_strengths.get(position, 'Weak')
        
        base_scores = {
            'Critical': 100,
            'Weak': 80,
            'Adequate': 40,
            'Strong': 20
        }
        
        base_score = base_scores.get(position_strength, 50)
        
        # Adjust for position scarcity
        scarcity_multiplier = self.position_scarcity.get(position, 1.0)
        
        # Extra boost if it's a starter weakness
        if position in team_context.starter_weaknesses:
            base_score += 20
        
        return min(100, base_score * scarcity_multiplier)
    
    def _calculate_player_value(self, player: pd.Series, roster_df: pd.DataFrame, position: str) -> float:
        """Calculate player value vs roster alternatives (0-100)."""
        weekly_proj = float(player.get('external_proj', player.get('projected_points', 0)))
        season_proj = float(player.get('season_projection', 0))
        
        # Compare against roster players at same position
        pos_players = roster_df[roster_df['position'] == position]
        
        if pos_players.empty:
            return 90  # High value if no competition
        
        # Find weakest roster player at position
        weakest_proj = pos_players['external_proj'].fillna(pos_players['projected_points']).min()
        avg_proj = pos_players['external_proj'].fillna(pos_players['projected_points']).mean()
        
        # Calculate improvement potential
        weekly_upgrade = max(0, weekly_proj - weakest_proj)
        season_upgrade = max(0, season_proj - (avg_proj * 17))  # Rough season conversion
        
        # Score based on upgrade potential
        value_score = min(100, (weekly_upgrade * 8) + (season_upgrade * 0.3))
        
        return value_score
    
    def _calculate_market_timing(self, ownership: float, trend_cat: str) -> float:
        """Calculate market timing score (0-100)."""
        base_score = 50
        
        # Ownership scoring (lower ownership = higher score)
        if ownership < 10:
            ownership_score = 90
        elif ownership < 25:
            ownership_score = 75
        elif ownership < 50:
            ownership_score = 60
        elif ownership < 75:
            ownership_score = 40
        else:
            ownership_score = 20
        
        # Trend scoring
        trend_bonus = 0
        if trend_cat == 'buy_low':
            trend_bonus = 25
        elif trend_cat == 'sell_high':
            trend_bonus = -15
        
        return min(100, ownership_score + trend_bonus)
    
    def _calculate_upside_potential(self, player: pd.Series) -> float:
        """Calculate ceiling/upside potential (0-100)."""
        weekly_proj = float(player.get('external_proj', player.get('projected_points', 0)))
        season_proj = float(player.get('season_projection', 0))
        dfs_value = float(player.get('dfs_value', 0))
        
        # Base upside from projections
        upside_score = min(50, weekly_proj * 2.5)
        
        # Season projection upside
        if season_proj > 200:  # Strong season projection
            upside_score += 20
        elif season_proj > 150:
            upside_score += 10
        
        # DFS value indicates efficiency
        if dfs_value >= 2.5:
            upside_score += 15
        elif dfs_value >= 2.3:
            upside_score += 8
        
        return min(100, upside_score)
    
    def _create_player_insight(self, 
                              player: pd.Series,
                              strategic_score: float,
                              need_score: float,
                              value_score: float,
                              timing_score: float,
                              team_context: TeamContext,
                              roster_df: pd.DataFrame) -> PlayerInsight:
        """Create a strategic insight for a player."""
        
        name = player['name']
        position = player['position']
        weekly_proj = float(player.get('external_proj', player.get('projected_points', 0)))
        ownership = float(player.get('percent_owned', 0))
        trend_cat = player.get('trend_category', 'neutral')
        
        # Determine insight type and confidence (more lenient thresholds)
        if strategic_score >= 80:
            insight_type = 'must_add'
            confidence = 'high'
            urgency = 9
        elif strategic_score >= 65:
            insight_type = 'value_play'
            confidence = 'high' if need_score > 60 else 'medium'
            urgency = 7
        elif strategic_score >= 50:
            if trend_cat == 'buy_low':
                insight_type = 'sleeper'
                confidence = 'medium'
                urgency = 5
            else:
                insight_type = 'value_play'
                confidence = 'medium'
                urgency = 6
        elif strategic_score >= 35:
            insight_type = 'trade_target'
            confidence = 'low'
            urgency = 3
        elif weekly_proj >= 12:  # Even if low strategic score, high projection players are worth noting
            insight_type = 'value_play'
            confidence = 'low'
            urgency = 4
        else:
            insight_type = 'avoid'
            confidence = 'medium'
            urgency = 1
        
        # Generate reasoning
        reasoning = self._generate_reasoning(
            player, strategic_score, need_score, value_score, 
            timing_score, team_context, insight_type
        )
        
        # Generate action
        action = self._generate_action(player, insight_type, team_context, roster_df)
        
        return PlayerInsight(
            player_name=name,
            position=position,
            insight_type=insight_type,
            confidence=confidence,
            reasoning=reasoning,
            action=action,
            urgency=urgency
        )
    
    def _generate_reasoning(self, 
                           player: pd.Series,
                           strategic_score: float,
                           need_score: float,
                           value_score: float,
                           timing_score: float,
                           team_context: TeamContext,
                           insight_type: str) -> str:
        """Generate human-readable reasoning for the recommendation."""
        
        position = player['position']
        weekly_proj = float(player.get('external_proj', player.get('projected_points', 0)))
        ownership = float(player.get('percent_owned', 0))
        trend_cat = player.get('trend_category', 'neutral')
        
        reasons = []
        
        # Position need reasoning
        pos_strength = team_context.position_strengths.get(position, 'Adequate')
        if pos_strength in ['Critical', 'Weak']:
            reasons.append(f"{position} is a team weakness")
        elif position in team_context.starter_weaknesses:
            reasons.append(f"upgrades weak {position} starter")
        
        # Value reasoning
        if weekly_proj >= 15:
            reasons.append(f"elite {weekly_proj:.1f} projection")
        elif weekly_proj >= 12:
            reasons.append(f"strong {weekly_proj:.1f} projection")
        elif weekly_proj >= 8:
            reasons.append(f"solid {weekly_proj:.1f} projection")
        
        # Market timing reasoning
        if ownership < 15:
            reasons.append("low ownership gem")
        elif ownership < 30:
            reasons.append("undervalued asset")
        
        if trend_cat == 'buy_low':
            reasons.append("buy-low opportunity")
        elif trend_cat == 'sell_high':
            reasons.append("potential regression risk")
        
        # Insight-specific reasoning
        if insight_type == 'must_add':
            reasons.append("addresses critical need")
        elif insight_type == 'sleeper':
            reasons.append("high ceiling potential")
        elif insight_type == 'avoid':
            if strategic_score < 30:
                reasons.append("limited upside")
            else:
                reasons.append("better options available")
        
        return ", ".join(reasons) if reasons else "standard waiver consideration"
    
    def _find_drop_candidate(self, position: str, roster_df: pd.DataFrame, target_proj: float) -> Optional[str]:
        """Find the best drop candidate for a given position."""
        # Filter out IR players and get active roster
        active_roster = roster_df[
            roster_df['injury_status'] != 'INJURY_RESERVE'
        ].copy()
        
        # Look for drop candidates by position priority
        position_priority = {
            position: 1.0,  # Same position gets priority
            'K': 0.3,       # Kickers are most droppable
            'D/ST': 0.4,    # Defenses are droppable
        }
        
        # Add flex position considerations
        if position in ['RB', 'WR', 'TE']:
            position_priority.update({'RB': 0.8, 'WR': 0.8, 'TE': 0.9})
        
        drop_candidates = []
        
        for _, roster_player in active_roster.iterrows():
            roster_pos = roster_player['position']
            roster_proj = float(roster_player.get('external_proj', roster_player.get('projected_points', 0)))
            
            # Skip if this player is significantly better than target
            if roster_proj > target_proj + 2:
                continue
            
            # Calculate drop priority (higher = more droppable)
            priority = position_priority.get(roster_pos, 0.5)
            
            # Boost priority for bench players
            if not roster_player.get('is_starting', False):
                priority += 0.2
            
            # Lower priority for good projections
            if roster_proj >= 10:
                priority -= 0.2
            elif roster_proj >= 8:
                priority -= 0.1
            
            drop_candidates.append({
                'name': roster_player['name'],
                'position': roster_pos,
                'projection': roster_proj,
                'priority': priority,
                'is_starter': roster_player.get('is_starting', False)
            })
        
        # Sort by drop priority (highest first) then by projection (lowest first)
        drop_candidates.sort(key=lambda x: (x['priority'], -x['projection']), reverse=True)
        
        if drop_candidates:
            return drop_candidates[0]['name']
        
        return None

    def _generate_action(self, player: pd.Series, insight_type: str, team_context: TeamContext, roster_df: pd.DataFrame = None) -> str:
        """Generate specific action recommendation with drop suggestions."""
        
        name = player['name']
        position = player['position']
        weekly_proj = float(player.get('external_proj', player.get('projected_points', 0)))
        
        # Find drop candidate if we're recommending an add
        drop_candidate = None
        if insight_type in ['must_add', 'value_play', 'sleeper'] and roster_df is not None:
            drop_candidate = self._find_drop_candidate(position, roster_df, weekly_proj)
        
        if insight_type == 'must_add':
            if drop_candidate:
                return f"🔥 ADD {name} (DROP {drop_candidate}) - critical need"
            else:
                return f"🔥 ADD {name} immediately - critical need"
        elif insight_type == 'value_play':
            if drop_candidate:
                return f"💎 ADD {name} (DROP {drop_candidate}) - excellent value"
            else:
                return f"💎 ADD {name} - excellent value"
        elif insight_type == 'sleeper':
            if drop_candidate:
                return f"🌟 STASH {name} (DROP {drop_candidate}) - breakout candidate"
            else:
                return f"🌟 STASH {name} - breakout candidate"
        elif insight_type == 'trade_target':
            return f"🎯 TRADE for {name} - good value"
        else:
            return f"⚠️ AVOID {name} - better options exist"
    
    def enhance_waiver_recommendations(self, 
                                     waiver_df: pd.DataFrame,
                                     roster_df: pd.DataFrame) -> pd.DataFrame:
        """Enhance the existing waiver DataFrame with strategic insights."""
        
        # Analyze team context
        team_context = self.analyze_team_context(roster_df)
        
        # Generate insights
        insights = self.generate_strategic_insights(waiver_df, team_context, roster_df)
        
        # Create insights lookup
        insights_dict = {insight.player_name: insight for insight in insights}
        
        # Enhance DataFrame with strategic recommendations
        enhanced_df = waiver_df.copy()
        
        for idx, row in enhanced_df.iterrows():
            player_name = row['name']
            
            if player_name in insights_dict:
                insight = insights_dict[player_name]
                
                # Override the basic recommendation with strategic one
                enhanced_df.at[idx, 'recommendation'] = insight.action
                enhanced_df.at[idx, 'strategic_reasoning'] = insight.reasoning
                enhanced_df.at[idx, 'confidence'] = insight.confidence
                enhanced_df.at[idx, 'urgency'] = insight.urgency
                enhanced_df.at[idx, 'insight_type'] = insight.insight_type
        
        return enhanced_df