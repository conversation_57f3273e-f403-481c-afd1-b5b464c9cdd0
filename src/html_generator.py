"""
HTML Report Generator for Fantasy Football Waiver Wire Analysis

Generates professional FantasyPros-style HTML cheatsheets with interactive sorting,
dual projections, flex analysis, and FanDuel salary integration.
"""

import pandas as pd
from typing import Dict, Any
from datetime import datetime
from .html_builder import HtmlBuilder
from jinja2 import Environment, FileSystemLoader
from src.post_week_analyzer import WeekSummary

class HtmlGenerator:
    """Generates interactive HTML reports for fantasy football analysis."""
    
    def __init__(self, template_path: str = "templates"):
        """Initialize the HTML generator."""
        self.builder = HtmlBuilder()
        self.template_path = template_path
        self.env = Environment(loader=FileSystemLoader(template_path))

    def _read_template(self, template_name: str):
        """Load an HTML template using Jinja2."""
        return self.env.get_template(template_name)

    def generate_waiver_cheatsheet(self, suggestions: Dict[str, Any], week: int) -> str:
        """
        Generate comprehensive waiver wire cheatsheet HTML.
        
        Args:
            suggestions: Dictionary containing waiver analysis and team data
            week: NFL week number
            
        Returns:
            Complete HTML string for waiver cheatsheet
        """
        waiver_df = suggestions.get('waiver_analysis', pd.DataFrame())
        my_team = suggestions.get('my_team')
        team_name = my_team.team_name if my_team else 'My Team'
        report_type = "Waiver Wire"

        if waiver_df.empty:
            return self._generate_empty_report(week, team_name, report_type)

        available_players = waiver_df[waiver_df['on_team'] == False]
        roster_players = waiver_df[waiver_df['on_team'] == True]

        # Build HTML sections
        positions = ['QB', 'RB', 'WR', 'TE', 'K', 'D/ST']
        position_sections = "".join(self.builder.generate_position_section_content(p, available_players, roster_players) for p in positions)
        flex_section = self.builder.generate_flex_section_content(available_players, roster_players)

        # Populate body template
        body_template = self._read_template("waiver_cheatsheet.html")
        body = body_template.render(
            report_type=report_type,
            week=week,
            team_name=team_name,
            position_sections=position_sections,
            flex_section=flex_section
        )

        # Return full HTML page
        return self._read_template("base.html").render(title=f"{report_type} Cheatsheet - Week {week}", body=body)
    

    def generate_start_sit_cheatsheet(self, optimal_lineup: Dict[str, pd.DataFrame], my_team: Any, week: int, opponent_optimal_lineup: Dict[str, pd.DataFrame], my_team_bench: pd.DataFrame, opponent_team_bench: pd.DataFrame) -> str:
        """
        Generate comprehensive start/sit cheatsheet HTML for rostered players.
        
        Args:
            optimal_lineup: Dictionary containing optimal lineup recommendations for user's team.
            my_team: The user's team object.
            week: NFL week number.
            opponent_optimal_lineup: Dictionary containing optimal lineup recommendations for opponent's team.
            
        Returns:
            Complete HTML string for start/sit cheatsheet.
        """
        team_name = my_team.team_name if my_team else 'My Team'
        report_type = "Start/Sit"

        if not optimal_lineup and not opponent_optimal_lineup:
            return self._generate_empty_report(week, team_name, report_type)

        # Calculate total projected points
        user_total_projected_points = sum(slot_df['external_proj'].iloc[0] for slot_df in optimal_lineup.values() if not slot_df.empty)
        opponent_total_projected_points = sum(slot_df['external_proj'].iloc[0] for slot_df in opponent_optimal_lineup.values() if not slot_df.empty)

        # Build HTML sections
        my_lineup_html = self.builder.generate_optimal_lineup_section_content(optimal_lineup, "My Optimal Lineup")
        my_total_points_html = self.builder.generate_total_points_section_content(user_total_projected_points, "My Total Projected Points")
        my_bench_html = self.builder.generate_bench_section_content(my_team_bench, "My Bench")

        opponent_lineup_html = self.builder.generate_optimal_lineup_section_content(opponent_optimal_lineup, "Opponent's Optimal Lineup")
        opponent_total_points_html = self.builder.generate_total_points_section_content(opponent_total_projected_points, "Opponent's Total Projected Points")
        opponent_bench_html = self.builder.generate_bench_section_content(opponent_team_bench, "Opponent's Bench")

        # Populate body template
        body_template = self._read_template("start_sit_cheatsheet.html")
        body = body_template.render(
            report_type=report_type,
            week=week,
            team_name=team_name,
            my_lineup=my_lineup_html,
            my_total_points=my_total_points_html,
            my_bench=my_bench_html,
            opponent_lineup=opponent_lineup_html,
            opponent_total_points=opponent_total_points_html,
            opponent_bench=opponent_bench_html
        )

        # Return full HTML page
        return self._read_template("base.html").render(title=f"{report_type} Cheatsheet - Week {week}", body=body)

    def generate_postweek_report(self, summary: WeekSummary):
        """Generate HTML report for post-week analysis using Jinja2 template"""
        try:
            # Use Jinja2 template
            body_template = self._read_template("postweek_analysis.html")
            body = body_template.render(
                week=summary.week,
                timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                summary=summary
            )
            
            # Return full HTML page using base template
            return self._read_template("base.html").render(
                title=f"Week {summary.week} Post-Game Analysis",
                body=body
            )
        except Exception as e:
            # Fallback to simple HTML if template fails
            print(f"Warning: Template rendering failed ({e}), using fallback HTML")
            return f"""
<!DOCTYPE html>
<html>
<head>
    <title>Week {summary.week} Post-Week Analysis</title>
    <style>body {{ font-family: Arial, sans-serif; margin: 20px; }}</style>
</head>
<body>
    <h1>📊 Week {summary.week} Post-Week Analysis</h1>
    <p><strong>Players Analyzed:</strong> {summary.total_players_analyzed}</p>
    <p><strong>Overall MAE:</strong> {summary.overall_mae:.2f}</p>
    <p><strong>Most Accurate Source:</strong> {summary.most_accurate_source}</p>
    <p>Template rendering failed. Please check template configuration.</p>
</body>
</html>
            """

    def _generate_empty_report(self, week: int, team_name: str, report_type: str = "Waiver Wire") -> str:
        """Generate empty report when no data is available."""
        body = f"""
    <div style="text-align: center; padding: 50px;">
        <h1> {report_type} Cheatsheet</h1>
        <h2>Week {week} • {team_name}</h2>
        <p>No {report_type.lower()} data available at this time.</p>
    </div>
"""
        return self._read_template("base.html").render(title=f"{report_type} Cheatsheet - Week {week}", body=body)
