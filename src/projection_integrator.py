#!/usr/bin/env python3
"""
Consolidated Projection Integration Module

Integrates existing projection data from multiple sources into enhanced projection storage:
- FanDuel (cached GraphQL data)
- DraftSharks (flex ratings CSV)
- WinWithOdds (season projections and DFS values)

This replaces all the one-off extraction scripts with a proper module.
"""

import pandas as pd
import json
import os
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path

from .enhanced_projection_storage import EnhancedProjectionStorage, StrategicProjectionRecord


class ProjectionIntegrator:
    """Consolidates projection data from multiple sources into enhanced storage"""
    
    def __init__(self):
        self.storage = EnhancedProjectionStorage()
        self.cache_dir = Path("cache")
        
    def integrate_all_sources(self, week: int = 1, year: int = 2025) -> Dict[str, int]:
        """Integrate all available projection sources"""
        
        print("🔄 Starting consolidated projection integration...")
        
        results = {}
        
        # Integrate FanDuel projections
        fanduel_count = self.integrate_fanduel_projections(week, year)
        results['FanDuel'] = fanduel_count
        
        # Integrate DraftSharks flex ratings
        draftsharks_count = self.integrate_draftsharks_projections(week, year)
        results['DraftSharks'] = draftsharks_count
        
        # Integrate WinWithOdds projections
        wwo_count = self.integrate_winwithodds_projections(week, year)
        results['WinWithOdds'] = wwo_count
        
        total_count = sum(results.values())
        print(f"✅ Integrated {total_count} total projections from {len(results)} sources")
        
        # Show breakdown
        for source, count in results.items():
            print(f"  📊 {source}: {count} projections")
            
        return results
    
    def integrate_fanduel_projections(self, week: int = 1, year: int = 2025) -> int:
        """Integrate cached FanDuel GraphQL projection data"""
        
        print("🔄 Integrating FanDuel projections...")
        
        records = []
        
        # Load skill position data (PPR and Weekly)
        skill_files = [
            'fanduel_projections_NFL_SKILL_PPR.json',
            'fanduel_projections_NFL_SKILL_WEEKLY.json'
        ]
        
        for filename in skill_files:
            filepath = self.cache_dir / filename
            if filepath.exists():
                with open(filepath, 'r') as f:
                    data = json.load(f)
                    records.extend(self._process_fanduel_skill_data(data, week, year, filename))
        
        # Load DST data
        dst_file = self.cache_dir / 'fanduel_projections_NFL_D_ST_WEEKLY.json'
        if dst_file.exists():
            with open(dst_file, 'r') as f:
                data = json.load(f)
                records.extend(self._process_fanduel_dst_data(data, week, year))
        
        # Load Kicker data
        k_file = self.cache_dir / 'fanduel_projections_NFL_KICKER_WEEKLY.json'
        if k_file.exists():
            with open(k_file, 'r') as f:
                data = json.load(f)
                records.extend(self._process_fanduel_kicker_data(data, week, year))
        
        # Store in enhanced projection storage
        if records:
            count = self.storage._store_projections(records)
            print(f"  ✅ Stored {count} FanDuel projections")
            return count
        else:
            print("  ⚠️  No FanDuel data found in cache")
            return 0
    
    def integrate_draftsharks_projections(self, week: int = 1, year: int = 2025) -> int:
        """Integrate DraftSharks flex ratings CSV data"""
        
        print("🔄 Integrating DraftSharks projections...")
        
        csv_file = self.cache_dir / 'ds-weekly-rankings.csv'
        if not csv_file.exists():
            print("  ⚠️  DraftSharks CSV not found in cache")
            return 0
        
        df = pd.read_csv(csv_file)
        records = []
        
        for _, row in df.iterrows():
            # Extract team from matchup (e.g., "SF" from "@SEA" or " TB")
            matchup = row.get('Matchup', '')
            team = row.get('Team', '')
            
            raw_data = {
                'matchup': matchup,
                'sos': row.get('SOS', ''),
                'rush_yards': row.get('Rush Yds', 0),
                'rush_tds': row.get('Rush TDs', 0),
                'receptions': row.get('Rec', 0),
                'rec_yards': row.get('Rec Yds', 0),
                'rec_tds': row.get('Rec TDs', 0),
                'fanduel_salary': row.get('FanDuel $', 0),
                'fanduel_value': row.get('$ / Point', 0),
                'draftkings_salary': row.get('DraftKings $', 0),
                'draftkings_value': row.get('$ / Point ', 0),
                'floor': row.get('Floor', 0),
                'ceiling': row.get('Ceil', 0),
                'consistency': row.get('Cons.', 0),
                '3d_projection': row.get('3D Proj.', 0)
            }
            
            record = StrategicProjectionRecord(
                week=week,
                year=year,
                player_id=None,
                player_name=row.get('Player', ''),
                position=row.get('Pos.', ''),
                team=team,
                source='DraftSharks',
                projected_points=float(row.get('Proj', 0) or 0),
                raw_source_data=raw_data
            )
            records.append(record)
        
        # Store in enhanced projection storage
        if records:
            count = self.storage._store_projections(records)
            print(f"  ✅ Stored {count} DraftSharks projections")
            return count
        else:
            print("  ⚠️  No DraftSharks data to process")
            return 0
    
    def integrate_winwithodds_projections(self, week: int = 1, year: int = 2025) -> int:
        """Integrate WinWithOdds season projections and DFS values"""
        
        print("🔄 Integrating WinWithOdds projections...")
        
        records = []
        
        # Load season projections
        season_file = self.cache_dir / 'winwithodds_season_projections.json'
        if season_file.exists():
            with open(season_file, 'r') as f:
                data = json.load(f)
                records.extend(self._process_wwo_season_data(data, week, year))
        
        # Load DFS values
        dfs_file = self.cache_dir / 'winwithodds_dfs_values.json'
        if dfs_file.exists():
            with open(dfs_file, 'r') as f:
                data = json.load(f)
                records.extend(self._process_wwo_dfs_data(data, week, year))
        
        # Store in enhanced projection storage
        if records:
            count = self.storage._store_projections(records)
            print(f"  ✅ Stored {count} WinWithOdds projections")
            return count
        else:
            print("  ⚠️  No WinWithOdds data found in cache")
            return 0
    
    def _process_fanduel_skill_data(self, data: dict, week: int, year: int, filename: str) -> List[StrategicProjectionRecord]:
        """Process FanDuel skill position JSON data"""
        records = []
        
        # Handle different FanDuel data formats
        player_data = []
        
        if 'data' in data and 'getProjections' in data['data']:
            player_data = data['data']['getProjections']
        elif 'data' in data and 'players' in data['data']:
            player_data = data['data']['players']
        elif isinstance(data, list):
            player_data = data
        
        for player in player_data:
            # Extract player info - handle different formats
            if 'player' in player and 'name' in player['player']:
                player_name = player['player']['name']
                position = player['player'].get('position', '')
            else:
                first_name = player.get('firstName', '')
                last_name = player.get('lastName', '')
                player_name = f"{first_name} {last_name}".strip()
                position = player.get('position', '')
            
            # Extract team
            if 'team' in player and 'abbreviation' in player['team']:
                team = player['team']['abbreviation']
            else:
                team = player.get('team', '')
            
            # Extract fantasy points
            fantasy_points = 0
            if 'fantasy' in player:
                fantasy_points = float(player['fantasy'])
            elif 'projectedStats' in player:
                for stat in player['projectedStats']:
                    if stat.get('stat') == 'FPPG':
                        fantasy_points = float(stat.get('value', 0))
                        break
            
            raw_data = {
                'player_info': player.get('player', {}),
                'team_info': player.get('team', {}),
                'game_info': player.get('gameInfo', {}),
                'salary': player.get('salary', 'N/A'),
                'value': player.get('value', 'N/A'),
                'projected_stats': player.get('projectedStats', []),
                'source_file': filename
            }
            
            record = StrategicProjectionRecord(
                week=week,
                year=year,
                player_id=player.get('id'),
                player_name=player_name,
                position=position,
                team=team,
                source='FanDuel',
                projected_points=fantasy_points,
                raw_source_data=raw_data
            )
            records.append(record)
        
        return records
    
    def _process_fanduel_dst_data(self, data: dict, week: int, year: int) -> List[StrategicProjectionRecord]:
        """Process FanDuel DST JSON data"""
        records = []
        
        if 'data' in data and 'players' in data['data']:
            for player in data['data']['players']:
                team_name = player.get('lastName', '')  # DST uses lastName as team name
                
                # Extract FPPG projection
                fppg = 0
                for stat in player.get('projectedStats', []):
                    if stat.get('stat') == 'FPPG':
                        fppg = float(stat.get('value', 0))
                        break
                
                raw_data = {
                    'fanduel_id': player.get('id'),
                    'team_name': team_name,
                    'projected_stats': player.get('projectedStats', []),
                    'salary': player.get('salary'),
                    'game_info': player.get('gameInfo', {})
                }
                
                record = StrategicProjectionRecord(
                    week=week,
                    year=year,
                    player_id=player.get('id'),
                    player_name=team_name,
                    position='DST',
                    team=team_name.split()[-1] if team_name else '',
                    source='FanDuel',
                    projected_points=fppg,
                    raw_source_data=raw_data
                )
                records.append(record)
        
        return records
    
    def _process_fanduel_kicker_data(self, data: dict, week: int, year: int) -> List[StrategicProjectionRecord]:
        """Process FanDuel Kicker JSON data"""
        records = []
        
        if 'data' in data and 'players' in data['data']:
            for player in data['data']['players']:
                player_name = player.get('firstName', '') + ' ' + player.get('lastName', '')
                team = player.get('team', '')
                
                # Extract FPPG projection
                fppg = 0
                for stat in player.get('projectedStats', []):
                    if stat.get('stat') == 'FPPG':
                        fppg = float(stat.get('value', 0))
                        break
                
                raw_data = {
                    'fanduel_id': player.get('id'),
                    'first_name': player.get('firstName'),
                    'last_name': player.get('lastName'),
                    'projected_stats': player.get('projectedStats', []),
                    'salary': player.get('salary'),
                    'game_info': player.get('gameInfo', {})
                }
                
                record = StrategicProjectionRecord(
                    week=week,
                    year=year,
                    player_id=player.get('id'),
                    player_name=player_name.strip(),
                    position='K',
                    team=team,
                    source='FanDuel',
                    projected_points=fppg,
                    raw_source_data=raw_data
                )
                records.append(record)
        
        return records
    
    def _process_wwo_season_data(self, data: dict, week: int, year: int) -> List[StrategicProjectionRecord]:
        """Process WinWithOdds season projections"""
        records = []
        
        for player_data in data:
            raw_data = {
                'season_projection': player_data.get('season_proj'),
                'season_rank': player_data.get('season_rank'),
                'adp': player_data.get('adp'),
                'value_over_adp': player_data.get('value_over_adp'),
                'breakout_probability': player_data.get('breakout_prob'),
                'injury_risk': player_data.get('injury_risk')
            }
            
            record = StrategicProjectionRecord(
                week=week,
                year=year,
                player_id=None,
                player_name=player_data.get('name', ''),
                position=player_data.get('position', ''),
                team=player_data.get('team', ''),
                source='WinWithOdds',
                projected_points=float(player_data.get('season_proj', 0) or 0) / 17,  # Convert to weekly
                raw_source_data=raw_data
            )
            records.append(record)
        
        return records
    
    def _process_wwo_dfs_data(self, data: dict, week: int, year: int) -> List[StrategicProjectionRecord]:
        """Process WinWithOdds DFS values"""
        records = []
        
        for player_data in data:
            raw_data = {
                'dfs_value': player_data.get('dfs_value'),
                'salary': player_data.get('salary'),
                'ownership': player_data.get('ownership'),
                'ceil_projection': player_data.get('ceil_proj'),
                'floor_projection': player_data.get('floor_proj')
            }
            
            record = StrategicProjectionRecord(
                week=week,
                year=year,
                player_id=None,
                player_name=player_data.get('name', ''),
                position=player_data.get('position', ''),
                team=player_data.get('team', ''),
                source='WinWithOdds-DFS',
                projected_points=float(player_data.get('projection', 0) or 0),
                raw_source_data=raw_data
            )
            records.append(record)
        
        return records


def main():
    """Run the consolidated projection integration"""
    
    integrator = ProjectionIntegrator()
    results = integrator.integrate_all_sources(week=1, year=2025)
    
    print(f"\n📈 Projection integration complete!")
    print(f"Total projections integrated: {sum(results.values())}")


if __name__ == "__main__":
    main()