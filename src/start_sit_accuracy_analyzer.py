"""
Start/Sit Accuracy Analysis Module

Analyzes the accuracy of start/sit decisions across all teams in the league.
Tracks which teams make optimal lineup decisions and identifies patterns in
lineup construction that lead to better or worse fantasy outcomes.

Key Features:
- Team-level start/sit performance tracking
- Multi-source projection comparison for lineup optimization
- League-wide start/sit intelligence and rankings
- Points left on bench analysis
- Historical trend tracking for decision-making quality
- Integration with existing post-week analysis system
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime
from espn_api.football import League
import logging

@dataclass
class StartSitDecision:
    """Individual start/sit decision for a player"""
    team_name: str
    player_name: str
    position: str
    week: int
    was_started: bool
    actual_points: float
    bench_points: float  # Points scored by the player who was benched for this player
    decision_impact: float  # Positive if good decision, negative if bad
    projection_source: str
    projected_points: float

@dataclass
class TeamStartSitPerformance:
    """Team-level start/sit performance for a specific week"""
    team_name: str
    week: int
    total_starting_points: float
    total_bench_points: float
    points_left_on_bench: float
    optimal_lineup_points: float  # Best possible lineup points
    actual_vs_optimal_diff: float
    lineup_efficiency: float  # Percentage of optimal points achieved
    best_start_decision: Optional[StartSitDecision]
    worst_start_decision: Optional[StartSitDecision] 
    missed_opportunities: List[StartSitDecision]  # Bench players who outscored starters
    start_sit_decisions: List[StartSitDecision]  # All start/sit decisions for this team
    position_breakdown: Dict[str, Dict[str, float]]  # Points by position for starters vs bench
    # Victory analysis fields
    opponent_team_name: str = ""
    opponent_actual_points: float = 0.0
    actual_game_result: str = "Unknown"  # "Won", "Lost", "Tied"
    actual_margin: float = 0.0  # Positive if won, negative if lost
    optimal_game_result: str = "Unknown"  # What result would have been with optimal lineup
    optimal_margin: float = 0.0  # Margin with optimal lineup
    game_impact: str = "No Change"  # "Changed Loss to Win", "Changed Win to Loss", "Improved Win", "Reduced Loss", etc.
    optimal_lineup: Dict[str, str] = None  # Position -> Player name mapping for optimal lineup
    actual_lineup: Dict[str, str] = None   # Position -> Player name mapping for actual lineup
    optimal_player_data: Dict[str, Dict[str, float]] = None  # Player name -> {projected_points, actual_points} for optimal lineup
    optimal_projected_total: float = 0.0  # Total projected points for optimal lineup
    optimal_actual_total: float = 0.0     # Total actual points for optimal lineup

@dataclass
class LeagueStartSitIntelligence:
    """League-wide start/sit intelligence for a week"""
    week: int
    team_performances: List[TeamStartSitPerformance]
    league_avg_points_on_bench: float
    league_avg_lineup_efficiency: float
    best_decision_maker: str  # Team with highest lineup efficiency
    worst_decision_maker: str  # Team with lowest lineup efficiency
    most_points_left_on_bench: str
    best_start_decision_league_wide: StartSitDecision
    worst_start_decision_league_wide: StartSitDecision
    position_trends: Dict[str, Dict[str, float]]  # Which positions teams struggle most with

@dataclass
class ProjectionSourceComparison:
    """Comparison of projection sources for start/sit decisions"""
    source_name: str
    week: int
    total_decisions_tracked: int
    correct_start_decisions: int
    correct_sit_decisions: int
    overall_accuracy: float
    points_gained_vs_actual_decisions: float
    position_accuracy: Dict[str, float]  # Accuracy by position
    best_position: str
    worst_position: str

class StartSitAccuracyAnalyzer:
    """
    Analyzes start/sit decision accuracy across all teams in a fantasy league.
    
    This class tracks lineup optimization decisions, identifies trends, and provides
    intelligence on which teams make the best start/sit choices.
    """
    
    def __init__(self, league: League):
        """
        Initialize the StartSitAccuracyAnalyzer
        
        Args:
            league: ESPN League instance
        """
        self.league = league
        self.logger = logging.getLogger(__name__)
        
    def analyze_team_start_sit_performance(
        self, 
        team: Any, 
        week: int,
        projection_sources: Optional[Dict[str, pd.DataFrame]] = None
    ) -> TeamStartSitPerformance:
        """
        Analyze a single team's start/sit performance for a specific week.
        
        Args:
            team: ESPN team object
            week: Week number to analyze
            projection_sources: Optional external projection data
            
        Returns:
            TeamStartSitPerformance object with detailed analysis
        """
        # Get team's box score and lineup for the week
        box_scores = self.league.box_scores(week=week)
        team_lineup = None
        team_roster = []
        
        # Find this team's matchup and opponent
        team_matchup = None
        opponent_team = None
        opponent_points = 0.0
        
        for matchup in box_scores:
            if matchup.home_team == team:
                team_lineup = matchup.home_lineup
                team_matchup = matchup
                opponent_team = matchup.away_team
                opponent_points = matchup.away_score
                break
            elif matchup.away_team == team:
                team_lineup = matchup.away_lineup
                team_matchup = matchup
                opponent_team = matchup.home_team
                opponent_points = matchup.home_score
                break
        
        if not team_lineup:
            self.logger.warning(f"Could not find lineup for team {team.team_name} in week {week}")
            return self._create_empty_performance(team.team_name, week)
        
        # Get full roster for this team (including bench players)
        team_roster = team.roster
        
        # Separate starters from bench players
        starters = []
        bench_players = []
        
        for player in team_lineup:
            if hasattr(player, 'slot_position') and player.slot_position not in ['BE', 'Bench', 'BN']:
                starters.append(player)
            else:
                bench_players.append(player)
        
        # Calculate basic metrics
        total_starting_points = sum(getattr(player, 'points', 0) for player in starters)
        total_bench_points = sum(getattr(player, 'points', 0) for player in bench_players)
        
        # Calculate optimal lineup points
        all_available_players = [p for p in team_lineup if getattr(p, 'points', 0) > 0]
        optimal_lineup_points = self._calculate_optimal_lineup_points(all_available_players)
        
        # Calculate efficiency metrics
        points_left_on_bench = max(0, optimal_lineup_points - total_starting_points)
        lineup_efficiency = (total_starting_points / optimal_lineup_points * 100) if optimal_lineup_points > 0 else 0
        actual_vs_optimal_diff = total_starting_points - optimal_lineup_points
        
        # Identify specific start/sit decisions
        start_sit_decisions = self._analyze_start_sit_decisions(
            starters, bench_players, week, team.team_name, projection_sources
        )
        
        # Find best and worst decisions
        best_decision = max(start_sit_decisions, key=lambda x: x.decision_impact) if start_sit_decisions else None
        worst_decision = min(start_sit_decisions, key=lambda x: x.decision_impact) if start_sit_decisions else None
        
        # Identify missed opportunities (bench players who outscored starters)
        missed_opportunities = [d for d in start_sit_decisions if d.decision_impact < -2.0]  # Significant misses
        
        # Position breakdown
        position_breakdown = self._calculate_position_breakdown(starters, bench_players)
        
        # Calculate victory analysis
        victory_analysis = self._calculate_victory_analysis(
            team, opponent_team, total_starting_points, optimal_lineup_points, opponent_points, 
            starters, all_available_players
        )
        
        return TeamStartSitPerformance(
            team_name=team.team_name,
            week=week,
            total_starting_points=total_starting_points,
            total_bench_points=total_bench_points,
            points_left_on_bench=points_left_on_bench,
            optimal_lineup_points=optimal_lineup_points,
            actual_vs_optimal_diff=actual_vs_optimal_diff,
            lineup_efficiency=lineup_efficiency,
            best_start_decision=best_decision,
            worst_start_decision=worst_decision,
            missed_opportunities=missed_opportunities,
            start_sit_decisions=start_sit_decisions,
            position_breakdown=position_breakdown,
            # Victory analysis fields
            opponent_team_name=victory_analysis['opponent_team_name'],
            opponent_actual_points=victory_analysis['opponent_actual_points'],
            actual_game_result=victory_analysis['actual_game_result'],
            actual_margin=victory_analysis['actual_margin'],
            optimal_game_result=victory_analysis['optimal_game_result'],
            optimal_margin=victory_analysis['optimal_margin'],
            game_impact=victory_analysis['game_impact'],
            optimal_lineup=victory_analysis['optimal_lineup'],
            actual_lineup=victory_analysis['actual_lineup'],
            optimal_player_data=victory_analysis['optimal_player_data'],
            optimal_projected_total=victory_analysis['optimal_projected_total'],
            optimal_actual_total=victory_analysis['optimal_actual_total']
        )
    
    def _calculate_optimal_lineup_points(self, all_players: List[Any]) -> float:
        """
        Calculate the maximum possible points from an optimal lineup.
        
        Uses a simplified lineup structure: QB, RB1, RB2, WR1, WR2, TE, FLEX, K, D/ST
        """
        if not all_players:
            return 0.0
            
        # Group players by position
        position_groups = {}
        for player in all_players:
            pos = getattr(player, 'position', 'UNKNOWN')
            if pos not in position_groups:
                position_groups[pos] = []
            position_groups[pos].append(getattr(player, 'points', 0))
        
        # Sort each position by points (highest first)
        for pos in position_groups:
            position_groups[pos].sort(reverse=True)
        
        total_points = 0.0
        
        # Required starting positions
        lineup_requirements = {
            'QB': 1,
            'K': 1,
            'D/ST': 1,
            'TE': 1,
            'RB': 2,
            'WR': 2
        }
        
        flex_candidates = []  # RB/WR/TE eligible for flex
        
        # Fill required positions
        for pos, count in lineup_requirements.items():
            if pos in position_groups:
                # Take the top players for this position
                taken = min(count, len(position_groups[pos]))
                for i in range(taken):
                    total_points += position_groups[pos][i]
                
                # Add remaining players to flex candidates (for RB/WR/TE)
                if pos in ['RB', 'WR', 'TE'] and len(position_groups[pos]) > taken:
                    remaining = position_groups[pos][taken:]
                    flex_candidates.extend(remaining)
        
        # Fill FLEX with best remaining RB/WR/TE
        if flex_candidates:
            flex_candidates.sort(reverse=True)
            total_points += flex_candidates[0]  # Best flex option
        
        return total_points
    
    def _analyze_start_sit_decisions(
        self, 
        starters: List[Any], 
        bench_players: List[Any], 
        week: int, 
        team_name: str,
        projection_sources: Optional[Dict[str, pd.DataFrame]] = None
    ) -> List[StartSitDecision]:
        """
        Analyze individual start/sit decisions by comparing starters to bench alternatives.
        """
        decisions = []
        
        # Group bench players by position for comparison
        bench_by_position = {}
        for player in bench_players:
            pos = getattr(player, 'position', 'UNKNOWN')
            if pos not in bench_by_position:
                bench_by_position[pos] = []
            bench_by_position[pos].append(player)
        
        # Analyze each starter's decision
        for starter in starters:
            starter_pos = getattr(starter, 'position', 'UNKNOWN')
            starter_points = getattr(starter, 'points', 0)
            
            # Find best bench alternative at same position
            best_bench_alternative = None
            best_bench_points = 0
            
            # Check same position bench players
            if starter_pos in bench_by_position:
                for bench_player in bench_by_position[starter_pos]:
                    bench_points = getattr(bench_player, 'points', 0)
                    if bench_points > best_bench_points:
                        best_bench_points = bench_points
                        best_bench_alternative = bench_player
            
            # For FLEX positions, also check cross-position alternatives
            if starter_pos in ['RB', 'WR', 'TE']:
                for flex_pos in ['RB', 'WR', 'TE']:
                    if flex_pos != starter_pos and flex_pos in bench_by_position:
                        for bench_player in bench_by_position[flex_pos]:
                            bench_points = getattr(bench_player, 'points', 0)
                            if bench_points > best_bench_points:
                                best_bench_points = bench_points
                                best_bench_alternative = bench_player
            
            # Calculate decision impact
            decision_impact = starter_points - best_bench_points
            
            # Get projection data if available
            projected_points = getattr(starter, 'projected_points', 0)
            projection_source = "ESPN"  # Default
            
            decision = StartSitDecision(
                team_name=team_name,
                player_name=getattr(starter, 'name', 'Unknown'),
                position=starter_pos,
                week=week,
                was_started=True,
                actual_points=starter_points,
                bench_points=best_bench_points,
                decision_impact=decision_impact,
                projection_source=projection_source,
                projected_points=projected_points
            )
            
            decisions.append(decision)
        
        return decisions
    
    def _calculate_position_breakdown(
        self, 
        starters: List[Any], 
        bench_players: List[Any]
    ) -> Dict[str, Dict[str, float]]:
        """
        Calculate points breakdown by position for starters vs bench.
        """
        breakdown = {}
        
        # Group by position
        all_positions = set()
        for player in starters + bench_players:
            all_positions.add(getattr(player, 'position', 'UNKNOWN'))
        
        for pos in all_positions:
            starter_points = []
            bench_points = []
            
            for player in starters:
                if getattr(player, 'position', 'UNKNOWN') == pos:
                    starter_points.append(getattr(player, 'points', 0))
            
            for player in bench_players:
                if getattr(player, 'position', 'UNKNOWN') == pos:
                    bench_points.append(getattr(player, 'points', 0))
            
            breakdown[pos] = {
                'starter_total': sum(starter_points),
                'starter_avg': np.mean(starter_points) if starter_points else 0,
                'bench_total': sum(bench_points),
                'bench_avg': np.mean(bench_points) if bench_points else 0,
                'starter_count': len(starter_points),
                'bench_count': len(bench_points)
            }
        
        return breakdown
    
    def analyze_league_start_sit_intelligence(
        self, 
        week: int,
        projection_sources: Optional[Dict[str, pd.DataFrame]] = None
    ) -> LeagueStartSitIntelligence:
        """
        Analyze start/sit performance across all teams in the league for a given week.
        
        Args:
            week: Week number to analyze
            projection_sources: Optional external projection data
            
        Returns:
            LeagueStartSitIntelligence with comprehensive league analysis
        """
        team_performances = []
        
        # Analyze each team's performance
        for team in self.league.teams:
            performance = self.analyze_team_start_sit_performance(
                team, week, projection_sources
            )
            team_performances.append(performance)
        
        if not team_performances:
            self.logger.warning(f"No team performances found for week {week}")
            return self._create_empty_league_intelligence(week)
        
        # Calculate league-wide metrics
        league_avg_points_on_bench = np.mean([p.points_left_on_bench for p in team_performances])
        league_avg_lineup_efficiency = np.mean([p.lineup_efficiency for p in team_performances])
        
        # Find best and worst decision makers
        best_efficiency_team = max(team_performances, key=lambda x: x.lineup_efficiency)
        worst_efficiency_team = min(team_performances, key=lambda x: x.lineup_efficiency)
        most_points_left_team = max(team_performances, key=lambda x: x.points_left_on_bench)
        
        # Find best and worst decisions league-wide
        all_decisions = []
        for performance in team_performances:
            if performance.best_start_decision:
                all_decisions.append(performance.best_start_decision)
            if performance.worst_start_decision:
                all_decisions.append(performance.worst_start_decision)
        
        best_decision_league = max(all_decisions, key=lambda x: x.decision_impact) if all_decisions else None
        worst_decision_league = min(all_decisions, key=lambda x: x.decision_impact) if all_decisions else None
        
        # Calculate position trends across the league
        position_trends = self._calculate_league_position_trends(team_performances)
        
        return LeagueStartSitIntelligence(
            week=week,
            team_performances=team_performances,
            league_avg_points_on_bench=league_avg_points_on_bench,
            league_avg_lineup_efficiency=league_avg_lineup_efficiency,
            best_decision_maker=best_efficiency_team.team_name,
            worst_decision_maker=worst_efficiency_team.team_name,
            most_points_left_on_bench=most_points_left_team.team_name,
            best_start_decision_league_wide=best_decision_league,
            worst_start_decision_league_wide=worst_decision_league,
            position_trends=position_trends
        )
    
    def _calculate_league_position_trends(
        self, 
        team_performances: List[TeamStartSitPerformance]
    ) -> Dict[str, Dict[str, float]]:
        """
        Calculate league-wide position trends for start/sit decisions.
        """
        position_data = {}
        
        for performance in team_performances:
            for pos, breakdown in performance.position_breakdown.items():
                if pos not in position_data:
                    position_data[pos] = {
                        'total_starter_points': 0,
                        'total_bench_points': 0,
                        'total_teams': 0,
                        'points_left_on_bench': 0
                    }
                
                position_data[pos]['total_starter_points'] += breakdown['starter_total']
                position_data[pos]['total_bench_points'] += breakdown['bench_total']
                position_data[pos]['total_teams'] += 1
                
                # Points left on bench for this position
                bench_avg = breakdown['bench_avg']
                starter_avg = breakdown['starter_avg']
                if bench_avg > starter_avg:
                    position_data[pos]['points_left_on_bench'] += (bench_avg - starter_avg)
        
        # Convert to averages and insights
        trends = {}
        for pos, data in position_data.items():
            if data['total_teams'] > 0:
                trends[pos] = {
                    'avg_starter_points': data['total_starter_points'] / data['total_teams'],
                    'avg_bench_points': data['total_bench_points'] / data['total_teams'],
                    'avg_points_left_on_bench': data['points_left_on_bench'] / data['total_teams'],
                    'decision_difficulty': data['points_left_on_bench']  # Higher = harder decisions
                }
        
        return trends
    
    def compare_projection_sources_for_start_sit(
        self,
        week: int,
        projection_sources: Dict[str, pd.DataFrame]
    ) -> List[ProjectionSourceComparison]:
        """
        Compare how different projection sources would have performed for start/sit decisions.
        
        Args:
            week: Week to analyze
            projection_sources: Dict mapping source names to projection DataFrames
            
        Returns:
            List of ProjectionSourceComparison objects
        """
        comparisons = []
        
        # Get actual league performance data
        league_intelligence = self.analyze_league_start_sit_intelligence(week)
        
        for source_name, projections_df in projection_sources.items():
            comparison = self._analyze_single_projection_source(
                source_name, projections_df, league_intelligence, week
            )
            comparisons.append(comparison)
        
        return comparisons
    
    def _analyze_single_projection_source(
        self,
        source_name: str,
        projections_df: pd.DataFrame,
        league_intelligence: LeagueStartSitIntelligence,
        week: int
    ) -> ProjectionSourceComparison:
        """
        Analyze how well a single projection source would have performed for start/sit decisions.
        """
        correct_starts = 0
        correct_sits = 0
        total_decisions = 0
        points_gained = 0.0
        position_accuracy = {}
        
        # This is a simplified analysis - in reality, you'd need to simulate
        # optimal lineups using the projection source and compare to actual results
        
        # For now, return a basic structure
        return ProjectionSourceComparison(
            source_name=source_name,
            week=week,
            total_decisions_tracked=len(projections_df) if not projections_df.empty else 0,
            correct_start_decisions=correct_starts,
            correct_sit_decisions=correct_sits,
            overall_accuracy=0.0,  # Would calculate based on simulation
            points_gained_vs_actual_decisions=points_gained,
            position_accuracy=position_accuracy,
            best_position="QB",  # Placeholder
            worst_position="TE"  # Placeholder
        )
    
    def _calculate_victory_analysis(
        self, 
        team: Any, 
        opponent_team: Any, 
        actual_points: float, 
        optimal_points: float, 
        opponent_points: float,
        starters: List[Any],
        all_available_players: List[Any]
    ) -> Dict[str, Any]:
        """
        Calculate victory analysis comparing actual vs optimal lineup outcomes.
        
        Args:
            team: Team object
            opponent_team: Opponent team object  
            actual_points: Points scored with actual lineup
            optimal_points: Points that could have been scored with optimal lineup
            opponent_points: Opponent's actual points
            starters: List of starting players
            all_available_players: All available players for optimal lineup calculation
            
        Returns:
            Dictionary containing victory analysis data
        """
        # Basic opponent info
        opponent_name = opponent_team.team_name if opponent_team else "Unknown"
        
        # Calculate actual game result
        actual_margin = actual_points - opponent_points
        if actual_margin > 0:
            actual_result = "Won"
        elif actual_margin < 0:
            actual_result = "Lost"
        else:
            actual_result = "Tied"
        
        # Calculate optimal game result
        optimal_margin = optimal_points - opponent_points
        if optimal_margin > 0:
            optimal_result = "Won"
        elif optimal_margin < 0:
            optimal_result = "Lost"
        else:
            optimal_result = "Tied"
        
        # Determine game impact
        game_impact = self._determine_game_impact(actual_result, optimal_result, actual_margin, optimal_margin)
        
        # Build actual lineup mapping (exclude IR players) with proper lineup slots
        actual_lineup = {}
        position_counts = {}  # Track position counts for RB1/RB2, WR1/WR2 etc.
        
        for player in starters:
            position = getattr(player, 'position', 'UNKNOWN')
            slot = getattr(player, 'slot_position', position)
            # Exclude bench and IR players
            if slot not in ['BE', 'Bench', 'BN', 'IR']:
                # Use proper lineup slot formatting
                if slot in ['QB', 'TE', 'K', 'D/ST']:
                    # Single position slots
                    actual_lineup[slot] = player.name
                elif slot in ['RB', 'WR']:
                    # Multiple position slots - number them
                    if slot not in position_counts:
                        position_counts[slot] = 0
                    position_counts[slot] += 1
                    lineup_slot = f"{slot}{position_counts[slot]}"
                    actual_lineup[lineup_slot] = player.name
                elif 'FLEX' in slot or slot in ['RB/WR/TE', 'RB/WR', 'WR/RB/TE']:
                    # FLEX positions
                    actual_lineup['FLEX'] = player.name
                else:
                    # Fallback for other positions
                    actual_lineup[slot] = player.name
        
        # Build optimal lineup mapping (simplified)
        optimal_lineup = self._build_optimal_lineup_mapping(all_available_players)
        
        # Create optimal lineup player data for template
        optimal_player_data = {}
        optimal_projected_total = 0.0
        optimal_actual_total = 0.0
        
        for player in all_available_players:
            player_name = getattr(player, 'name', 'Unknown')
            projected = getattr(player, 'projected_points', 0)
            actual = getattr(player, 'points', 0)
            
            optimal_player_data[player_name] = {
                'projected_points': projected,
                'actual_points': actual
            }
        
        # Calculate totals only for players actually in the optimal lineup
        for position, player_name in optimal_lineup.items():
            if player_name in optimal_player_data:
                optimal_projected_total += optimal_player_data[player_name]['projected_points']
                optimal_actual_total += optimal_player_data[player_name]['actual_points']
        
        return {
            'opponent_team_name': opponent_name,
            'opponent_actual_points': opponent_points,
            'actual_game_result': actual_result,
            'actual_margin': actual_margin,
            'optimal_game_result': optimal_result,
            'optimal_margin': optimal_margin,
            'game_impact': game_impact,
            'optimal_lineup': optimal_lineup,
            'actual_lineup': actual_lineup,
            'optimal_player_data': optimal_player_data,
            'optimal_projected_total': optimal_projected_total,
            'optimal_actual_total': optimal_actual_total
        }
    
    def _determine_game_impact(self, actual_result: str, optimal_result: str, actual_margin: float, optimal_margin: float) -> str:
        """Determine the impact of optimal lineup decisions on game outcome."""
        if actual_result == "Lost" and optimal_result == "Won":
            return f"Changed Loss to Win (+{optimal_margin:.1f} vs {actual_margin:.1f})"
        elif actual_result == "Won" and optimal_result == "Lost":
            return f"Changed Win to Loss ({optimal_margin:.1f} vs +{actual_margin:.1f})"
        elif actual_result == "Won" and optimal_result == "Won":
            margin_improvement = optimal_margin - actual_margin
            return f"Improved Win Margin (+{margin_improvement:.1f} points)"
        elif actual_result == "Lost" and optimal_result == "Lost":
            margin_improvement = optimal_margin - actual_margin
            if margin_improvement > 0:
                return f"Reduced Loss Margin (+{margin_improvement:.1f} points closer)"
            else:
                return "No significant improvement"
        else:
            return "No Change"
    
    def _build_optimal_lineup_mapping(self, all_players: List[Any]) -> Dict[str, str]:
        """Build optimal lineup player mapping."""
        optimal_lineup = {}
        
        # Group players by position
        players_by_pos = {}
        for player in all_players:
            pos = getattr(player, 'position', 'UNKNOWN')
            if pos not in players_by_pos:
                players_by_pos[pos] = []
            players_by_pos[pos].append(player)
        
        # Sort each position by points (descending)
        for pos in players_by_pos:
            players_by_pos[pos].sort(key=lambda x: getattr(x, 'points', 0), reverse=True)
        
        # Build optimal lineup (simplified logic)
        positions_needed = {
            'QB': 1,
            'RB': 2, 
            'WR': 2,
            'TE': 1,
            'K': 1,
            'D/ST': 1
        }
        
        for pos, count in positions_needed.items():
            if pos in players_by_pos:
                for i in range(min(count, len(players_by_pos[pos]))):
                    player = players_by_pos[pos][i]
                    if count > 1:
                        # Use standard ordering like RB1, RB2, WR1, WR2
                        key = f"{pos}{i+1}" if pos in ['RB', 'WR'] else f"{pos}"
                    else:
                        key = pos
                    optimal_lineup[key] = player.name
        
        # Add FLEX from remaining RB/WR/TE
        flex_candidates = []
        for pos in ['RB', 'WR', 'TE']:
            if pos in players_by_pos:
                start_idx = positions_needed.get(pos, 0)
                flex_candidates.extend(players_by_pos[pos][start_idx:])
        
        if flex_candidates:
            flex_candidates.sort(key=lambda x: getattr(x, 'points', 0), reverse=True)
            optimal_lineup['FLEX'] = flex_candidates[0].name
        
        return optimal_lineup
    
    def _standardize_position_display(self, slot: str, position: str) -> str:
        """Standardize position display for consistent lineup formatting."""
        # Handle different slot/position combinations
        if slot == 'QB' or position == 'QB':
            return 'QB'
        elif slot == 'RB' or position == 'RB':
            return 'RB' 
        elif slot == 'WR' or position == 'WR':
            return 'WR'
        elif slot == 'TE' or position == 'TE':
            return 'TE'
        elif slot == 'K' or position == 'K':
            return 'K'
        elif slot == 'D/ST' or position == 'D/ST':
            return 'D/ST'
        elif 'FLEX' in slot or slot in ['RB/WR/TE', 'RB/WR', 'WR/RB/TE']:
            return 'FLEX'
        else:
            # Fallback to position if slot is unclear
            return position

    def _create_empty_performance(self, team_name: str, week: int) -> TeamStartSitPerformance:
        """Create empty performance object when data is unavailable."""
        return TeamStartSitPerformance(
            team_name=team_name,
            week=week,
            total_starting_points=0.0,
            total_bench_points=0.0,
            points_left_on_bench=0.0,
            optimal_lineup_points=0.0,
            actual_vs_optimal_diff=0.0,
            lineup_efficiency=0.0,
            best_start_decision=None,
            worst_start_decision=None,
            missed_opportunities=[],
            start_sit_decisions=[],
            position_breakdown={},
            # Victory analysis fields
            opponent_team_name="Unknown",
            opponent_actual_points=0.0,
            actual_game_result="Unknown",
            actual_margin=0.0,
            optimal_game_result="Unknown", 
            optimal_margin=0.0,
            game_impact="No Data",
            optimal_lineup={},
            actual_lineup={},
            optimal_player_data={},
            optimal_projected_total=0.0,
            optimal_actual_total=0.0
        )
    
    def _create_empty_league_intelligence(self, week: int) -> LeagueStartSitIntelligence:
        """Create empty league intelligence when data is unavailable."""
        return LeagueStartSitIntelligence(
            week=week,
            team_performances=[],
            league_avg_points_on_bench=0.0,
            league_avg_lineup_efficiency=0.0,
            best_decision_maker="Unknown",
            worst_decision_maker="Unknown",
            most_points_left_on_bench="Unknown",
            best_start_decision_league_wide=None,
            worst_start_decision_league_wide=None,
            position_trends={}
        )