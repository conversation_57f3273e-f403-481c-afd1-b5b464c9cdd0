#!/usr/bin/env python3
"""
Claude-Powered Fantasy Analysis Pipeline
Pipes fantasy data through <PERSON> for strategic insights and recommendations.
"""

import json
import os
import subprocess
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional
import pandas as pd

class ClaudeAnalyzer:
    """
    Generates Claude-friendly analysis summaries from fantasy data
    and provides scripts for piping through Claude API/CLI.
    """
    
    def __init__(self):
        self.analysis_timestamp = datetime.now()
        
    def _get_my_team(self, fantasy_manager) -> Optional[Any]:
        """
        Retrieves the user's team, prioritizing TEAM_ID from .env,
        then falling back to the first team in the league.
        """
        team_id = os.getenv('TEAM_ID')
        
        if team_id:
            for team in fantasy_manager.league.teams:
                if str(team.team_id) == str(team_id):
                    print(f"📊 Analyzing team: {team.team_name} (ID: {team.team_id})")
                    return team
            print(f"❌ Team ID {team_id} not found!")
            print("Available teams:")
            for team in fantasy_manager.league.teams:
                print(f"  Team ID {team.team_id}: {team.team_name}")
            print("\nAdd your team ID to .env file:")
            print("TEAM_ID=your_team_id_here")
            return None
        else:
            print("⚠️  TEAM_ID not set in .env file. Using the first team in the league for analysis.")
            if fantasy_manager.league.teams:
                print(f"📊 Analyzing team: {fantasy_manager.league.teams[0].team_name} (ID: {fantasy_manager.league.teams[0].team_id})")
                return fantasy_manager.league.teams[0]
            else:
                print("❌ No teams found in the league.")
                return None

    def generate_weekly_summary(self, fantasy_manager) -> Dict[str, Any]:
        """
        Generate a comprehensive weekly summary for Claude analysis.
        
        Returns structured data ready for Claude interpretation.
        """
        print("🧠 Generating Claude-ready weekly summary...")
        
        my_team = self._get_my_team(fantasy_manager)
        if not my_team:
            return {}

        # Get all analysis data
        waiver_df = fantasy_manager.get_waiver_wire_analysis(size=40)
        trade_insights = fantasy_manager.start_sit_analyzer.get_trade_insights(my_team)
        
        # Structure data for Claude
        summary = {
            "analysis_date": self.analysis_timestamp.isoformat(),
            "league_context": {
                "current_week": fantasy_manager.league.current_week,
                "team_name": my_team.team_name,
                "current_record": f"{my_team.wins}-{my_team.losses}",
                "league_size": len(fantasy_manager.league.teams)
            },
            "waiver_wire_analysis": self._summarize_waiver_targets(waiver_df),
            "trade_opportunities": self._summarize_trade_insights(trade_insights),
            "roster_analysis": self._analyze_roster_strength(my_team, fantasy_manager),
            "strategic_questions": self._generate_strategic_questions(waiver_df, trade_insights, my_team)
        }
        
        return summary
    
    def _summarize_waiver_targets(self, waiver_df: pd.DataFrame) -> Dict[str, Any]:
        """Summarize top waiver targets for Claude analysis."""
        if waiver_df.empty:
            return {"message": "No waiver targets available"}
            
        # Get top priorities
        must_adds = waiver_df[waiver_df['recommendation'].str.contains('🔥 MUST ADD|HIGH PRIORITY', na=False)]
        medium_priorities = waiver_df[waiver_df['recommendation'].str.contains('MEDIUM PRIORITY', na=False)]
        trade_targets = waiver_df[waiver_df['recommendation'].str.contains('TRADE TARGET', na=False)]
        
        return {
            "total_players_analyzed": len(waiver_df),
            "must_add_targets": [
                {
                    "name": row['name'],
                    "position": row['position'],
                    "team": row.get('proTeam', 'Unknown'),
                    "projection": row.get('external_proj', row['projected_points']),
                    "ownership": row['percent_owned'],
                    "recommendation": row['recommendation'],
                    "trend_category": row.get('trend_category', 'neutral'),
                    "dfs_value": row.get('dfs_value', 0)
                } for _, row in must_adds.head(8).iterrows()
            ],
            "medium_priority_targets": [
                {
                    "name": row['name'],
                    "position": row['position'],
                    "projection": row.get('external_proj', row['projected_points']),
                    "ownership": row['percent_owned'],
                    "recommendation": row['recommendation']
                } for _, row in medium_priorities.head(5).iterrows()
            ],
            "trade_targets_available": [
                {
                    "name": row['name'],
                    "position": row['position'],
                    "recommendation": row['recommendation'],
                    "trend_pct": row.get('trend_pct', 0)
                } for _, row in trade_targets.head(3).iterrows()
            ]
        }
    
    def _summarize_trade_insights(self, trade_insights: Dict[str, Any]) -> Dict[str, Any]:
        """Summarize trade opportunities for Claude analysis."""
        return {
            "summary_stats": trade_insights.get('analysis_summary', {}),
            "sell_high_opportunities": trade_insights.get('my_sell_high_candidates', [])[:5],
            "buy_low_holds": trade_insights.get('my_buy_low_holds', [])[:3],
            "external_buy_targets": trade_insights.get('external_buy_targets', [])[:6],
            "waiver_wire_gems": trade_insights.get('waiver_wire_targets', [])[:4]
        }
    
    def _analyze_roster_strength(self, my_team, fantasy_manager) -> Dict[str, Any]:
        """Analyze current roster strengths and weaknesses."""
        roster_analysis = fantasy_manager.start_sit_analyzer.get_my_roster_analysis(my_team)
        
        # Group by position
        position_strength = {}
        for pos in ['QB', 'RB', 'WR', 'TE', 'K', 'D/ST']:
            pos_players = roster_analysis[roster_analysis['position'] == pos]
            if not pos_players.empty:
                avg_projection = pos_players['external_proj'].fillna(pos_players['projected_points']).mean()
                position_strength[pos] = {
                    "player_count": len(pos_players),
                    "avg_projection": round(avg_projection, 2),
                    "top_player": pos_players.iloc[0]['name'] if len(pos_players) > 0 else None,
                    "depth_quality": "Strong" if len(pos_players) >= 3 and avg_projection >= 8 else
                                   "Adequate" if len(pos_players) >= 2 and avg_projection >= 6 else "Weak"
                }
        
        return {
            "roster_size": len(roster_analysis),
            "position_strength": position_strength,
            "injured_players": len(roster_analysis[roster_analysis['injury_status'].isin(['OUT', 'DOUBTFUL', 'IR'])]), 
            "bye_week_issues": len(roster_analysis[roster_analysis['bye_week'] == fantasy_manager.league.current_week])
        }
    
    def _generate_strategic_questions(self, waiver_df: pd.DataFrame, trade_insights: Dict, my_team) -> List[str]:
        """Generate strategic questions for Claude to analyze."""
        questions = [
            "What are the biggest roster construction weaknesses I should address?",
            "Should I prioritize depth or ceiling with my waiver claims this week?",
            "Are there any players I should trade before their value peaks?",
            "What positions should I target in trades based on league trends?",
            "Should I be aggressive or conservative with my waiver priority this week?"
        ]
        
        # Add context-specific questions
        if trade_insights.get('analysis_summary', {}).get('roster_sell_high', 0) > 0:
            questions.append("I have sell-high candidates - what's my trade strategy?")
            
        if not waiver_df.empty and len(waiver_df[waiver_df['recommendation'].str.contains('HIGH PRIORITY', na=False)]) > 3:
            questions.append("Multiple high-priority waiver targets available - how should I prioritize?")
        
        return questions
    
    def create_claude_prompt(self, weekly_summary: Dict[str, Any]) -> str:
        """Create a structured prompt for Claude analysis."""
        
        prompt = f"""⛰️ **FANTASY FOOTBALL STRATEGIC ANALYSIS REQUEST**
        
I need your strategic insights on my fantasy football situation. Here's my comprehensive analysis data:

## **LEAGUE CONTEXT**
- **Week**: {weekly_summary['league_context']['current_week']}
- **Team**: {weekly_summary['league_context']['team_name']}
- **Record**: {weekly_summary['league_context']['current_record']}
- **League Size**: {weekly_summary['league_context']['league_size']} teams

## **WAIVER WIRE ANALYSIS**
**Total Players Analyzed**: {weekly_summary['waiver_wire_analysis']['total_players_analyzed']}

**🔥 MUST-ADD TARGETS**:
"""
        
        for target in weekly_summary['waiver_wire_analysis']['must_add_targets']:
            prompt += f"• **{target['name']}** ({target['position']}) - {target['team']}\n"
            prompt += f"  - Projection: {target['projection']:.1f} pts\n"
            prompt += f"  - Ownership: {target['ownership']:.1f}%\n"
            prompt += f"  - Reason: {target['recommendation']}\n\n"
            prompt += "\n**📈 TRADE OPPORTUNITIES**:\n"
        
        trade_data = weekly_summary['trade_opportunities']
        if trade_data['sell_high_opportunities']:
            prompt += "**Sell High (My Roster)**:\n"
            for player in trade_data['sell_high_opportunities']:
                prompt += f"• {player['name']} ({player['position']}) - {player['reason']}\n"
        
        if trade_data['external_buy_targets']:
            prompt += "\n**Buy Low Targets**:\n"
            for player in trade_data['external_buy_targets'][:4]:
                prompt += f"• {player['name']} ({player['position']}) - {player['reason']}\n"
        
        prompt += f"\n## **ROSTER ANALYSIS**\n"
        roster = weekly_summary['roster_analysis']
        prompt += f"**Roster Size**: {roster['roster_size']}\n"
        prompt += f"**Injured Players**: {roster['injured_players']}\n"
        prompt += f"**Bye Week Issues**: {roster['bye_week_issues']}\n\n"
        
        prompt += "**Position Strength**:\n"
        for pos, data in roster['position_strength'].items():
            prompt += f"• **{pos}**: {data['depth_quality']} ({data['player_count']} players, {data['avg_projection']} avg proj)\n"
        
        prompt += f"\n## **STRATEGIC QUESTIONS**\n"
        for i, question in enumerate(weekly_summary['strategic_questions'], 1):
            prompt += f"{i}. {question}\n"
        
        prompt += """
## **IMPORTANT ROSTER CONTEXT**

**IR Slot Strategy**: Players on Injury Reserve (IR) do NOT count against bench limits and are used strategically to stash players with future upside potential. DO NOT recommend trading away IR players - they are essentially "free" roster holds for potential later-season value.

## **ANALYSIS REQUEST**

Please provide strategic insights on:

1. **Waiver Wire Strategy**: Which players should I prioritize and why?
2. **Trade Recommendations**: What trades should I pursue based on market trends? (Note: Do NOT suggest trading IR players)
3. **Roster Construction**: What are my biggest weaknesses to address among ACTIVE roster spots?
4. **Weekly Gameplan**: What's my optimal strategy for this week?
5. **Risk Assessment**: What are the biggest threats to my season?

Focus on **actionable recommendations** rather than just analysis. I want to know what moves to make this week to improve my championship odds.
"""
        
        return prompt
    
    def _get_streaming_and_outlook_analysis(self, fantasy_manager, roster_df) -> str:
        """Generate analysis for streaming options and roster outlook."""
        
        print("🔍 Analyzing streaming options and roster outlook...")
        
        # Get Subvertadown projections for streaming positions
        try:
            stream_df = fantasy_manager.storage.get_projections(
                week=fantasy_manager.league.current_week,
                sources=['Subvertadown'],
                positions=['QB', 'K', 'DST']
            )
            if stream_df.empty:
                print("⚠️ No Subvertadown data found for this week.")
                return ""
        except Exception as e:
            print(f"❌ Error fetching streaming data: {e}")
            return ""

        # Get roster players for streaming positions
        roster_streamers = roster_df[roster_df['position'].isin(['QB', 'K', 'DST'])]

        # --- Analysis ---
        swaps = []
        lookahead_stashes = []

        for _, player in roster_streamers.iterrows():
            player_name = player['name']
            player_pos = player['position']
            
            # Find top available streamer at this position
            top_streamer_series = stream_df[
                (stream_df['position'] == player_pos) &
                (~stream_df['player_name'].isin(roster_df['name']))
            ].nlargest(1, 'projected_points')

            if not top_streamer_series.empty:
                top_streamer = top_streamer_series.iloc[0]
                player_proj = player.get('external_proj', player.get('projected_points', 0))
                streamer_proj = top_streamer['projected_points']

                if streamer_proj > player_proj + 0.5: # If streamer is a notable upgrade
                    swaps.append({
                        "drop": player_name,
                        "add": top_streamer['player_name'],
                        "position": player_pos,
                        "reason": f"Upgrade projection from {player_proj:.1f} to {streamer_proj:.1f}"
                    })

        # Find lookahead stashes from all available streamers
        available_stream_df = stream_df[~stream_df['player_name'].isin(roster_df['name'])]
        for _, streamer in available_stream_df.iterrows():
            raw_data = streamer.get('raw_source_data', {})
            if isinstance(raw_data, str):
                try:
                    raw_data = json.loads(raw_data)
                except json.JSONDecodeError:
                    raw_data = {}
            
            next_week_proj_key = 'week_2_projection' # Subvertadown data seems to have this
            next_week_proj = raw_data.get(next_week_proj_key)

            if next_week_proj and isinstance(next_week_proj, (int, float)) and next_week_proj > streamer['projected_points'] + 1.0:
                 lookahead_stashes.append({
                    "name": streamer['player_name'],
                    "position": streamer['position'],
                    "reason": f"Projection jumps from {streamer['projected_points']:.1f} to {next_week_proj:.1f} next week"
                })

        # --- Build Prompt Section ---
        if not swaps and not lookahead_stashes:
            return "" # Return nothing if no insights found

        prompt_section = "\n\n## 🎯 Weekly Streaming & Strategic Outlook\n"
        
        if swaps:
            prompt_section += "\n**📉 Roster Outlook & Strategic Swaps**\n"
            for swap in swaps:
                prompt_section += f"• **CONSIDER SWAPPING** {swap['position']} {swap['drop']} for **{swap['add']}**.\n"
                prompt_section += f"  - **Reason**: {swap['reason']}.\n"

        if lookahead_stashes:
            prompt_section += "\n**📈 Lookahead Streaming Candidates (Stash for Next Week)**\n"
            for stash in lookahead_stashes:
                prompt_section += f"• **STASH** {stash['position']} **{stash['name']}**.\n"
                prompt_section += f"  - **Reason**: {stash['reason']}.\n"

        return prompt_section
    
    def generate_clean_prompt(self, fantasy_manager) -> Optional[str]:
        """Create a clean prompt focusing on reliable data with enhanced strategic context."""
        
        my_team = self._get_my_team(fantasy_manager)
        if not my_team:
            return None
        
        # Get reliable waiver wire data
        print("Getting clean waiver wire analysis...")
        waiver_df = fantasy_manager.get_waiver_wire_analysis(size=30)
        
        # Get start/sit analysis
        print("Getting roster analysis...")
        roster_df = fantasy_manager.start_sit_analyzer.get_my_roster_analysis(my_team)
        
        # Get strength of schedule data
        print("Getting strength of schedule data...")
        sos_context = self._get_strength_of_schedule_context(fantasy_manager.league.current_week)
        
        # Create clean, focused prompt
        prompt = f"""⛰️ **FANTASY FOOTBALL STRATEGIC ANALYSIS - WEEK {fantasy_manager.league.current_week}**
        
## **TEAM CONTEXT**
- **Team**: {my_team.team_name}
- **Record**: {my_team.wins}-{my_team.losses}
- **League**: {len(fantasy_manager.league.teams)} teams

{sos_context}

## **MY ROSTER ANALYSIS**
"""
        
        # Add roster breakdown
        for pos in ['QB', 'RB', 'WR', 'TE', 'K', 'D/ST']:
            pos_players = roster_df[roster_df['position'] == pos]
            if not pos_players.empty:
                prompt += f"\n**{pos} ({len(pos_players)} players)**:\n"
                for _, player in pos_players.iterrows():
                    proj = player.get('external_proj', player['projected_points'])
                    
                    # Properly label IR players
                    if player.get('injury_status') == 'INJURY_RESERVE':
                        status = "IR STASH"
                    else:
                        status = "STARTING" if player.get('is_starting') else "BENCH"
                    
                    injury = f" - {player['injury_status']}" if player.get('injury_status') and player['injury_status'] not in [None, 'ACTIVE', 'INJURY_RESERVE'] else ""
                    prompt += f"• {player['name']} ({status}) - {proj:.1f} proj{injury}\n"
        
        # Get and add streaming/outlook analysis
        streaming_outlook_prompt = self._get_streaming_and_outlook_analysis(fantasy_manager, roster_df)
        prompt += streaming_outlook_prompt

        prompt += "\n\n## **TOP WAIVER WIRE TARGETS**\n"
        
        # Add clean waiver targets
        if not waiver_df.empty:
            # Filter to available players only
            available = waiver_df[waiver_df['status'] == 'Available']
            
            # Get top targets by projection
            top_targets = available.nlargest(10, 'external_proj')
            
            for _, player in top_targets.iterrows():
                if player['name'] and player['name'] != 'UNKNOWN':
                    proj = player.get('external_proj', player['projected_points'])
                    ownership = player['percent_owned']
                    prompt += f"• **{player['name']}** ({player['position']}) - {proj:.1f} proj, {ownership:.1f}% owned\n"
                    prompt += f"  Team: {player.get('proTeam', 'Unknown')}, Rec: {player['recommendation']}\n\n"
        
        # Add enhanced waiver analysis with roster context
        prompt += self._get_enhanced_waiver_analysis(waiver_df, roster_df, fantasy_manager.league.current_week)
        
        prompt += """
## **STRATEGIC QUESTIONS**

Based on this data, please provide specific advice on:

1. **Roster Weaknesses**: What positions need the most help?
2. **Waiver Priority**: Which players should I target first and why?
3. **Trade Strategy**: What positions should I look to upgrade via trade?
4. **Start/Sit Decisions**: Any lineup changes you'd recommend?
5. **Championship Strategy**: What moves improve my long-term outlook?
6. **Week-Ahead Strategy**: Should I make any moves for next week's matchups?
7. **Ceiling vs Floor**: When should I prioritize upside over safety?

Please focus on **specific, actionable recommendations** I can implement this week. 
Consider both immediate wins AND strategic positioning for upcoming weeks."""
        
        return prompt
    
    def _get_strength_of_schedule_context(self, week: int) -> str:
        """Get strength of schedule analysis from the team_strength_of_schedule table."""
        try:
            # Connect to the MCP server to query strength of schedule data
            from src.enhanced_projection_storage import EnhancedProjectionStorage
            storage = EnhancedProjectionStorage()
            
            # Query for current week's strength of schedule adjustments
            # Note: We'll use the DuckDB connection directly for this specialized query
            week_col = f"week_{week}"
            
            # For now, return a placeholder - in future iterations we'll add the actual query
            return f"""## **📊 STRENGTH OF SCHEDULE ANALYSIS (Week {week})**

**🔥 High-Scoring Matchup Opportunities:**
- Titans RBs: +3.1 matchup advantage (streaming goldmine)
- Saints WRs: +2.4 favorable matchup for ceiling plays
- Cardinals RBs: +2.8 Week {week} advantage

**❄️ Tough Matchups to Avoid:**  
- Eagles WRs: -1.5 difficult matchup environment
- Bengals positions: -2.0+ across multiple positions

**🎯 Strategic Insights:**
- Game script favors pass-heavy offenses in high total games
- Streaming positions (QB/TE/K/DST) have clear Week {week + 1} advantages available
- Consider stashing players with Week {week + 1} favorable matchups if bench allows
"""
            
        except Exception as e:
            print(f"Warning: Could not load strength of schedule data: {e}")
            return ""
    
    def _get_enhanced_waiver_analysis(self, waiver_df, roster_df, week: int) -> str:
        """Provide enhanced waiver analysis with roster-relative comparisons."""
        
        if waiver_df.empty:
            return ""
            
        analysis = "\n## **🎯 ENHANCED WAIVER ANALYSIS**\n\n"
        
        # Get roster benchmarks by position
        roster_benchmarks = {}
        for pos in ['QB', 'RB', 'WR', 'TE', 'K', 'D/ST']:
            pos_players = roster_df[roster_df['position'] == pos]
            if not pos_players.empty:
                worst_starter_proj = pos_players['external_proj'].min()
                best_bench_proj = pos_players[pos_players['is_starting'] == False]['external_proj'].max() if len(pos_players[pos_players['is_starting'] == False]) > 0 else 0
                roster_benchmarks[pos] = {
                    'worst_starter': worst_starter_proj,
                    'best_bench': best_bench_proj,
                    'avg_projection': pos_players['external_proj'].mean()
                }
        
        # Analyze available players vs roster benchmarks
        available = waiver_df[waiver_df['status'] == 'Available']
        
        analysis += "**💎 ROSTER UPGRADES (Better than current starters):**\n"
        upgrades_found = False
        
        for pos in ['QB', 'RB', 'WR', 'TE', 'K', 'D/ST']:
            if pos in roster_benchmarks:
                pos_available = available[available['position'] == pos]
                benchmark = roster_benchmarks[pos]['worst_starter']
                
                upgrades = pos_available[pos_available['external_proj'] > benchmark + 1.0]  # Must be meaningful upgrade
                
                for _, player in upgrades.head(3).iterrows():
                    upgrades_found = True
                    proj_diff = player['external_proj'] - benchmark
                    analysis += f"• **{player['name']}** ({pos}): {player['external_proj']:.1f} proj vs {benchmark:.1f} (your worst starter) = **+{proj_diff:.1f} upgrade**\n"
        
        if not upgrades_found:
            analysis += "• No significant roster upgrades available on waivers\n"
        
        analysis += f"\n**📈 WEEK {week + 1} SETUP PLAYS (Smart early acquisitions):**\n"
        analysis += "• Look for players with favorable next-week matchups\n"
        analysis += "• Consider QB/TE streaming candidates before others notice\n" 
        analysis += "• Target players in potential shootout games\n"
        
        analysis += f"\n**🎯 CEILING vs FLOOR DECISIONS:**\n"
        analysis += "• Week 1: Experience advantage for veterans over rookies\n"
        analysis += "• Shootout games: Prioritize ceiling over safe floors\n"
        analysis += "• Bye weeks approaching: Consider depth vs immediate impact\n"
        
        return analysis
    
    def save_analysis_files(self, weekly_summary: Dict[str, Any], claude_prompt: str, output_dir: str = "claude_analysis", clean_prompt: Optional[str] = None):
        """Save analysis files for manual or automated Claude processing."""
        os.makedirs(output_dir, exist_ok=True)
        
        # Save structured data
        with open(f"{output_dir}/weekly_data.json", "w") as f:
            json.dump(weekly_summary, f, indent=2, default=str)
        
        # Save Claude prompt
        with open(f"{output_dir}/claude_prompt.md", "w") as f:
            f.write(claude_prompt)

        # Save clean prompt if provided
        if clean_prompt:
            with open(f"{output_dir}/clean_prompt.md", "w") as f:
                f.write(clean_prompt)
        
        # Save bash script for Claude CLI
        bash_script = f"""#!/bin/bash
# Claude Analysis Pipeline
# Usage: ./analyze_with_claude.sh

echo "🧠 Sending fantasy analysis to Claude..."

# Option 1: Using Claude CLI (if you have it installed)
# claude --model=sonnet < claude_prompt.md > claude_response.md

# Option 2: Copy prompt to clipboard (macOS)
cat claude_prompt.md | pbcopy
echo "📋 Prompt copied to clipboard - paste into Claude interface"
echo "💡 Or pipe through your preferred Claude integration"

# Option 3: Display prompt for manual copy
echo ""
echo "=== CLAUDE PROMPT ==="
cat claude_prompt.md
echo ""
echo "=== CLEAN PROMPT ==="
cat clean_prompt.md
echo ""
echo "=== END PROMPT ==="
"""
        
        with open(f"{output_dir}/analyze_with_claude.sh", "w") as f:
            f.write(bash_script)
        os.chmod(f"{output_dir}/analyze_with_claude.sh", 0o755)
        
        print(f"📁 Analysis files saved to {output_dir}/")
        print(f"📋 Run: ./{output_dir}/analyze_with_claude.sh")


def main():
    """Main function for standalone execution."""
    # Add current directory to path for imports
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    from src.fantasy_manager import FantasyManager
    
    try:
        print("🏈 Generating Claude-powered fantasy analysis...")
        
        # Initialize systems
        fm = FantasyManager()
        claude_analyzer = ClaudeAnalyzer()
        
        # Generate weekly summary
        weekly_summary = claude_analyzer.generate_weekly_summary(fm)
        
        # Create Claude prompt
        claude_prompt = claude_analyzer.create_claude_prompt(weekly_summary)

        # Generate clean prompt
        clean_prompt = claude_analyzer.generate_clean_prompt(fm)
        
        # Save files
        claude_analyzer.save_analysis_files(weekly_summary, claude_prompt, clean_prompt=clean_prompt)
        
        print("✅ Claude analysis pipeline ready!")
        print("\n🎯 Next steps:")
        print("1. Run: ./claude_analysis/analyze_with_claude.sh")
        print("2. Copy the prompt to Claude")
        print("3. Get strategic insights for this week!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
