from typing import Any, Dict
import pandas as pd
from .winwithodds_trends import WinWithOddsTrends

class TradeAnalyzer:
    """Analyzes trade opportunities for a fantasy football roster."""

    def __init__(self):
        self.wwo_trends = WinWithOddsTrends()

    def get_trade_insights(self, roster_df: pd.DataFrame) -> Dict[str, any]:
        """
        Generate trade insights for roster optimization.
        """
        buy_low_targets, sell_high_targets = self.wwo_trends.get_trade_targets()
        undervalued_players = self.wwo_trends.get_undervalued_players()
        
        roster_names = [name.lower() for name in roster_df['name']]
        
        my_sell_high = []
        if not sell_high_targets.empty:
            for _, player in sell_high_targets.iterrows():
                if player['name'].lower() in roster_names:
                    my_sell_high.append({
                        'name': player['name'],
                        'position': player['position'],
                        'trend_pct': player['trend_pct'],
                        'reason': f"Overperforming by {player['trend_pct']:.1f}% - sell before regression"
                    })
        
        my_buy_low = []
        if not buy_low_targets.empty:
            for _, player in buy_low_targets.iterrows():
                if player['name'].lower() in roster_names:
                    my_buy_low.append({
                        'name': player['name'],
                        'position': player['position'], 
                        'trend_pct': player['trend_pct'],
                        'reason': f"Underperforming by {abs(player['trend_pct']):.1f}% - hold/buy more"
                    })
        
        external_buy_targets = []
        if not buy_low_targets.empty:
            for _, player in buy_low_targets.iterrows():
                if player['name'].lower() not in roster_names:
                    external_buy_targets.append({
                        'name': player['name'],
                        'position': player['position'],
                        'trend_pct': player['trend_pct'],
                        'reason': f"Underperforming by {abs(player['trend_pct']):.1f}% - great buy-low target"
                    })
        
        waiver_targets = []
        if not undervalued_players.empty:
            top_values = undervalued_players.head(10)
            for _, player in top_values.iterrows():
                if player['name'].lower() not in roster_names:
                    waiver_targets.append({
                        'name': player['name'],
                        'position': player['position'],
                        'salary': player.get('salary', 0),
                        'value': player['value'],
                        'reason': f"High efficiency ({player['value']:.2f} pts/$1k) - undervalued"
                    })
        
        return {
            'my_sell_high_candidates': my_sell_high,
            'my_buy_low_holds': my_buy_low,
            'external_buy_targets': external_buy_targets[:8],
            'waiver_wire_targets': waiver_targets[:6],
            'analysis_summary': {
                'roster_sell_high': len(my_sell_high),
                'roster_buy_low': len(my_buy_low),
                'external_targets': len(external_buy_targets),
                'waiver_targets': len(waiver_targets)
            }
        }
