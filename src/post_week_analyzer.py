"""
Post-Week Analysis Module

Analyzes the accuracy of projections vs actual performance after each week completes.
Designed to run Tuesday morning after Monday Night Football.

Key Features:
- Projection accuracy analysis (ESPN, FanDuel, WinWithOdds)
- Player performance insights (overperform/underperform)
- Position-based trends and patterns
- League-wide statistical analysis
- Waiver wire performance tracking
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from espn_api.football import League
import logging
from .start_sit_accuracy_analyzer import StartSitAccuracyAnalyzer, LeagueStartSitIntelligence

@dataclass
class ProjectionAccuracy:
    """Data class for projection accuracy metrics"""
    source: str  # ESPN, FanDuel, WinWithOdds
    player_name: str
    position: str
    projected_points: float
    actual_points: float
    difference: float
    absolute_error: float
    percentage_error: float
    
@dataclass  
class PositionAnalysis:
    """Position-level analysis summary"""
    position: str
    total_players: int
    avg_projected: float
    avg_actual: float
    avg_error: float
    avg_abs_error: float
    best_projection_source: str
    worst_projection_source: str

@dataclass
class TeamPerformance:
    """Team-specific performance analysis"""
    team_name: str
    total_projected: float
    total_actual: float
    difference: float
    players_analyzed: int
    lineup_players: int
    bench_players: int
    best_performer: ProjectionAccuracy
    worst_performer: ProjectionAccuracy
    lineup_accuracy: float  # MAE for starting lineup only
    bench_accuracy: float   # MAE for bench players

@dataclass
class WeekSummary:
    """Week-level summary statistics"""
    week: int
    total_players_analyzed: int
    most_accurate_source: str
    least_accurate_source: str
    biggest_bust: ProjectionAccuracy
    biggest_boom: ProjectionAccuracy
    position_summaries: List[PositionAnalysis]
    overall_mae: float  # Mean Absolute Error
    overall_rmse: float  # Root Mean Square Error
    my_team_performance: Optional['TeamPerformance'] = None
    start_sit_intelligence: Optional[LeagueStartSitIntelligence] = None

class PostWeekAnalyzer:
    """
    Analyzes projection accuracy after each week completes.
    
    This class provides comprehensive post-week analysis comparing projections
    to actual performance across multiple data sources.
    """
    
    def __init__(self, league: League):
        """
        Initialize the PostWeekAnalyzer
        
        Args:
            league: ESPN League instance
        """
        self.league = league
        self.logger = logging.getLogger(__name__)
        self.start_sit_analyzer = StartSitAccuracyAnalyzer(league)
        
    def is_week_complete(self, week: int) -> bool:
        """
        Check if a given week is complete (all games finished)
        
        Args:
            week: Week number to check
            
        Returns:
            True if week is complete, False otherwise
        """
        try:
            # Get box scores for the week
            box_scores = self.league.box_scores(week=week)
            
            # Check if all games are completed
            for matchup in box_scores:
                for player in matchup.home_lineup + matchup.away_lineup:
                    # If any player has game_played < 100, week isn't complete
                    if hasattr(player, 'game_played') and player.game_played < 100:
                        return False
                        
            return True
            
        except Exception as e:
            self.logger.warning(f"Could not check week completion for week {week}: {e}")
            return False
    
    def get_completed_week_data(self, week: int) -> List[Any]:
        """
        Get all player data for a completed week
        
        Args:
            week: Week number (must be completed)
            
        Returns:
            List of player objects with actual and projected stats
        """
        if not self.is_week_complete(week):
            raise ValueError(f"Week {week} is not yet complete")
            
        all_players = []
        box_scores = self.league.box_scores(week=week)
        
        for matchup in box_scores:
            # Collect all players from both teams
            all_players.extend(matchup.home_lineup)
            all_players.extend(matchup.away_lineup)
            
        return all_players
    
    def analyze_projection_accuracy(
        self, 
        week: int, 
        external_projections: Optional[Dict[str, pd.DataFrame]] = None
    ) -> List[ProjectionAccuracy]:
        """
        Analyze accuracy of projections vs actual performance
        
        Args:
            week: Week number to analyze
            external_projections: Dict of external projection sources
                                 {'FanDuel': df, 'WinWithOdds': df}
        
        Returns:
            List of ProjectionAccuracy objects for analysis
        """
        players = self.get_completed_week_data(week)
        accuracy_results = []
        
        for player in players:
            # Skip players who didn't play or have no projections
            if not hasattr(player, 'points') or not hasattr(player, 'projected_points'):
                continue
                
            if player.projected_points == 0 and player.points == 0:
                continue  # Skip bench/bye players
            
            # ESPN projection accuracy
            espn_accuracy = self._calculate_accuracy(
                source="ESPN",
                player_name=player.name,
                position=player.position,
                projected=player.projected_points,
                actual=player.points
            )
            accuracy_results.append(espn_accuracy)
            
            # External projections (FanDuel, WinWithOdds, etc.)
            if external_projections:
                for source_name, df in external_projections.items():
                    external_proj = self._find_external_projection(player, df)
                    if external_proj is not None:
                        ext_accuracy = self._calculate_accuracy(
                            source=source_name,
                            player_name=player.name,
                            position=player.position,
                            projected=external_proj,
                            actual=player.points
                        )
                        accuracy_results.append(ext_accuracy)
        
        return accuracy_results
    
    def _calculate_accuracy(
        self, 
        source: str, 
        player_name: str, 
        position: str,
        projected: float, 
        actual: float
    ) -> ProjectionAccuracy:
        """Calculate accuracy metrics for a single projection"""
        difference = actual - projected
        abs_error = abs(difference)
        pct_error = (abs_error / max(projected, 0.1)) * 100  # Avoid division by zero
        
        return ProjectionAccuracy(
            source=source,
            player_name=player_name,
            position=position,
            projected_points=projected,
            actual_points=actual,
            difference=difference,
            absolute_error=abs_error,
            percentage_error=pct_error
        )
    
    def _find_external_projection(self, player: Any, projection_df: pd.DataFrame) -> Optional[float]:
        """Find external projection for a player"""
        # Simple name matching - could be enhanced with fuzzy matching
        matches = projection_df[projection_df['name'].str.contains(player.name, case=False, na=False)]
        if not matches.empty:
            # Return the projection value (adjust column name as needed)
            proj_col = 'projected_points' if 'projected_points' in matches.columns else 'fantasy'
            return matches.iloc[0][proj_col]
        return None
    
    def generate_position_analysis(self, accuracy_results: List[ProjectionAccuracy]) -> List[PositionAnalysis]:
        """Generate position-level analysis from accuracy results"""
        df = pd.DataFrame([
            {
                'source': acc.source,
                'position': acc.position,
                'projected': acc.projected_points,
                'actual': acc.actual_points,
                'abs_error': acc.absolute_error,
                'pct_error': acc.percentage_error
            }
            for acc in accuracy_results
        ])
        
        position_analyses = []
        
        for position in df['position'].unique():
            pos_data = df[df['position'] == position]
            
            # Calculate source accuracy for this position
            source_accuracy = pos_data.groupby('source')['abs_error'].mean().sort_values()
            best_source = source_accuracy.index[0] if len(source_accuracy) > 0 else "Unknown"
            worst_source = source_accuracy.index[-1] if len(source_accuracy) > 0 else "Unknown"
            
            analysis = PositionAnalysis(
                position=position,
                total_players=len(pos_data),
                avg_projected=pos_data['projected'].mean(),
                avg_actual=pos_data['actual'].mean(),
                avg_error=(pos_data['actual'] - pos_data['projected']).mean(),
                avg_abs_error=pos_data['abs_error'].mean(),
                best_projection_source=best_source,
                worst_projection_source=worst_source
            )
            position_analyses.append(analysis)
            
        return position_analyses
    
    def generate_week_summary(
        self, 
        week: int, 
        accuracy_results: List[ProjectionAccuracy]
    ) -> WeekSummary:
        """Generate comprehensive week summary"""
        if not accuracy_results:
            raise ValueError("No accuracy results to analyze")
            
        df = pd.DataFrame([
            {
                'source': acc.source,
                'abs_error': acc.absolute_error,
                'difference': acc.difference,
                'player': acc.player_name,
                'position': acc.position,
                'projected': acc.projected_points,
                'actual': acc.actual_points
            }
            for acc in accuracy_results
        ])
        
        # Overall source accuracy
        source_mae = df.groupby('source')['abs_error'].mean().sort_values()
        most_accurate = source_mae.index[0]
        least_accurate = source_mae.index[-1]
        
        # Find biggest boom (most over-projected)
        biggest_boom_idx = df['difference'].idxmax()
        biggest_boom = accuracy_results[biggest_boom_idx]
        
        # Find biggest bust (most under-projected) 
        biggest_bust_idx = df['difference'].idxmin()
        biggest_bust = accuracy_results[biggest_bust_idx]
        
        # Position analysis
        position_summaries = self.generate_position_analysis(accuracy_results)
        
        # Overall metrics
        overall_mae = df['abs_error'].mean()
        overall_rmse = np.sqrt((df['difference'] ** 2).mean())
        
        return WeekSummary(
            week=week,
            total_players_analyzed=len(df),
            most_accurate_source=most_accurate,
            least_accurate_source=least_accurate,
            biggest_boom=biggest_boom,
            biggest_bust=biggest_bust,
            position_summaries=position_summaries,
            overall_mae=overall_mae,
            overall_rmse=overall_rmse
        )
    
    def analyze_week(
        self, 
        week: int, 
        external_projections: Optional[Dict[str, pd.DataFrame]] = None
    ) -> WeekSummary:
        """
        Complete analysis pipeline for a given week
        
        Args:
            week: Week number to analyze
            external_projections: Optional external projection sources
            
        Returns:
            WeekSummary with complete analysis
        """
        self.logger.info(f"Starting post-week analysis for Week {week}")
        
        # Get projection accuracy results
        accuracy_results = self.analyze_projection_accuracy(week, external_projections)
        
        # Generate comprehensive summary
        summary = self.generate_week_summary(week, accuracy_results)
        
        self.logger.info(f"Analysis complete: {summary.total_players_analyzed} players analyzed")
        
        # Add team-specific analysis
        my_team = self._get_my_team(self.league)
        if my_team:
            team_performance = self.analyze_team_performance(
                team=my_team,
                week=week,
                accuracy_results=accuracy_results
            )
            summary.my_team_performance = team_performance
        
        # Add start/sit intelligence analysis
        try:
            start_sit_intelligence = self.start_sit_analyzer.analyze_league_start_sit_intelligence(
                week=week,
                projection_sources=external_projections
            )
            summary.start_sit_intelligence = start_sit_intelligence
            self.logger.info(f"Start/sit analysis complete for {len(start_sit_intelligence.team_performances)} teams")
        except Exception as e:
            self.logger.warning(f"Could not generate start/sit analysis: {e}")
            summary.start_sit_intelligence = None
        
        return summary
    
    def _get_my_team(self, league: League) -> Any:
        """Get the user's team from the league using TEAM_ID from environment"""
        try:
            import os
            from dotenv import load_dotenv
            
            load_dotenv()
            team_id = os.getenv('TEAM_ID')
            
            if team_id:
                team_id = int(team_id)
                # Find team by team_id
                for team in league.teams:
                    if team.team_id == team_id:
                        return team
                self.logger.warning(f"Team with ID {team_id} not found in league")
            
            # Fallback to first team if TEAM_ID not found
            self.logger.info("Using first team as fallback (TEAM_ID not set or team not found)")
            return league.teams[0]
        except (IndexError, AttributeError, ValueError) as e:
            self.logger.warning(f"Could not identify user's team: {e}")
            return None
    
    def analyze_team_performance(
        self, 
        team: Any, 
        week: int,
        accuracy_results: List[ProjectionAccuracy]
    ) -> TeamPerformance:
        """
        Analyze specific team's performance vs projections
        
        Args:
            team: Team object to analyze
            week: Week number
            accuracy_results: All accuracy results to filter from
            
        Returns:
            TeamPerformance object with team-specific insights
        """
        # Get team's box score for the week
        box_scores = self.league.box_scores(week=week)
        team_matchup = None
        team_lineup = []
        
        # Find this team's matchup and lineup
        for matchup in box_scores:
            if matchup.home_team == team:
                team_lineup = matchup.home_lineup
                team_matchup = matchup
                break
            elif matchup.away_team == team:
                team_lineup = matchup.away_lineup
                team_matchup = matchup
                break
        
        if not team_lineup:
            self.logger.warning(f"Could not find lineup for team {team.team_name}")
            # Return empty performance object
            return TeamPerformance(
                team_name=team.team_name,
                total_projected=0.0,
                total_actual=0.0,
                difference=0.0,
                players_analyzed=0,
                lineup_players=0,
                bench_players=0,
                best_performer=accuracy_results[0] if accuracy_results else None,
                worst_performer=accuracy_results[0] if accuracy_results else None,
                lineup_accuracy=0.0,
                bench_accuracy=0.0
            )
        
        # Filter accuracy results to only this team's players
        team_player_names = [player.name for player in team_lineup]
        team_accuracy_results = [
            acc for acc in accuracy_results 
            if acc.player_name in team_player_names and acc.source == "ESPN"  # Use ESPN as primary
        ]
        
        if not team_accuracy_results:
            self.logger.warning(f"No accuracy data found for team {team.team_name}")
        
        # Calculate team totals
        total_projected = sum(acc.projected_points for acc in team_accuracy_results)
        total_actual = sum(acc.actual_points for acc in team_accuracy_results)
        difference = total_actual - total_projected
        
        # Separate lineup vs bench players
        lineup_results = []
        bench_results = []
        
        for acc in team_accuracy_results:
            # Find the corresponding player in lineup
            lineup_player = next((p for p in team_lineup if p.name == acc.player_name), None)
            if lineup_player and hasattr(lineup_player, 'slot_position'):
                # Check if player was in starting lineup (not bench)
                if lineup_player.slot_position not in ['BE', 'Bench', 'BN']:
                    lineup_results.append(acc)
                else:
                    bench_results.append(acc)
            else:
                # Default to lineup if we can't determine
                lineup_results.append(acc)
        
        # Find best and worst performers
        best_performer = max(team_accuracy_results, key=lambda x: x.difference) if team_accuracy_results else None
        worst_performer = min(team_accuracy_results, key=lambda x: x.difference) if team_accuracy_results else None
        
        # Calculate accuracy metrics
        lineup_accuracy = np.mean([acc.absolute_error for acc in lineup_results]) if lineup_results else 0.0
        bench_accuracy = np.mean([acc.absolute_error for acc in bench_results]) if bench_results else 0.0
        
        return TeamPerformance(
            team_name=team.team_name,
            total_projected=total_projected,
            total_actual=total_actual,
            difference=difference,
            players_analyzed=len(team_accuracy_results),
            lineup_players=len(lineup_results),
            bench_players=len(bench_results),
            best_performer=best_performer,
            worst_performer=worst_performer,
            lineup_accuracy=lineup_accuracy,
            bench_accuracy=bench_accuracy
        )