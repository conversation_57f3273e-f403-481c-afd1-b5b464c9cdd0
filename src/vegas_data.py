import requests
import pandas as pd
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import json

class VegasDataFetcher:
    """
    Fetches Vegas betting lines and game totals for NFL games.
    Uses The Odds API (free tier: 500 requests/month).
    """
    
    def __init__(self, api_key: str = None):
        """Initialize with optional API key for The Odds API"""
        self.api_key = api_key
        self.base_url = "https://api.the-odds-api.com/v4"
        self.sport = "americanfootball_nfl"
        
        # Fallback to ESPN API for basic game info if no API key
        self.espn_api_base = "https://site.api.espn.com/apis/site/v2/sports/football/nfl"
        
    def get_week_betting_lines(self, week: int = None) -> pd.DataFrame:
        """
        Get betting lines for all NFL games in a given week.
        
        Returns DataFrame with columns:
        - home_team, away_team, spread, total, home_implied_total, away_implied_total
        """
        if self.api_key:
            return self._get_odds_api_lines()
        else:
            return self._get_espn_game_data(week)
    
    def _get_odds_api_lines(self) -> pd.DataFrame:
        """Fetch from The Odds API (requires API key)"""
        try:
            url = f"{self.base_url}/sports/{self.sport}/odds"
            params = {
                'apiKey': self.api_key,
                'regions': 'us',
                'markets': 'h2h,spreads,totals',
                'oddsFormat': 'american',
                'dateFormat': 'iso'
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            games_data = []
            for game in response.json():
                game_data = self._parse_odds_api_game(game)
                if game_data:
                    games_data.append(game_data)
            
            return pd.DataFrame(games_data)
            
        except Exception as e:
            print(f"Error fetching from Odds API: {e}")
            return self._get_espn_game_data()
    
    def _get_espn_game_data(self, week: int = None) -> pd.DataFrame:
        """Fallback: Get basic game info from ESPN (no betting lines)"""
        try:
            # Determine current NFL week if not provided
            if week is None:
                week = self._get_current_nfl_week()
                
            url = f"{self.espn_api_base}/scoreboard"
            params = {'week': week}
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            games_data = []
            
            for game in data.get('events', []):
                try:
                    competitors = game.get('competitions', [{}])[0].get('competitors', [])
                    if len(competitors) >= 2:
                        home_team = None
                        away_team = None
                        
                        for comp in competitors:
                            team_name = comp.get('team', {}).get('displayName', '')
                            if comp.get('homeAway') == 'home':
                                home_team = team_name
                            else:
                                away_team = team_name
                        
                        if home_team and away_team:
                            games_data.append({
                                'home_team': home_team,
                                'away_team': away_team,
                                'spread': 0.0,  # Not available from ESPN
                                'total': 44.0,  # Default NFL average
                                'home_implied_total': 22.0,
                                'away_implied_total': 22.0,
                                'data_source': 'ESPN_fallback'
                            })
                            
                except Exception as e:
                    print(f"Error parsing ESPN game: {e}")
                    continue
            
            return pd.DataFrame(games_data)
            
        except Exception as e:
            print(f"Error fetching from ESPN: {e}")
            return pd.DataFrame()
    
    def _parse_odds_api_game(self, game: Dict) -> Optional[Dict]:
        """Parse a single game from The Odds API"""
        try:
            home_team = game['home_team']
            away_team = game['away_team']
            
            # Extract betting data from first available bookmaker
            bookmakers = game.get('bookmakers', [])
            if not bookmakers:
                return None
                
            bookmaker = bookmakers[0]  # Use first available bookmaker
            markets = {market['key']: market for market in bookmaker.get('markets', [])}
            
            # Get spread
            spread = 0.0
            if 'spreads' in markets:
                spread_outcomes = markets['spreads'].get('outcomes', [])
                for outcome in spread_outcomes:
                    if outcome['name'] == home_team:
                        spread = float(outcome.get('point', 0))
                        break
            
            # Get total
            total = 44.0  # Default
            if 'totals' in markets:
                total_outcomes = markets['totals'].get('outcomes', [])
                if total_outcomes:
                    total = float(total_outcomes[0].get('point', 44.0))
            
            # Calculate implied totals
            home_implied = (total - spread) / 2
            away_implied = (total + spread) / 2
            
            return {
                'home_team': home_team,
                'away_team': away_team,
                'spread': spread,
                'total': total,
                'home_implied_total': home_implied,
                'away_implied_total': away_implied,
                'data_source': 'odds_api'
            }
            
        except Exception as e:
            print(f"Error parsing game: {e}")
            return None
    
    def _get_current_nfl_week(self) -> int:
        """Estimate current NFL week based on date"""
        # NFL season typically starts first Tuesday after Labor Day
        # This is a rough estimate - you might want to make this more precise
        now = datetime.now()
        
        # Assume week 1 starts around September 7th
        season_start = datetime(now.year, 9, 7)
        if now < season_start:
            season_start = datetime(now.year - 1, 9, 7)
            
        days_since_start = (now - season_start).days
        week = max(1, min(18, (days_since_start // 7) + 1))
        
        return week
    
    def get_team_game_context(self, team_name: str, week: int = None) -> Dict:
        """
        Get game context for a specific team.
        
        Returns:
        - implied_total: Team's implied point total
        - spread: Point spread (positive = underdog)
        - game_total: Total points expected in game
        - game_script: Predicted game script (blowout/close/etc)
        """
        betting_lines = self.get_week_betting_lines(week)
        
        if betting_lines.empty:
            return self._get_default_context()
        
        # Find team's game
        team_game = None
        is_home = False
        
        for _, game in betting_lines.iterrows():
            if self._team_name_matches(team_name, game['home_team']):
                team_game = game
                is_home = True
                break
            elif self._team_name_matches(team_name, game['away_team']):
                team_game = game
                is_home = False
                break
        
        if team_game is None:
            return self._get_default_context()
        
        # Calculate context
        if is_home:
            implied_total = team_game['home_implied_total']
            spread = -team_game['spread']  # Flip for home team perspective
        else:
            implied_total = team_game['away_implied_total']
            spread = team_game['spread']
        
        game_script = self._determine_game_script(spread, team_game['total'])
        
        return {
            'implied_total': implied_total,
            'spread': spread,
            'game_total': team_game['total'],
            'game_script': game_script,
            'is_home': is_home,
            'opponent': team_game['away_team'] if is_home else team_game['home_team']
        }
    
    def _team_name_matches(self, espn_team: str, vegas_team: str) -> bool:
        """Check if ESPN team name matches Vegas team name"""
        # Common team name mappings
        team_mappings = {
            'Cardinals': ['Arizona', 'ARI'],
            'Falcons': ['Atlanta', 'ATL'],
            'Ravens': ['Baltimore', 'BAL'],
            'Bills': ['Buffalo', 'BUF'],
            'Panthers': ['Carolina', 'CAR'],
            'Bears': ['Chicago', 'CHI'],
            'Bengals': ['Cincinnati', 'CIN'],
            'Browns': ['Cleveland', 'CLE'],
            'Cowboys': ['Dallas', 'DAL'],
            'Broncos': ['Denver', 'DEN'],
            'Lions': ['Detroit', 'DET'],
            'Packers': ['Green Bay', 'GB'],
            'Texans': ['Houston', 'HOU'],
            'Colts': ['Indianapolis', 'IND'],
            'Jaguars': ['Jacksonville', 'JAX'],
            'Chiefs': ['Kansas City', 'KC'],
            'Chargers': ['Los Angeles Chargers', 'LAC'],
            'Rams': ['Los Angeles Rams', 'LAR'],
            'Dolphins': ['Miami', 'MIA'],
            'Vikings': ['Minnesota', 'MIN'],
            'Patriots': ['New England', 'NE'],
            'Saints': ['New Orleans', 'NO'],
            'Giants': ['New York Giants', 'NYG'],
            'Jets': ['New York Jets', 'NYJ'],
            'Raiders': ['Las Vegas', 'LV'],
            'Eagles': ['Philadelphia', 'PHI'],
            'Steelers': ['Pittsburgh', 'PIT'],
            '49ers': ['San Francisco', 'SF'],
            'Seahawks': ['Seattle', 'SEA'],
            'Buccaneers': ['Tampa Bay', 'TB'],
            'Titans': ['Tennessee', 'TEN'],
            'Commanders': ['Washington', 'WAS']
        }
        
        # Simple string matching first
        if espn_team.lower() in vegas_team.lower() or vegas_team.lower() in espn_team.lower():
            return True
            
        # Check mappings
        for nickname, variations in team_mappings.items():
            if nickname.lower() in espn_team.lower():
                for variation in variations:
                    if variation.lower() in vegas_team.lower():
                        return True
                        
        return False
    
    def _determine_game_script(self, spread: float, total: float) -> str:
        """Determine likely game script based on spread and total"""
        abs_spread = abs(spread)
        
        if abs_spread >= 7:
            if total >= 48:
                return "high_scoring_blowout"  # Lots of points, one team dominates
            else:
                return "low_scoring_blowout"   # Defensive game, one team controls
        elif abs_spread >= 3:
            if total >= 46:
                return "high_scoring_competitive"  # Back and forth scoring
            else:
                return "low_scoring_competitive"   # Defensive battle
        else:
            if total >= 46:
                return "high_scoring_close"    # Shootout
            else:
                return "low_scoring_close"     # Defensive slugfest
    
    def _get_default_context(self) -> Dict:
        """Default context when no betting data available"""
        return {
            'implied_total': 22.0,
            'spread': 0.0,
            'game_total': 44.0,
            'game_script': 'average_game',
            'is_home': True,
            'opponent': 'Unknown'
        }