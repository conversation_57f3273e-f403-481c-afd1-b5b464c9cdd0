"""
Enhanced Projection Storage System - Phase 1
DuckDB-powered analytics with multi-source support and robust error handling.

Key Features:
- DuckDB for analytical queries and DataFrame integration  
- Multi-source projection capture (ESPN, FanDuel, WinWithOdds, DraftSharks)
- Data validation and integrity checks
- Automated retry logic with exponential backoff
- Parquet archival for cold storage
- Comprehensive logging and monitoring
"""

import duckdb
import pandas as pd
import json
import os
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from pathlib import Path
import numpy as np
from contextlib import contextmanager
from espn_api.football import League
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import existing projection functions 
# Note: These will be integrated properly in Phase 2
import logging

@dataclass
class StrategicProjectionRecord:
    """Strategic projection record with value preservation"""
    # Core normalized fields (for cross-source analytics)
    week: int
    year: int
    player_id: Optional[str]
    player_name: str
    position: str
    team: str
    source: str
    projected_points: float
    capture_timestamp: str = None
    
    # Complete raw data preservation
    raw_source_data: Dict[str, Any] = None
    
    # Strategic value extraction (computed)
    strategic_insights: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.capture_timestamp is None:
            self.capture_timestamp = datetime.now().isoformat()
        if self.raw_source_data is None:
            self.raw_source_data = {}
        if self.strategic_insights is None:
            self.strategic_insights = self.extract_strategic_insights()
    
    def extract_strategic_insights(self) -> Dict[str, Any]:
        """Extract strategic value while preserving original data"""
        insights = {}
        
        if self.source == 'DraftSharks':
            insights.update({
                'projection_range': {
                    'floor': self.raw_source_data.get('Floor'),
                    'ceiling': self.raw_source_data.get('Ceil'), 
                    'conservative': self.raw_source_data.get('Cons.'),
                    'range_width': (self.raw_source_data.get('Ceil', 0) - 
                                  self.raw_source_data.get('Floor', 0)) if 
                                  (self.raw_source_data.get('Ceil') and self.raw_source_data.get('Floor')) else None
                },
                'matchup_context': {
                    'sos_pct': self.raw_source_data.get('SOS'),
                    'matchup': self.raw_source_data.get('Matchup')
                },
                'dfs_efficiency': {
                    'fanduel_value': self.raw_source_data.get('$ / Point'),
                    'fanduel_salary': self.raw_source_data.get('FanDuel $'),
                    'draftkings_salary': self.raw_source_data.get('DraftKings $')
                },
                'stat_projections': {
                    'rush_yards': self.raw_source_data.get('Rush Yds'),
                    'rush_tds': self.raw_source_data.get('Rush TDs'),
                    'receptions': self.raw_source_data.get('Rec'),
                    'rec_yards': self.raw_source_data.get('Rec Yds'),
                    'rec_tds': self.raw_source_data.get('Rec TDs')
                }
            })
            
        elif self.source == 'WinWithOdds':
            insights.update({
                'season_context': {
                    'season_total': self.raw_source_data.get('fantasy_points'),
                    'passing_tds': self.raw_source_data.get('passing_tds'),
                    'rushing_tds': self.raw_source_data.get('rushing_tds')
                },
                'trend_indicators': self.raw_source_data.get('trend_data', {}),
                'dfs_context': self.raw_source_data.get('dfs_values', {})
            })
            
        elif self.source == 'FanDuel':
            insights.update({
                'matchup_context': {
                    'opponent': self.raw_source_data.get('opponent'),
                    'game_info': self.raw_source_data.get('gameInfo', {})
                },
                'stat_projections': self.raw_source_data.get('stat_breakdown', {})
            })
        
        # Add projection confidence metrics
        insights['projection_confidence'] = self._calculate_confidence_metrics()
        
        return insights
    
    def _calculate_confidence_metrics(self) -> Dict[str, Any]:
        """Calculate confidence metrics based on available data"""
        confidence = {'level': 'medium'}  # default
        
        if self.source == 'DraftSharks' and self.raw_source_data:
            floor = self.raw_source_data.get('Floor', 0)
            ceiling = self.raw_source_data.get('Ceil', 0)
            if floor and ceiling:
                range_width = ceiling - floor
                # Narrow range = higher confidence
                if range_width < 5:
                    confidence['level'] = 'high'
                elif range_width > 10:
                    confidence['level'] = 'low'
                confidence['range_width'] = range_width
                
        return confidence
    
    def get_ceiling_upside(self) -> Optional[float]:
        """Get ceiling upside over projection"""
        ceiling = self.strategic_insights.get('projection_range', {}).get('ceiling')
        return ceiling - self.projected_points if ceiling else None
    
    def get_floor_downside(self) -> Optional[float]:
        """Get floor downside from projection"""  
        floor = self.strategic_insights.get('projection_range', {}).get('floor')
        return self.projected_points - floor if floor else None

class DraftSharksProjections:
    """DraftSharks projection integration"""
    
    def __init__(self, csv_path: Optional[str] = None):
        self.csv_path = csv_path or "cache/ds-weekly-rankings.csv"
        
    def fetch_projections(self, week: int) -> List[StrategicProjectionRecord]:
        """Load DraftSharks projections from CSV"""
        if not os.path.exists(self.csv_path):
            logging.warning(f"DraftSharks CSV not found: {self.csv_path}")
            return []
            
        try:
            df = pd.read_csv(self.csv_path)
            projections = []
            
            for _, row in df.iterrows():
                # Store complete raw data
                raw_data = row.to_dict()
                
                record = StrategicProjectionRecord(
                    week=week,
                    year=datetime.now().year,
                    player_id=None,  # DraftSharks doesn't provide IDs
                    player_name=row['Player'],
                    position=row['Pos.'],
                    team=row['Team'],
                    source='DraftSharks',
                    projected_points=float(row['Proj']),
                    raw_source_data=raw_data  # Preserve everything
                )
                projections.append(record)
                
            logging.info(f"Loaded {len(projections)} DraftSharks projections")
            return projections
            
        except Exception as e:
            logging.error(f"Failed to load DraftSharks projections: {e}")
            return []

class ProjectionValidator:
    """Data validation and integrity checking"""
    
    def __init__(self):
        # Reasonable projection ranges by position
        self.position_ranges = {
            'QB': (5.0, 45.0),
            'RB': (0.0, 35.0), 
            'WR': (0.0, 30.0),
            'TE': (0.0, 25.0),
            'K': (3.0, 20.0),
            'D/ST': (0.0, 25.0)
        }
        
    def validate_projections(self, projections: List[StrategicProjectionRecord]) -> List[StrategicProjectionRecord]:
        """Comprehensive validation with automatic fixes"""
        valid_projections = []
        
        for proj in projections:
            try:
                # Schema validation
                self._validate_schema(proj)
                
                # Range validation
                self._validate_projection_range(proj)
                
                # Data type validation
                self._validate_data_types(proj)
                
                valid_projections.append(proj)
                
            except ValidationError as e:
                logging.warning(f"Validation failed for {proj.player_name}: {e}")
                # Attempt to fix common issues
                fixed_proj = self._attempt_fix(proj)
                if fixed_proj:
                    valid_projections.append(fixed_proj)
                    
        logging.info(f"Validated {len(valid_projections)}/{len(projections)} projections")
        return valid_projections
    
    def _validate_schema(self, proj: StrategicProjectionRecord):
        """Ensure required fields are present and valid"""
        if not proj.player_name or proj.player_name.strip() == '':
            raise ValidationError("Missing player name")
            
        if proj.position not in self.position_ranges:
            raise ValidationError(f"Invalid position: {proj.position}")
            
        if proj.projected_points is None:
            raise ValidationError("Missing projected points")
    
    def _validate_projection_range(self, proj: StrategicProjectionRecord):
        """Check if projections are within reasonable ranges"""
        min_proj, max_proj = self.position_ranges[proj.position]
        
        if not (min_proj <= proj.projected_points <= max_proj):
            raise ValidationError(
                f"Projection {proj.projected_points} outside range {min_proj}-{max_proj}"
            )
    
    def _validate_data_types(self, proj: StrategicProjectionRecord):
        """Ensure data types are correct"""
        try:
            float(proj.projected_points)
            int(proj.week)
            int(proj.year)
        except (ValueError, TypeError) as e:
            raise ValidationError(f"Invalid data types: {e}")
    
    def _attempt_fix(self, proj: StrategicProjectionRecord) -> Optional[StrategicProjectionRecord]:
        """Attempt to fix common validation issues"""
        try:
            # Fix missing team
            if not proj.team:
                proj.team = 'UNK'
                
            # Fix projection outliers by capping to reasonable ranges
            if proj.position in self.position_ranges:
                min_proj, max_proj = self.position_ranges[proj.position]
                proj.projected_points = max(min_proj, min(max_proj, proj.projected_points))
                
            # Re-validate
            self._validate_schema(proj)
            self._validate_projection_range(proj)
            self._validate_data_types(proj)
            
            return proj
            
        except ValidationError:
            return None

class ValidationError(Exception):
    """Custom validation error"""
    pass

class EnhancedProjectionStorage:
    """
    DuckDB-powered projection storage with analytical capabilities
    """
    
    def __init__(self, db_path: str = "data/projections_analytics.duckdb"):
        """Initialize enhanced storage system"""
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(exist_ok=True)
        
        # Setup logging first
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Initialize database connection
        self.conn = duckdb.connect(str(self.db_path))
        self._setup_schema()
        self._migrate_schema()  # Add this line
        
        # Initialize ESPN League
        self.league_id = int(os.getenv('LEAGUE_ID', 0))
        self.year = int(os.getenv('YEAR', datetime.now().year))
        self.espn_s2 = os.getenv('ESPN_S2')
        self.swid = os.getenv('SWID')
        try:
            self.league = League(league_id=self.league_id, year=self.year, espn_s2=self.espn_s2, swid=self.swid)
            self.logger.info("ESPN League initialized successfully.")
        except Exception as e:
            self.logger.error(f"Failed to initialize ESPN League: {e}")
            self.league = None # Ensure league is None if initialization fails

        # Initialize data sources
        self.sources = {
            'draftsharks': DraftSharksProjections(),
            'espn': None # Placeholder, logic is handled separately
            # 'fanduel' and 'winwithodds' will be integrated in Phase 2
        }
        
        # Initialize validator
        self.validator = ProjectionValidator()
        
        self.logger.info(f"Enhanced projection storage initialized at {self.db_path}")
    
    def _setup_schema(self):
        """Create optimized analytical schema"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS projections (
            week INTEGER,
            year INTEGER,
            player_id VARCHAR,
            player_name VARCHAR,
            position VARCHAR,
            team VARCHAR,
            source VARCHAR,
            projected_points DOUBLE,
            capture_timestamp TIMESTAMP,
            
            -- Complete raw data preservation
            raw_source_data JSON,
            
            -- Strategic insights extraction  
            strategic_insights JSON,
            
            -- Constraints
            PRIMARY KEY (week, year, player_name, source)
        );
        """)
        
        # Team strength of schedule table with historical tracking
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS team_strength_of_schedule (
            team VARCHAR,
            position VARCHAR,
            baseline DOUBLE,
            week_1 DOUBLE, week_2 DOUBLE, week_3 DOUBLE, week_4 DOUBLE, week_5 DOUBLE,
            week_6 DOUBLE, week_7 DOUBLE, week_8 DOUBLE, week_9 DOUBLE, week_10 DOUBLE,
            week_11 DOUBLE, week_12 DOUBLE, week_13 DOUBLE, week_14 DOUBLE, week_15 DOUBLE,
            week_16 DOUBLE, week_17 DOUBLE,
            next_4_avg DOUBLE,
            playoffs_avg DOUBLE,
            capture_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            data_source VARCHAR DEFAULT 'Subvertadown',
            
            -- Allow multiple historical entries
            PRIMARY KEY (team, position, capture_timestamp)
        );
        """)
    
    def _migrate_schema(self):
        """Apply schema migrations for existing databases"""
        try:
            # Check if team_strength_of_schedule table exists and needs migration
            table_exists = self.conn.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='team_strength_of_schedule'
            """).fetchall()

            if table_exists:
                # Check current primary key structure
                pk_info = self.conn.execute("PRAGMA table_info(team_strength_of_schedule)").fetchall()
                pk_columns = [row[1] for row in pk_info if row[5]]  # row[5] is pk flag

                # If primary key doesn't include capture_timestamp, we need to migrate
                if 'capture_timestamp' not in pk_columns:
                    self.logger.info("Migrating team_strength_of_schedule table to support historical data...")

                    # Check if capture_timestamp column exists
                    has_timestamp = any(row[1] == 'capture_timestamp' for row in pk_info)

                    if not has_timestamp:
                        # Add missing columns first
                        self.conn.execute("""
                            ALTER TABLE team_strength_of_schedule
                            ADD COLUMN capture_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        """)
                        self.conn.execute("""
                            ALTER TABLE team_strength_of_schedule
                            ADD COLUMN data_source VARCHAR DEFAULT 'Subvertadown'
                        """)

                    # Create new table with correct schema
                    self.conn.execute("""
                        CREATE TABLE team_strength_of_schedule_new (
                            team VARCHAR,
                            position VARCHAR,
                            baseline DOUBLE,
                            week_1 DOUBLE, week_2 DOUBLE, week_3 DOUBLE, week_4 DOUBLE, week_5 DOUBLE,
                            week_6 DOUBLE, week_7 DOUBLE, week_8 DOUBLE, week_9 DOUBLE, week_10 DOUBLE,
                            week_11 DOUBLE, week_12 DOUBLE, week_13 DOUBLE, week_14 DOUBLE, week_15 DOUBLE,
                            week_16 DOUBLE, week_17 DOUBLE,
                            next_4_avg DOUBLE,
                            playoffs_avg DOUBLE,
                            capture_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            data_source VARCHAR DEFAULT 'Subvertadown',

                            PRIMARY KEY (team, position, capture_timestamp)
                        )
                    """)

                    # Copy existing data with current timestamp
                    self.conn.execute("""
                        INSERT INTO team_strength_of_schedule_new
                        SELECT team, position, baseline, week_1, week_2, week_3, week_4, week_5,
                               week_6, week_7, week_8, week_9, week_10, week_11, week_12, week_13,
                               week_14, week_15, week_16, week_17, next_4_avg, playoffs_avg,
                               COALESCE(capture_timestamp, CURRENT_TIMESTAMP) as capture_timestamp,
                               COALESCE(data_source, 'Subvertadown') as data_source
                        FROM team_strength_of_schedule
                    """)

                    # Drop old table and rename new one
                    self.conn.execute("DROP TABLE team_strength_of_schedule")
                    self.conn.execute("ALTER TABLE team_strength_of_schedule_new RENAME TO team_strength_of_schedule")

                    self.logger.info("✅ Schema migration completed - historical data preservation enabled")

        except Exception as e:
            self.logger.warning(f"Schema migration failed (may be expected): {e}")
    
    @contextmanager
    def transaction(self):
        """Database transaction context manager"""
        try:
            self.conn.begin()
            yield
            self.conn.commit()
        except Exception as e:
            self.conn.rollback()
            self.logger.error(f"Transaction failed: {e}")
            raise
    
    def capture_all_sources(self, week: int, sources: List[str] = None, max_retries: int = 3) -> Dict[str, int]:
        """
        Capture projections from all sources with retry logic
        
        Returns:
            Dict mapping source name to number of projections captured
        """
        sources = sources or list(self.sources.keys())
        results = {}
        
        for source_name in sources:
            if source_name not in self.sources:
                self.logger.warning(f"Unknown source: {source_name}")
                continue
                
            result = self._capture_source_with_retry(source_name, week, max_retries)
            results[source_name] = result
        
        return results
    
    def _capture_source_with_retry(self, source_name: str, week: int, max_retries: int) -> int:
        """Capture from single source with exponential backoff retry"""
        
        for attempt in range(max_retries):
            try:
                # Handle ESPN source differently (needs league integration)
                if source_name == 'espn':
                    projections = self._capture_espn_projections(week)
                else:
                    source = self.sources[source_name]
                    projections = source.fetch_projections(week)
                
                # Convert to ProjectionRecord format if needed
                if projections and not isinstance(projections[0], StrategicProjectionRecord):
                    projections = self._convert_to_projection_records(projections, source_name, week)
                
                # Validate projections
                valid_projections = self.validator.validate_projections(projections)
                
                # Store in database
                stored_count = self._store_projections(valid_projections)
                
                # Record metadata
                self._record_capture_metadata(source_name, week, len(projections), 
                                            len(projections) - len(valid_projections), stored_count)
                
                self.logger.info(f"Captured {stored_count} projections from {source_name}")
                return stored_count
                
            except Exception as e:
                wait_time = (2 ** attempt) * 60  # 1min, 2min, 4min
                self.logger.warning(f"Attempt {attempt+1} failed for {source_name}: {e}")
                
                if attempt < max_retries - 1:
                    self.logger.info(f"Retrying in {wait_time}s...")
                    time.sleep(wait_time)
                else:
                    self.logger.error(f"Failed to capture {source_name} after {max_retries} attempts")
                    
        return 0
    
    def _convert_to_projection_records(self, projections: List[Dict], source_name: str, week: int) -> List[StrategicProjectionRecord]:
        """Convert raw projection data to ProjectionRecord format"""
        records = []
        
        for proj in projections:
            try:
                record = StrategicProjectionRecord(
                    week=week,
                    year=datetime.now().year,
                    player_id=proj.get('player_id'),
                    player_name=proj['player_name'],
                    position=proj['position'],
                    team=proj.get('team', 'UNK'),
                    source=source_name,
                    projected_points=float(proj['projected_points']),
                    raw_source_data=proj  # Store complete raw data
                )
                records.append(record)
                
            except Exception as e:
                self.logger.warning(f"Failed to convert projection: {e}")
                
        return records
    
    def _capture_espn_projections(self, week: int) -> List[StrategicProjectionRecord]:
        """Special handling for ESPN projections (requires league integration)"""
        projections = []
        self.logger.info(f"Capturing ESPN projections for week {week}")

        # Get all free agents and rostered players
        all_players = self.league.free_agents(size=150)
        for team in self.league.teams:
            all_players.extend(team.roster)

        self.logger.info(f"Found {len(all_players)} players from ESPN API.")

        for player in all_players:
            if hasattr(player, 'stats') and player.stats and week in player.stats:
                week_stats = player.stats[week]
                projected_points = week_stats.get('projected_points', 0)
                
                if projected_points > 0:
                    record = StrategicProjectionRecord(
                        week=week,
                        year=self.year,
                        player_id=str(player.playerId),
                        player_name=player.name,
                        position=player.position,
                        team=player.proTeam,
                        source='ESPN',
                        projected_points=float(projected_points),
                        raw_source_data=week_stats
                    )
                    projections.append(record)

        self.logger.info(f"Captured {len(projections)} ESPN projections")
        return projections

    def _store_projections(self, projections: List[StrategicProjectionRecord]) -> int:
        """Store validated projections in database"""
        if not projections:
            return 0
        
        try:
            with self.transaction():
                # Convert to DataFrame for efficient bulk insert
                data = []
                for proj in projections:
                    data.append({
                        'week': proj.week,
                        'year': proj.year,
                        'player_id': proj.player_id,
                        'player_name': proj.player_name,
                        'position': proj.position,
                        'team': proj.team,
                        'source': proj.source,
                        'projected_points': proj.projected_points,
                        'capture_timestamp': proj.capture_timestamp,
                        'raw_source_data': json.dumps(proj.raw_source_data),
                        'strategic_insights': json.dumps(proj.strategic_insights)
                    })
                
                df = pd.DataFrame(data)
                
                # Use DuckDB's efficient DataFrame insertion
                self.conn.register('temp_projections', df)
                self.conn.execute("""
                INSERT OR REPLACE INTO projections 
                SELECT * FROM temp_projections
                """)
                
                return len(projections)
                
        except Exception as e:
            self.logger.error(f"Failed to store projections: {e}")
            return 0
    
    def _record_capture_metadata(self, source_name: str, week: int, total: int, errors: int, stored: int):
        """Record capture statistics for monitoring"""
        success_rate = stored / total if total > 0 else 0.0
        
        self.conn.execute("""
        INSERT OR REPLACE INTO capture_metadata 
        (week, year, source, capture_timestamp, total_projections, validation_errors, success_rate)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        """, [week, datetime.now().year, source_name, datetime.now().isoformat(),
              total, errors, success_rate])
    
    def get_projections(self, week: int, sources: List[str] = None, positions: List[str] = None) -> pd.DataFrame:
        """Retrieve projections with analytical capabilities"""
        
        where_conditions = [f"week = {week}"]
        
        if sources:
            source_list = "', '".join(sources)
            where_conditions.append(f"source IN ('{source_list}')")
            
        if positions:
            pos_list = "', '".join(positions)
            where_conditions.append(f"position IN ('{pos_list}')")
        
        where_clause = " AND ".join(where_conditions)
        
        query = f"""
        SELECT * FROM projections 
        WHERE {where_clause}
        ORDER BY position, projected_points DESC
        """
        
        return self.conn.execute(query).df()
    
    def analytical_query(self, query: str) -> pd.DataFrame:
        """Execute custom analytical SQL queries"""
        return self.conn.execute(query).df()
    
    def get_source_accuracy_comparison(self, weeks_back: int = 4) -> pd.DataFrame:
        """Compare accuracy across sources (requires actual results data)"""
        # Placeholder for future implementation with actual results
        query = """
        SELECT 
            source,
            position,
            COUNT(*) as total_projections,
            AVG(projected_points) as avg_projection,
            STDDEV(projected_points) as projection_variance
        FROM projections 
        WHERE week >= ? 
        GROUP BY source, position
        ORDER BY source, position
        """
        
        current_week = datetime.now().isocalendar()[1]  # ISO week number
        return self.conn.execute(query, [current_week - weeks_back]).df()
    
    def archive_old_weeks(self, weeks_to_keep: int = 8):
        """Archive old projection data to Parquet files"""
        current_week = datetime.now().isocalendar()[1]
        archive_threshold = current_week - weeks_to_keep
        
        # Get old data
        old_data = self.conn.execute(f"""
        SELECT * FROM projections WHERE week < {archive_threshold}
        """).df()
        
        if len(old_data) > 0:
            # Save to Parquet
            archive_dir = Path("data/archive")
            archive_dir.mkdir(exist_ok=True)
            
            archive_file = archive_dir / f"projections_before_week_{current_week}.parquet"
            old_data.to_parquet(archive_file, compression='snappy')
            
            # Remove from active database
            self.conn.execute(f"DELETE FROM projections WHERE week < {archive_threshold}")
            
            self.logger.info(f"Archived {len(old_data)} old projections to {archive_file}")

    def get_current_team_sos(self, position: str = None) -> pd.DataFrame:
        """Get the most recent SOS data for each team/position"""
        position_filter = f"AND position = '{position}'" if position else ""
        
        query = f"""
        WITH latest_sos AS (
            SELECT team, position, 
                   MAX(capture_timestamp) as latest_timestamp
            FROM team_strength_of_schedule 
            WHERE 1=1 {position_filter}
            GROUP BY team, position
        )
        SELECT sos.* 
        FROM team_strength_of_schedule sos
        JOIN latest_sos l ON sos.team = l.team 
                         AND sos.position = l.position 
                         AND sos.capture_timestamp = l.latest_timestamp
        ORDER BY sos.position, sos.team
        """
        
        return self.conn.execute(query).df()

    def get_team_sos_history(self, team: str, position: str) -> pd.DataFrame:
        """Get historical SOS changes for a specific team/position"""
        query = """
        SELECT * FROM team_strength_of_schedule 
        WHERE team = ? AND position = ?
        ORDER BY capture_timestamp DESC
        """
        
        return self.conn.execute(query, [team, position]).df()

    def get_sos_changes_since(self, days_back: int = 7, position: str = None) -> pd.DataFrame:
        """Find teams whose SOS has changed in the last N days"""
        position_filter = f"AND position = '{position}'" if position else ""
        
        query = f"""
        WITH recent_changes AS (
            SELECT team, position, baseline, week_1, capture_timestamp,
                   LAG(baseline) OVER (PARTITION BY team, position ORDER BY capture_timestamp) as prev_baseline,
                   LAG(week_1) OVER (PARTITION BY team, position ORDER BY capture_timestamp) as prev_week_1
            FROM team_strength_of_schedule 
            WHERE capture_timestamp >= datetime('now', '-{days_back} days') {position_filter}
        )
        SELECT team, position, baseline, prev_baseline,
               (baseline - prev_baseline) as baseline_change,
               week_1, prev_week_1,
               (week_1 - prev_week_1) as week_1_change,
               capture_timestamp
        FROM recent_changes 
        WHERE prev_baseline IS NOT NULL 
        AND (ABS(baseline - prev_baseline) > 0.1 OR ABS(week_1 - prev_week_1) > 0.1)
        ORDER BY ABS(baseline - prev_baseline) DESC
        """
        
        return self.conn.execute(query).df()
    
    def close(self):
        """Close database connection"""
        if self.conn:
            self.conn.close()


class StrategicAnalytics:
    """Strategic analytics for ceiling/floor analysis and cross-source insights"""
    
    def __init__(self, storage: EnhancedProjectionStorage):
        self.storage = storage
    
    def get_ceiling_plays(self, week: int, position: str = None, min_ceiling_upside: float = 5.0) -> pd.DataFrame:
        """Find players with highest ceiling upside for GPP/ceiling strategies"""
        position_filter = f"AND position = '{position}'" if position else ""
        
        query = f"""
        SELECT 
            player_name,
            position,
            team,
            projected_points,
            json_extract(strategic_insights, '$.projection_range.ceiling') as ceiling,
            json_extract(strategic_insights, '$.projection_range.floor') as floor,
            (json_extract(strategic_insights, '$.projection_range.ceiling') - projected_points) as ceiling_upside,
            json_extract(strategic_insights, '$.matchup_context.sos_pct') as sos,
            json_extract(strategic_insights, '$.dfs_efficiency.fanduel_value') as value_rating,
            json_extract(strategic_insights, '$.projection_confidence.level') as confidence
        FROM projections 
        WHERE week = {week}
        AND source = 'DraftSharks'
        AND json_extract(strategic_insights, '$.projection_range.ceiling') IS NOT NULL
        AND (json_extract(strategic_insights, '$.projection_range.ceiling') - projected_points) >= {min_ceiling_upside}
        {position_filter}
        ORDER BY ceiling_upside DESC
        """
        
        return self.storage.analytical_query(query)
    
    def get_floor_safe_plays(self, week: int, position: str = None, max_floor_risk: float = 3.0) -> pd.DataFrame:
        """Find players with minimal downside risk"""
        position_filter = f"AND position = '{position}'" if position else ""
        
        query = f"""
        SELECT 
            player_name,
            position,
            team,
            projected_points,
            json_extract(strategic_insights, '$.projection_range.floor') as floor,
            (projected_points - json_extract(strategic_insights, '$.projection_range.floor')) as floor_risk,
            json_extract(strategic_insights, '$.projection_confidence.level') as confidence,
            json_extract(strategic_insights, '$.projection_confidence.range_width') as range_width
        FROM projections 
        WHERE week = {week}
        AND source = 'DraftSharks'
        AND json_extract(strategic_insights, '$.projection_range.floor') IS NOT NULL
        AND (projected_points - json_extract(strategic_insights, '$.projection_range.floor')) <= {max_floor_risk}
        {position_filter}
        ORDER BY floor_risk ASC, projected_points DESC
        """
        
        return self.storage.analytical_query(query)
    
    def get_cross_source_consensus(self, week: int, position: str = None) -> pd.DataFrame:
        """Find players with consensus across multiple projection sources"""
        position_filter = f"AND p1.position = '{position}'" if position else ""
        
        query = f"""
        WITH player_projections AS (
            SELECT 
                player_name,
                position,
                team,
                source,
                projected_points
            FROM projections 
            WHERE week = {week}
        )
        SELECT 
            p.player_name,
            p.position,
            p.team,
            COUNT(p.source) as source_count,
            AVG(p.projected_points) as consensus_projection,
            STDDEV(p.projected_points) as projection_variance,
            MIN(p.projected_points) as min_projection,
            MAX(p.projected_points) as max_projection
        FROM player_projections p
        WHERE 1=1 {position_filter}
        GROUP BY p.player_name, p.position, p.team
        HAVING COUNT(p.source) >= 2
        ORDER BY source_count DESC, projection_variance ASC, consensus_projection DESC
        """
        
        return self.storage.analytical_query(query)
    
    def get_dfs_value_plays(self, week: int, position: str = None, min_value_rating: float = 2.0) -> pd.DataFrame:
        """Find DFS value plays with good salary efficiency"""
        position_filter = f"AND position = '{position}'" if position else ""
        
        query = f"""
        SELECT 
            player_name,
            position,
            team,
            projected_points,
            json_extract(strategic_insights, '$.dfs_efficiency.fanduel_salary') as fd_salary,
            json_extract(strategic_insights, '$.dfs_efficiency.fanduel_value') as fd_value,
            json_extract(strategic_insights, '$.projection_range.ceiling') as ceiling,
            json_extract(strategic_insights, '$.matchup_context.sos_pct') as sos
        FROM projections 
        WHERE week = {week}
        AND source = 'DraftSharks'
        AND json_extract(strategic_insights, '$.dfs_efficiency.fanduel_value') IS NOT NULL
        AND CAST(json_extract(strategic_insights, '$.dfs_efficiency.fanduel_value') AS FLOAT) <= {min_value_rating}
        {position_filter}
        ORDER BY CAST(json_extract(strategic_insights, '$.dfs_efficiency.fanduel_value') AS FLOAT) ASC
        """
        
        return self.storage.analytical_query(query)
