"""
Start/Sit Report Generator

Generates comprehensive HTML reports for start/sit accuracy analysis.
Uses Jinja2 templates following established patterns in the codebase.
"""

import pandas as pd
from typing import Dict, List, Optional, Any
from datetime import datetime
from jinja2 import Environment, FileSystemLoader
from .start_sit_accuracy_analyzer import (
    StartSitAccuracyAnalyzer, 
    LeagueStartSitIntelligence, 
    TeamStartSitPerformance,
    ProjectionSourceComparison
)

class StartSitReportGenerator:
    """
    Generates HTML reports for start/sit accuracy analysis using Jinja2 templates.
    Follows established patterns in the codebase for consistency.
    """
    
    def __init__(self, template_path: str = "templates"):
        """
        Initialize the report generator.
        
        Args:
            template_path: Path to Jinja2 templates
        """
        self.env = Environment(loader=FileSystemLoader(template_path))
        self.template = self.env.get_template('start_sit_cheatsheet.html')
    
    def generate_team_start_sit_report(
        self, 
        team_performance: TeamStartSitPerformance,
        league_intelligence: LeagueStartSitIntelligence
    ) -> str:
        """
        Generate HTML report for a single team's start/sit performance.
        
        Args:
            team_performance: Team's start/sit performance data
            league_intelligence: League-wide context for comparison
            
        Returns:
            HTML string with team-specific start/sit analysis
        """
        # Calculate team's league ranking
        team_ranking = self._calculate_team_ranking(
            team_performance, league_intelligence.team_performances, 'lineup_efficiency'
        )
        
        return self.template.render(
            week=team_performance.week,
            analysis_type=f"{team_performance.team_name} Analysis",
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            team_analysis=team_performance,
            team_ranking=team_ranking,
            league_intelligence=league_intelligence
        )
    
    def generate_league_start_sit_report(
        self, 
        league_intelligence: LeagueStartSitIntelligence,
        projection_comparisons: Optional[List[ProjectionSourceComparison]] = None
    ) -> str:
        """
        Generate comprehensive league-wide start/sit report.
        
        Args:
            league_intelligence: League-wide start/sit intelligence
            projection_comparisons: Optional projection source comparisons
            
        Returns:
            HTML string with league-wide analysis
        """
        # Sort teams by lineup efficiency for rankings
        sorted_teams = sorted(
            league_intelligence.team_performances, 
            key=lambda x: x.lineup_efficiency, 
            reverse=True
        )
        
        # Add grades and colors for display
        for team in sorted_teams:
            team.grade = self._calculate_grade(team.lineup_efficiency)
            team.grade_color = self._get_grade_color(team.grade)
        
        # Sort position trends by difficulty
        sorted_position_trends = []
        if league_intelligence.position_trends:
            sorted_position_trends = sorted(
                league_intelligence.position_trends.items(),
                key=lambda x: x[1]['avg_points_left_on_bench'],
                reverse=True
            )
            
            # Add difficulty levels
            for pos, trends in sorted_position_trends:
                trends['difficulty_level'] = self._get_difficulty_level(trends['avg_points_left_on_bench'])
        
        return self.template.render(
            week=league_intelligence.week,
            analysis_type="League-Wide Analysis",
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            team_analysis=None,  # This signals league-wide mode in template
            league_intelligence=league_intelligence,
            sorted_teams=sorted_teams,
            sorted_position_trends=sorted_position_trends,
            projection_comparisons=projection_comparisons
        )
    
    def _calculate_team_ranking(
        self, 
        target_team: TeamStartSitPerformance, 
        all_teams: List[TeamStartSitPerformance],
        metric: str,
        reverse: bool = False
    ) -> int:
        """Calculate team's ranking for a specific metric."""
        sorted_teams = sorted(all_teams, key=lambda x: getattr(x, metric), reverse=not reverse)
        for i, team in enumerate(sorted_teams, 1):
            if team.team_name == target_team.team_name:
                return i
        return len(all_teams)  # Default to last place if not found
    
    def _calculate_grade(self, efficiency: float) -> str:
        """Calculate letter grade based on lineup efficiency."""
        if efficiency >= 95:
            return "A+"
        elif efficiency >= 90:
            return "A"
        elif efficiency >= 85:
            return "B+"
        elif efficiency >= 80:
            return "B"
        elif efficiency >= 75:
            return "C+"
        elif efficiency >= 70:
            return "C"
        elif efficiency >= 65:
            return "D+"
        elif efficiency >= 60:
            return "D"
        else:
            return "F"
    
    def _get_grade_color(self, grade: str) -> str:
        """Get color for grade display."""
        if grade.startswith('A'):
            return '#28a745'  # Green
        elif grade.startswith('B'):
            return '#17a2b8'  # Blue
        elif grade.startswith('C'):
            return '#ffc107'  # Yellow
        elif grade.startswith('D'):
            return '#fd7e14'  # Orange
        else:
            return '#dc3545'  # Red
    
    def _get_difficulty_level(self, points_left: float) -> str:
        """Get difficulty level description based on points left on bench."""
        if points_left >= 5.0:
            return "🔴 Very Hard"
        elif points_left >= 3.0:
            return "🟠 Hard"
        elif points_left >= 1.5:
            return "🟡 Moderate"
        elif points_left >= 0.5:
            return "🟢 Easy"
        else:
            return "✅ Very Easy"