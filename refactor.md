# Refactoring Plan

This document tracks the refactoring tasks for the Fantasy AI project.

## `cli.py` Refactoring

- [x] **Move all commands to `src/commands` directory.**
    - [x] `waiver` command moved.
    - [x] `startsit` command.
    - [x] `claude` command.
    - [x] `ai-analyze` command.
    - [x] `pre-waiver` command.
    - [x] `post-waiver` command.
    - [x] `pre-game` command.
    - [x] `quick` command.
    - [x] `status` command.
    - [x] `email` command.
    - [x] `postweek` command.
    - [x] `capture` command.
    - [x] `capture-db` command.
    - [x] `stream-import` command.
- [x] **Create a command registry to automatically discover and register commands.**
- [x] **Simplify `cli.py` to only handle argument parsing and command dispatching.**

## Command Consolidation

- [ ] **Consolidate AI-related commands.**
    - [ ] Deprecate `--ai` flags on timeline commands.
    - [ ] Deprecate the `claude` command.
    - [ ] Use `ai-analyze` as the primary AI command.
- [ ] **Consolidate projection capture commands.**
    - [ ] Deprecate the `capture` command.
    - [ ] Merge functionality into `capture-db` with a format flag.

## General Code Quality (from TODO-2.md)

- [ ] Add comprehensive test coverage for AI integration modules.
- [ ] Break up massive methods (400+ lines) into focused functions.
- [ ] Split large files (automation scripts, analyzers) into smaller modules.
- [ ] Add proper error handling and input validation.
- [ ] Remove code duplication and improve maintainability.
- [ ] Unit tests for AI analysis logic and SOS integration.
- [ ] Integration tests for webhook and email automation.
- [ ] Mock data for consistent testing of streaming recommendations.
- [ ] Performance tests for large projection datasets.
- [ ] Extract common patterns into reusable utilities.
- [ ] Separate concerns (analysis, formatting, communication).
- [ ] Improve error messages and logging.
- [ ] Add type hints and documentation.
