#!/usr/bin/env python3
"""
Fantasy AI Command Line Interface
Unified CLI combining timeline-based AI analysis with enhanced DuckDB projection storage

Key Features:
- Timeline-based workflow (pre-waiver, post-waiver, pre-game)
- Enhanced projection capture and storage with DuckDB analytics
- AI-powered strategic analysis with Claude/<PERSON> integration
- Professional waiver wire and start/sit analysis
- Post-week projection accuracy analysis
"""

import argparse
import os
import sys
import webbrowser
from datetime import datetime
from src.fantasy_manager import FantasyManager
from src.claude_analyzer import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.commands.waiver import Wai<PERSON><PERSON>ommand
from src.commands.startsit import StartSitCommand
from src.commands.claude import <PERSON><PERSON>ommand
from src.commands.ai_analyze import AiAnalyzeCommand
from src.commands.pre_waiver import Pre<PERSON>ai<PERSON><PERSON>ommand
from src.commands.post_waiver import <PERSON><PERSON>aiverCommand
from src.commands.pre_game import PreGameCommand
from src.commands.quick import QuickCommand
from src.commands.status import StatusCommand
from src.commands.email import EmailCommand
from src.commands.postweek import Post<PERSON><PERSON><PERSON>mand
from src.commands.capture import CaptureCommand
from src.commands.capture_db import CaptureDbCommand
from src.commands.stream_import import <PERSON><PERSON><PERSON>rt<PERSON>ommand
from src.commands.start_sit_analysis import StartSitAnalysisCommand
from jinja2 import Environment, FileSystemLoader
import pandas as pd

def print_banner():
    """Print a nice banner for the CLI"""
    print("""
☘ ═══════════════════════════════════════════════════════════════════
   FantasyAI - AI-Powered Fantasy Football Analysis & Strategy
   Timeline-based workflow with enhanced projection analytics  
═══════════════════════════════════════════════════════════════════ ☘
""")

def add_common_args(parser):
    """Add common arguments to subcommands"""
    # These arguments are now added to the main parser directly
    pass

def main():
    print_banner()
    
    # Create a parent parser with common arguments
    parent_parser = argparse.ArgumentParser(add_help=False)
    parent_parser.add_argument("--week", type=int, help="NFL week number (defaults to current week)")
    parent_parser.add_argument("--open", action="store_true", help="Open generated HTML file in browser")
    parent_parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")
    
    parser = argparse.ArgumentParser(
        description="Fantasy AI CLI - AI-powered fantasy football analysis",
        formatter_class=argparse.RawDescriptionHelpFormatter,
    )

    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Command registry
    command_registry = {
        "waiver": WaiverCommand(),
        "startsit": StartSitCommand(),
        "claude": ClaudeCommand(),
        "ai-analyze": AiAnalyzeCommand(),
        "pre-waiver": PreWaiverCommand(),
        "post-waiver": PostWaiverCommand(),
        "pre-game": PreGameCommand(),
        "quick": QuickCommand(),
        "status": StatusCommand(),
        "email": EmailCommand(),
        "postweek": PostweekCommand(),
        "capture": CaptureCommand(),
        "capture-db": CaptureDbCommand(),
        "stream-import": StreamImportCommand(),
        "start-sit-analysis": StartSitAnalysisCommand(),
    }

    # Register commands
    for command_name, command_instance in command_registry.items():
        cmd_parser = subparsers.add_parser(command_name, parents=[parent_parser])
        command_instance.add_arguments(cmd_parser)

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    try:
        # Initialize Fantasy Manager for commands that need it
        fm = None
        # Commands that do not require fm initialization
        no_fm_commands = ["capture-db", "stream-import"]
        
        if args.command not in no_fm_commands:
            if args.command != "status" or getattr(args, 'verbose', False):
                print("⏳ Initializing Fantasy Manager...")
            fm = FantasyManager()
            fm.league.refresh() # Refresh league data to ensure latest roster
            if args.command != "status":
                print(f"🏈 League: {fm.league.settings.name} | Year: {fm.year} | Week: {fm.league.current_week}")
        
        # Execute commands
        if args.command in command_registry:
            command_instance = command_registry[args.command]
            if args.command in no_fm_commands:
                command_instance.handle(args)
            else:
                command_instance.handle(args, fm=fm)

    except Exception as e:
        print(f"❌ Error: {e}")
        if getattr(args, 'verbose', False):
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
