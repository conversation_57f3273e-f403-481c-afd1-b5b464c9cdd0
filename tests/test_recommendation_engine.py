import unittest
import pandas as pd
from src.recommendation_engine import RecommendationEngine

class TestRecommendationEngine(unittest.TestCase):

    def setUp(self):
        self.recommendation_engine = RecommendationEngine()

    def test_get_enhanced_roster_recommendation_start(self):
        player_data = {
            'name': 'Test Player',
            'position': 'QB',
            'is_starting': True,
            'bye_week': 2,
            'injury_status': 'ACTIVE',
            'injury_status_full': 'Active',
            'external_proj': 25,
            'projected_points': 20,
            'spread': -7,
            'implied_total': 28,
            'game_script': 'high_scoring',
            'season_rank': 10
        }
        player_row = pd.Series(player_data)

        recommendation = self.recommendation_engine.get_enhanced_roster_recommendation(player_row, 1)

        self.assertIn('START', recommendation)

    def test_get_enhanced_roster_recommendation_bench(self):
        player_data = {
            'name': 'Test Player',
            'position': 'QB',
            'is_starting': False,
            'bye_week': 2,
            'injury_status': 'ACTIVE',
            'injury_status_full': 'Active',
            'external_proj': 15,
            'projected_points': 12,
            'spread': 3,
            'implied_total': 22,
            'game_script': 'average_game',
            'season_rank': 30
        }
        player_row = pd.Series(player_data)

        recommendation = self.recommendation_engine.get_enhanced_roster_recommendation(player_row, 1)

        self.assertIn('BENCH', recommendation)

    def test_get_enhanced_roster_recommendation_bye(self):
        player_data = {
            'name': 'Test Player',
            'position': 'QB',
            'is_starting': True,
            'bye_week': 1,
            'injury_status': 'ACTIVE',
            'injury_status_full': 'Active',
            'external_proj': 25,
            'projected_points': 20,
            'spread': -7,
            'implied_total': 28,
            'game_script': 'high_scoring',
            'season_rank': 10
        }
        player_row = pd.Series(player_data)

        recommendation = self.recommendation_engine.get_enhanced_roster_recommendation(player_row, 1)

        self.assertIn('BYE', recommendation)

    def test_get_enhanced_roster_recommendation_injured(self):
        player_data = {
            'name': 'Test Player',
            'position': 'QB',
            'is_starting': True,
            'bye_week': 2,
            'injury_status': 'OUT',
            'injury_status_full': 'Out for season',
            'external_proj': 25,
            'projected_points': 20,
            'spread': -7,
            'implied_total': 28,
            'game_script': 'high_scoring',
            'season_rank': 10
        }
        player_row = pd.Series(player_data)

        recommendation = self.recommendation_engine.get_enhanced_roster_recommendation(player_row, 1)

        self.assertIn('INJURED', recommendation)

if __name__ == '__main__':
    unittest.main()
