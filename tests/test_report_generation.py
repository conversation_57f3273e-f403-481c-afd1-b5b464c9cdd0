#!/usr/bin/env python3
"""Test the HTML report generation functionality"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.fantasy_manager import FantasyManager
import os

def test_html_report_generation():
    """
    Tests the generate_weekly_html_report method.
    """


    fm = FantasyManager()

    print("--- Testing HTML Report Generation ---")
    html_report = fm.generate_weekly_html_report()

    if html_report:
        report_path = "weekly_report.html"
        with open(report_path, "w") as f:
            f.write(html_report)

        print(f"Successfully generated HTML report: {os.path.abspath(report_path)}")
        print("You can open this file in your browser to view the report.")
    else:
        print("Could not generate HTML report.")

    print("\n--- Test Complete ---")

if __name__ == "__main__":
    test_html_report_generation()
