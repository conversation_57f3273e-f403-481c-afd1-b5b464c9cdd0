#!/usr/bin/env python3
"""Test the new waiver wire functionality"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.fantasy_manager import FantasyManager

def test_waiver_analysis():
    fm = FantasyManager()

    print("Testing waiver wire analysis...")

    # Test position rankings
    print("\n=== Top 10 Available RBs ===")
    waiver_df = fm.get_waiver_wire_analysis(size=100)
    if not waiver_df.empty:
        rb_rankings = waiver_df[waiver_df['position'] == 'RB']
        if not rb_rankings.empty:
            print(rb_rankings.head(10))
        else:
            print("No RB data available")

    print("\n=== Top 10 Available WRs ===")
    if not waiver_df.empty:
        wr_rankings = waiver_df[waiver_df['position'] == 'WR']
        if not wr_rankings.empty:
            print(wr_rankings.head(10))
        else:
            print("No WR data available")

    # Test full waiver analysis (smaller sample)
    print("\n=== High Priority Waiver Targets ===")
    waiver_df = fm.get_waiver_wire_analysis(size=50)
    if not waiver_df.empty:
        high_priority = waiver_df[
            waiver_df['recommendation'].str.contains('HIGH PRIORITY', na=False)
        ]
        if not high_priority.empty:
            print(high_priority[['name', 'position', 'projected_points', 'percent_owned', 'recommendation']])
        else:
            print("No high priority targets found")

        # Show some medium priority too
        medium_priority = waiver_df[
            waiver_df['recommendation'].str.contains('MEDIUM PRIORITY', na=False)
        ].head(5)
        if not medium_priority.empty:
            print("\n=== Medium Priority Targets ===")
            print(medium_priority[['name', 'position', 'projected_points', 'percent_owned', 'recommendation']])
    else:
        print("No waiver data available")

    

if __name__ == "__main__":
    test_waiver_analysis()
