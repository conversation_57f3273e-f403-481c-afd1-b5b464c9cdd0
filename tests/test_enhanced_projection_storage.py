"""
Tests for Enhanced Projection Storage System
Focus on core functionality and strategic insights extraction.
"""

import pytest
import tempfile
import os
import pandas as pd
from unittest.mock import Mock, patch
from src.enhanced_projection_storage import (
    StrategicProjectionRecord,
    DraftSharksProjections, 
    EnhancedProjectionStorage,
    StrategicAnalytics,
    ProjectionValidator,
    ValidationError
)

class TestStrategicProjectionRecord:
    """Test the strategic projection record data structure"""
    
    def test_draftsharks_strategic_insights_extraction(self):
        """Test that DraftSharks data extracts strategic insights correctly"""
        raw_data = {
            'Player': '<PERSON>',
            'Pos.': 'RB',
            'Team': 'SF',
            'Proj': 20.0,
            'Floor': 12.2,
            'Ceil': 27.0,
            'Cons.': 19.7,
            'SOS': '-3%',
            'Rush Yds': 74.2,
            'Rush TDs': 0.64,
            'FanDuel $': 8100,
            '$ / Point': 462.9
        }
        
        record = StrategicProjectionRecord(
            week=1,
            year=2025,
            player_id=None,
            player_name='<PERSON>',
            position='RB',
            team='SF',
            source='DraftSharks',
            projected_points=20.0,
            raw_source_data=raw_data
        )
        
        # Test strategic insights extraction
        insights = record.strategic_insights
        
        assert insights['projection_range']['floor'] == 12.2
        assert insights['projection_range']['ceiling'] == 27.0
        assert insights['projection_range']['range_width'] == 14.8
        assert insights['matchup_context']['sos_pct'] == '-3%'
        assert insights['dfs_efficiency']['fanduel_salary'] == 8100
        assert insights['stat_projections']['rush_yards'] == 74.2
        
        # Test convenience methods
        assert record.get_ceiling_upside() == 7.0  # 27 - 20
        assert abs(record.get_floor_downside() - 7.8) < 0.01  # 20 - 12.2 (floating point precision)
    
    def test_confidence_calculation(self):
        """Test projection confidence calculation"""
        # High confidence (narrow range)
        high_conf_data = {'Floor': 18.0, 'Ceil': 22.0}
        record_high = StrategicProjectionRecord(
            week=1, year=2025, player_id=None, player_name='Test',
            position='RB', team='SF', source='DraftSharks',
            projected_points=20.0, raw_source_data=high_conf_data
        )
        assert record_high.strategic_insights['projection_confidence']['level'] == 'high'
        
        # Low confidence (wide range) 
        low_conf_data = {'Floor': 8.0, 'Ceil': 25.0}
        record_low = StrategicProjectionRecord(
            week=1, year=2025, player_id=None, player_name='Test',
            position='RB', team='SF', source='DraftSharks', 
            projected_points=16.0, raw_source_data=low_conf_data
        )
        assert record_low.strategic_insights['projection_confidence']['level'] == 'low'

class TestDraftSharksIntegration:
    """Test DraftSharks CSV loading and processing"""
    
    def test_csv_loading_with_sample_data(self):
        """Test loading DraftSharks data from CSV"""
        # Create temp CSV with sample data
        sample_data = """Player,Pos.,Team,Matchup,SOS,Rush Yds,Rush TDs,Rec,Rec Yds,Rec TDs,FanDuel $,$ / Point,DraftKings $,$ / Point ,Floor,Cons.,Proj,Ceil,3D Proj.
Christian McCaffrey,RB,SF,@SEA,-3%,74.2,0.64,4.2,34.1,0.22,8100,462.9,7300,372.4,12.2,19.7,20,27,19.8
Ja'Marr Chase,WR,CIN,@CLE,11%,0,0,6.8,87.5,0.69,9200,561,8100,409.1,11.4,20.3,19.7,27,19.6"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(sample_data)
            csv_path = f.name
        
        try:
            # Test loading
            ds = DraftSharksProjections(csv_path)
            projections = ds.fetch_projections(week=1)
            
            assert len(projections) == 2
            
            # Test first player (CMC)
            cmc = projections[0]
            assert cmc.player_name == 'Christian McCaffrey'
            assert cmc.position == 'RB'
            assert cmc.projected_points == 20.0
            assert cmc.source == 'DraftSharks'
            
            # Test strategic insights
            assert cmc.get_ceiling_upside() == 7.0
            assert cmc.strategic_insights['dfs_efficiency']['fanduel_salary'] == 8100
            
        finally:
            os.unlink(csv_path)
    
    def test_missing_csv_handling(self):
        """Test graceful handling of missing CSV file"""
        ds = DraftSharksProjections('/nonexistent/path.csv')
        projections = ds.fetch_projections(week=1)
        assert projections == []

class TestProjectionValidator:
    """Test data validation and error handling"""
    
    def test_valid_projection_passes(self):
        """Test that valid projections pass validation"""
        valid_proj = StrategicProjectionRecord(
            week=1, year=2025, player_id='123', player_name='Josh Allen',
            position='QB', team='BUF', source='DraftSharks',
            projected_points=25.5, raw_source_data={}
        )
        
        validator = ProjectionValidator()
        result = validator.validate_projections([valid_proj])
        
        assert len(result) == 1
        assert result[0].player_name == 'Josh Allen'
    
    def test_validation_errors_caught(self):
        """Test that validation errors are caught and logged"""
        invalid_proj = StrategicProjectionRecord(
            week=1, year=2025, player_id='123', player_name='',  # Empty name
            position='QB', team='BUF', source='DraftSharks',
            projected_points=25.5, raw_source_data={}
        )
        
        validator = ProjectionValidator()
        result = validator.validate_projections([invalid_proj])
        
        # Should be filtered out
        assert len(result) == 0
    
    def test_projection_range_validation(self):
        """Test projection range validation by position"""
        # QB with unreasonable projection (too high)
        invalid_qb = StrategicProjectionRecord(
            week=1, year=2025, player_id='123', player_name='Test QB',
            position='QB', team='BUF', source='DraftSharks',
            projected_points=99.0,  # Way too high
            raw_source_data={}
        )
        
        validator = ProjectionValidator()
        result = validator.validate_projections([invalid_qb])
        
        # Should be filtered out due to unreasonable projection
        assert len(result) == 0

@pytest.fixture
def temp_storage():
    """Create temporary storage for testing"""
    with tempfile.TemporaryDirectory() as temp_dir:
        db_path = os.path.join(temp_dir, 'test.duckdb')
        storage = EnhancedProjectionStorage(db_path)
        yield storage
        storage.close()

class TestEnhancedProjectionStorage:
    """Test core storage functionality"""
    
    def test_database_initialization(self, temp_storage):
        """Test that database initializes correctly"""
        # Should not raise any errors
        assert temp_storage.conn is not None
        
        # Test that tables were created
        result = temp_storage.conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()
        table_names = [row[0] for row in result]
        assert 'projections' in table_names
        assert 'capture_metadata' in table_names
    
    def test_projection_storage_and_retrieval(self, temp_storage):
        """Test storing and retrieving projections"""
        # Create test projection
        test_proj = StrategicProjectionRecord(
            week=1, year=2025, player_id='123', player_name='Test Player',
            position='RB', team='SF', source='DraftSharks',
            projected_points=15.5,
            raw_source_data={'Floor': 10.0, 'Ceil': 20.0}
        )
        
        # Store projection  
        count = temp_storage._store_projections([test_proj])
        assert count == 1
        
        # Retrieve projection
        df = temp_storage.get_projections(week=1, sources=['DraftSharks'])
        
        assert len(df) == 1
        assert df.iloc[0]['player_name'] == 'Test Player'
        assert df.iloc[0]['projected_points'] == 15.5

class TestStrategicAnalytics:
    """Test strategic analytics queries"""
    
    def test_ceiling_plays_query(self, temp_storage):
        """Test ceiling plays analysis"""
        # Add test data with ceiling information
        test_projections = [
            StrategicProjectionRecord(
                week=1, year=2025, player_id='1', player_name='High Ceiling Player',
                position='WR', team='KC', source='DraftSharks', projected_points=15.0,
                raw_source_data={'Floor': 8.0, 'Ceil': 25.0}  # High ceiling upside
            ),
            StrategicProjectionRecord(
                week=1, year=2025, player_id='2', player_name='Safe Floor Player', 
                position='WR', team='SF', source='DraftSharks', projected_points=14.0,
                raw_source_data={'Floor': 12.0, 'Ceil': 16.0}  # Low ceiling upside
            )
        ]
        
        temp_storage._store_projections(test_projections)
        
        # Test ceiling analysis
        analytics = StrategicAnalytics(temp_storage)
        ceiling_plays = analytics.get_ceiling_plays(week=1, min_ceiling_upside=8.0)
        
        # Should only return the high ceiling player
        assert len(ceiling_plays) == 1
        assert ceiling_plays.iloc[0]['player_name'] == 'High Ceiling Player'
        assert ceiling_plays.iloc[0]['ceiling_upside'] == 10.0  # 25 - 15
    
    def test_cross_source_consensus(self, temp_storage):
        """Test cross-source consensus analysis"""
        # Add same player from multiple sources
        test_projections = [
            StrategicProjectionRecord(
                week=1, year=2025, player_id='1', player_name='Consensus Player',
                position='RB', team='SF', source='DraftSharks', projected_points=18.0,
                raw_source_data={}
            ),
            StrategicProjectionRecord(
                week=1, year=2025, player_id='1', player_name='Consensus Player',
                position='RB', team='SF', source='FanDuel', projected_points=17.5,
                raw_source_data={}
            )
        ]
        
        temp_storage._store_projections(test_projections)
        
        # Test consensus analysis
        analytics = StrategicAnalytics(temp_storage)
        consensus = analytics.get_cross_source_consensus(week=1)
        
        assert len(consensus) == 1
        assert consensus.iloc[0]['player_name'] == 'Consensus Player'
        assert consensus.iloc[0]['source_count'] == 2
        assert abs(consensus.iloc[0]['consensus_projection'] - 17.75) < 0.01

# Test runner
if __name__ == "__main__":
    pytest.main([__file__, "-v"])