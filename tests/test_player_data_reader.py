import unittest
from unittest.mock import MagicMock, patch
import pandas as pd
from src.player_data_reader import PlayerDataReader

class TestPlayerDataReader(unittest.TestCase):

    def setUp(self):
        self.mock_league = MagicMock()
        self.player_data_reader = PlayerDataReader(self.mock_league, 'test_api_key')

    def test_get_player_details(self):
        mock_player = MagicMock()
        mock_player.playerId = 123
        mock_player.name = 'Test Player'
        mock_player.position = 'QB'
        mock_player.proTeam = 'TEST'
        mock_player.projected_avg_points = 20.5
        mock_player.injuryStatus = 'ACTIVE'
        mock_player.injuryStatusFull = 'Active'
        mock_player.byeWeek = 5
        mock_player.percent_owned = 75.5
        mock_player.percent_started = 50.5
        mock_player.acquisitionType = 'DRAFT'
        mock_player.pro_opponent = 'OPP'

        player_details = self.player_data_reader.get_player_details(mock_player)

        self.assertEqual(player_details['player_id'], 123)
        self.assertEqual(player_details['name'], 'Test Player')
        self.assertEqual(player_details['position'], 'QB')
        self.assertEqual(player_details['team'], 'TEST')
        self.assertEqual(player_details['projected_points'], 20.5)

    @patch('src.player_data_reader.ProjectionMatcher')
    @patch('src.player_data_reader.VegasDataFetcher')
    @patch('src.player_data_reader.WinWithOddsProjections')
    def test_get_roster_data(self, mock_wwo, mock_vegas, mock_projection_matcher):
        mock_team = MagicMock()
        mock_player = MagicMock()
        mock_player.playerId = 123
        mock_player.name = 'Test Player'
        mock_player.position = 'QB'
        mock_player.proTeam = 'TEST'
        mock_player.projected_avg_points = 20.5
        mock_player.injuryStatus = 'ACTIVE'
        mock_player.injuryStatusFull = 'Active'
        mock_player.byeWeek = 5
        mock_player.percent_owned = 75.5
        mock_player.percent_started = 50.5
        mock_player.acquisitionType = 'DRAFT'
        mock_player.pro_opponent = 'OPP'
        mock_player.lineupSlot = 'QB'
        mock_team.roster = [mock_player]

        self.player_data_reader.projection_matcher = mock_projection_matcher.return_value
        self.player_data_reader.vegas_data = mock_vegas.return_value
        self.player_data_reader.wwo_projections = mock_wwo.return_value

        self.player_data_reader.projection_matcher.integrate_projections.return_value = pd.DataFrame([self.player_data_reader.get_player_details(mock_player)])
        self.player_data_reader.vegas_data.get_team_game_context.return_value = {'implied_total': 24, 'spread': -7, 'game_total': 48, 'game_script': 'close'}
        self.player_data_reader.wwo_projections.fetch_season_long_projections.return_value = pd.DataFrame()

        roster_df = self.player_data_reader.get_roster_data(mock_team, 1)

        self.assertEqual(len(roster_df), 1)
        self.assertEqual(roster_df.iloc[0]['name'], 'Test Player')
        self.assertTrue(roster_df.iloc[0]['is_starting'])

if __name__ == '__main__':
    unittest.main()
