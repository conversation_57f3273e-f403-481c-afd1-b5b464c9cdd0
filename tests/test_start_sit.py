#!/usr/bin/env python3
"""Test the new start/sit report functionality"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.fantasy_manager import <PERSON><PERSON>ana<PERSON>

def test_start_sit_report_generation():
    """
    Tests the generate_start_sit_html_report method.
    """
    print("\n--- Testing Start/Sit Report Generation ---")
    try:
        fm = FantasyManager()
        html_report = fm.generate_start_sit_html_report()

        if html_report:
            report_path = "start_sit_report.html"
            with open(report_path, "w") as f:
                f.write(html_report)

            print(f"✅ Successfully generated HTML start/sit report: {os.path.abspath(report_path)}")
            print("You can open this file in your browser to view the report.")
            assert len(html_report) > 1000 # Basic check for non-empty report
        else:
            print("❌ Could not generate HTML start/sit report: Report is empty.")
            assert False, "Start/sit report is empty."

    except Exception as e:
        print(f"❌ An error occurred during start/sit report generation: {e}")
        assert False, f"Exception: {e}"

    print("\n--- Start/Sit Report Test Complete ---")

if __name__ == "__main__":
    test_start_sit_report_generation()
