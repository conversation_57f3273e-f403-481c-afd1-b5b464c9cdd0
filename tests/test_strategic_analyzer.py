#!/usr/bin/env python3
"""
Test Suite for Strategic Analyzer
Following TDD principles to ensure reliability and prevent regressions.
"""

import pytest
import pandas as pd
import sys
import os

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from strategic_analyzer import StrategicAnalyzer, TeamContext, PlayerInsight

class TestStrategicAnalyzer:
    """Test suite for the StrategicAnalyzer class."""
    
    @pytest.fixture
    def analyzer(self):
        """Create a StrategicAnalyzer instance for testing."""
        return StrategicAnalyzer()
    
    @pytest.fixture
    def sample_roster_df(self):
        """Create sample roster data for testing."""
        return pd.DataFrame([
            {'name': '<PERSON>', 'position': 'QB', 'external_proj': 18.5, 'projected_points': 18.0, 'is_starting': True},
            {'name': '<PERSON><PERSON><PERSON><PERSON>', 'position': 'RB', 'external_proj': 15.2, 'projected_points': 15.0, 'is_starting': True},
            {'name': '<PERSON>', 'position': 'RB', 'external_proj': 12.1, 'projected_points': 12.0, 'is_starting': <PERSON>},
            {'name': '<PERSON><PERSON>', 'position': 'RB', 'external_proj': 8.5, 'projected_points': 8.0, 'is_starting': False},
            {'name': 'CeeDee Lamb', 'position': 'WR', 'external_proj': 14.8, 'projected_points': 14.5, 'is_starting': True},
            {'name': '<PERSON> <PERSON>', 'position': 'WR', 'external_proj': 11.2, 'projected_points': 11.0, 'is_starting': True},
            {'name': 'DJ Moore', 'position': 'WR', 'external_proj': 9.1, 'projected_points': 9.0, 'is_starting': True},
            {'name': 'Travis Kelce', 'position': 'TE', 'external_proj': 12.5, 'projected_points': 12.0, 'is_starting': True},
            {'name': 'Justin Tucker', 'position': 'K', 'external_proj': 8.2, 'projected_points': 8.0, 'is_starting': True},
            {'name': 'Bills D/ST', 'position': 'D/ST', 'external_proj': 7.8, 'projected_points': 7.5, 'is_starting': True},
        ])
    
    @pytest.fixture
    def sample_waiver_df(self):
        """Create sample waiver wire data for testing."""
        return pd.DataFrame([
            {'name': 'Jayden Daniels', 'position': 'QB', 'external_proj': 16.2, 'projected_points': 16.0, 'status': 'Available', 'percent_owned': 25.5, 'trend_category': 'neutral', 'season_projection': 250.0, 'dfs_value': 2.4},
            {'name': 'Brock Bowers', 'position': 'TE', 'external_proj': 8.9, 'projected_points': 8.5, 'status': 'Available', 'percent_owned': 15.2, 'trend_category': 'buy_low', 'season_projection': 140.0, 'dfs_value': 2.6},
            {'name': 'DeAndre Hopkins', 'position': 'WR', 'external_proj': 6.1, 'projected_points': 6.0, 'status': 'Available', 'percent_owned': 85.5, 'trend_category': 'sell_high', 'season_projection': 120.0, 'dfs_value': 1.8},
            {'name': 'Chuba Hubbard', 'position': 'RB', 'external_proj': 9.8, 'projected_points': 9.5, 'status': 'Available', 'percent_owned': 35.1, 'trend_category': 'neutral', 'season_projection': 160.0, 'dfs_value': 2.2},
            {'name': 'UNKNOWN', 'position': 'RB', 'external_proj': 5.0, 'projected_points': 5.0, 'status': 'Available', 'percent_owned': 10.0, 'trend_category': 'neutral', 'season_projection': 0, 'dfs_value': 0},
        ])
    
    def test_analyze_team_context_identifies_strengths_and_weaknesses(self, analyzer, sample_roster_df):
        """Test that team context analysis correctly identifies position strengths."""
        team_context = analyzer.analyze_team_context(sample_roster_df)
        
        # QB should be strong (1 elite player)
        assert team_context.position_strengths['QB'] == 'Strong'
        
        # RB should be strong (3 players with good projections)
        assert team_context.position_strengths['RB'] == 'Strong'
        
        # WR should be strong (3 players with decent projections)  
        assert team_context.position_strengths['WR'] == 'Strong'
        
        # TE should be adequate (1 strong player)
        assert team_context.position_strengths['TE'] == 'Adequate'
        
        # K and D/ST should be adequate or strong (streamable positions with decent projections)
        assert team_context.position_strengths['K'] in ['Strong', 'Adequate']  # 8.2 projection is solid for K
        assert team_context.position_strengths['D/ST'] in ['Strong', 'Adequate']  # 7.8 projection is solid for D/ST
    
    def test_analyze_team_context_identifies_trade_assets(self, analyzer, sample_roster_df):
        """Test that team context identifies potential trade assets."""
        team_context = analyzer.analyze_team_context(sample_roster_df)
        
        # Should identify strong positions with depth as trade assets
        # RB has 3 players, top ones could be trade assets
        assert len(team_context.trade_assets) > 0
        assert any('Barkley' in asset or 'Pollard' in asset for asset in team_context.trade_assets)
    
    def test_generate_strategic_insights_returns_valid_insights(self, analyzer, sample_waiver_df, sample_roster_df):
        """Test that strategic insights are generated with proper structure."""
        team_context = analyzer.analyze_team_context(sample_roster_df)
        insights = analyzer.generate_strategic_insights(sample_waiver_df, team_context, sample_roster_df)
        
        # Should return insights for valid players only
        assert len(insights) > 0
        assert all(isinstance(insight, PlayerInsight) for insight in insights)
        
        # Should not include UNKNOWN players
        player_names = [insight.player_name for insight in insights]
        assert 'UNKNOWN' not in player_names
        
        # Should have valid urgency scores
        assert all(1 <= insight.urgency <= 10 for insight in insights)
        
        # Should have valid confidence levels
        valid_confidence = {'high', 'medium', 'low'}
        assert all(insight.confidence in valid_confidence for insight in insights)
    
    def test_calculate_positional_need_score_accurate(self, analyzer):
        """Test that positional need calculation works correctly."""
        # Weak TE position should have high need score
        weak_te_context = TeamContext(
            position_strengths={'TE': 'Weak'},
            bye_week_needs=[],
            injury_concerns=[],
            starter_weaknesses=['TE'],
            depth_issues=['TE'],
            trade_assets=[]
        )
        
        te_need_score = analyzer._calculate_positional_need('TE', weak_te_context)
        assert te_need_score > 80  # Should be high due to weakness + scarcity
        
        # Strong QB position should have low need score
        strong_qb_context = TeamContext(
            position_strengths={'QB': 'Strong'},
            bye_week_needs=[],
            injury_concerns=[],
            starter_weaknesses=[],
            depth_issues=[],
            trade_assets=[]
        )
        
        qb_need_score = analyzer._calculate_positional_need('QB', strong_qb_context)
        assert qb_need_score < 40  # Should be low for strong position
    
    def test_calculate_player_value_vs_roster(self, analyzer, sample_roster_df):
        """Test player value calculation against roster alternatives."""
        # High-value TE against weak TE roster
        weak_te_roster = sample_roster_df[sample_roster_df['position'] != 'TE']  # No TEs
        high_te_player = pd.Series({
            'external_proj': 12.0,
            'projected_points': 12.0,
            'season_projection': 180.0
        })
        
        value_score = analyzer._calculate_player_value(high_te_player, weak_te_roster, 'TE')
        assert value_score > 80  # Should be very valuable with no TE competition
        
        # Low-value QB against strong QB roster
        strong_qb_roster = sample_roster_df  # Has Josh Allen at QB
        weak_qb_player = pd.Series({
            'external_proj': 10.0,
            'projected_points': 10.0,
            'season_projection': 150.0
        })
        
        value_score = analyzer._calculate_player_value(weak_qb_player, strong_qb_roster, 'QB')
        assert value_score < 30  # Should be low value against Josh Allen
    
    def test_calculate_market_timing_considers_ownership_and_trends(self, analyzer):
        """Test market timing calculation for ownership and trends."""
        # Low ownership + buy_low should score high
        high_timing_score = analyzer._calculate_market_timing(ownership=8.0, trend_cat='buy_low')
        assert high_timing_score > 80
        
        # High ownership + sell_high should score low  
        low_timing_score = analyzer._calculate_market_timing(ownership=90.0, trend_cat='sell_high')
        assert low_timing_score < 40
        
        # Medium ownership + neutral should score medium
        med_timing_score = analyzer._calculate_market_timing(ownership=45.0, trend_cat='neutral')
        assert 40 <= med_timing_score <= 80
    
    def test_calculate_upside_potential_considers_projections_and_value(self, analyzer):
        """Test upside calculation based on projections and efficiency."""
        # High projection + high DFS value should score high
        elite_player = pd.Series({
            'external_proj': 18.0,
            'projected_points': 18.0,
            'season_projection': 280.0,
            'dfs_value': 2.7
        })
        
        upside_score = analyzer._calculate_upside_potential(elite_player)
        assert upside_score > 70
        
        # Low projection + low DFS value should score low
        weak_player = pd.Series({
            'external_proj': 4.0,
            'projected_points': 4.0,
            'season_projection': 80.0,
            'dfs_value': 1.5
        })
        
        upside_score = analyzer._calculate_upside_potential(weak_player)
        assert upside_score < 30
    
    def test_enhance_waiver_recommendations_preserves_dataframe_structure(self, analyzer, sample_waiver_df, sample_roster_df):
        """Test that enhancing recommendations preserves DataFrame structure."""
        original_columns = set(sample_waiver_df.columns)
        enhanced_df = analyzer.enhance_waiver_recommendations(sample_waiver_df, sample_roster_df)
        
        # Should preserve all original columns
        assert original_columns.issubset(set(enhanced_df.columns))
        
        # Should add new strategic columns
        new_columns = {'strategic_reasoning', 'confidence', 'urgency', 'insight_type'}
        assert new_columns.issubset(set(enhanced_df.columns))
        
        # Should have same number of rows
        assert len(enhanced_df) == len(sample_waiver_df)
        
        # Should not have null values in new strategic columns for valid players
        valid_players = enhanced_df[enhanced_df['name'].notna() & (enhanced_df['name'] != 'UNKNOWN')]
        assert not valid_players['strategic_reasoning'].isna().any()
        assert not valid_players['confidence'].isna().any()
        assert not valid_players['urgency'].isna().any()
    
    def test_insights_handle_invalid_player_data(self, analyzer, sample_roster_df):
        """Test that insights properly handle invalid or missing player data."""
        invalid_waiver_df = pd.DataFrame([
            {'name': None, 'position': 'QB', 'external_proj': 15.0, 'status': 'Available'},
            {'name': 'UNKNOWN', 'position': 'RB', 'external_proj': 10.0, 'status': 'Available'},
            {'name': '', 'position': 'WR', 'external_proj': 8.0, 'status': 'Available'},
            {'name': 'Valid Player', 'position': 'TE', 'external_proj': 12.0, 'status': 'Available', 'percent_owned': 20.0, 'season_projection': 150.0}
        ])
        
        team_context = analyzer.analyze_team_context(sample_roster_df)
        insights = analyzer.generate_strategic_insights(invalid_waiver_df, team_context, sample_roster_df)
        
        # Should only return insights for valid players
        assert len(insights) == 1  # Only 'Valid Player' should generate insights
        assert insights[0].player_name == 'Valid Player'
    
    def test_insights_prioritize_team_needs(self, analyzer, sample_roster_df):
        """Test that insights properly prioritize based on team needs."""
        # Create roster without TE (critical need)
        no_te_roster = sample_roster_df[sample_roster_df['position'] != 'TE'].copy()
        
        waiver_with_te = pd.DataFrame([
            {'name': 'Elite TE', 'position': 'TE', 'external_proj': 12.0, 'status': 'Available', 'percent_owned': 15.0, 'trend_category': 'neutral', 'season_projection': 180.0, 'dfs_value': 2.5},
            {'name': 'Elite QB', 'position': 'QB', 'external_proj': 20.0, 'status': 'Available', 'percent_owned': 15.0, 'trend_category': 'neutral', 'season_projection': 320.0, 'dfs_value': 2.8}
        ])
        
        team_context = analyzer.analyze_team_context(no_te_roster)
        insights = analyzer.generate_strategic_insights(waiver_with_te, team_context, no_te_roster)
        
        # TE should be higher priority despite lower projection due to team need
        te_insight = next((i for i in insights if i.position == 'TE'), None)
        qb_insight = next((i for i in insights if i.position == 'QB'), None)
        
        assert te_insight is not None
        assert qb_insight is not None
        assert te_insight.urgency >= qb_insight.urgency  # TE should be higher or equal urgency
    
    def test_confidence_levels_match_insight_quality(self, analyzer, sample_waiver_df, sample_roster_df):
        """Test that confidence levels appropriately match insight quality."""
        team_context = analyzer.analyze_team_context(sample_roster_df)
        insights = analyzer.generate_strategic_insights(sample_waiver_df, team_context, sample_roster_df)
        
        # High urgency insights should generally have high/medium confidence
        high_urgency_insights = [i for i in insights if i.urgency >= 7]
        if high_urgency_insights:
            high_confidence_ratio = sum(1 for i in high_urgency_insights if i.confidence in ['high', 'medium']) / len(high_urgency_insights)
            assert high_confidence_ratio >= 0.7  # At least 70% should be high/medium confidence
        
        # Low urgency insights can have lower confidence
        low_urgency_insights = [i for i in insights if i.urgency <= 3]
        # This is more flexible as low urgency can still have good reasoning

class TestPlayerInsight:
    """Test the PlayerInsight dataclass."""
    
    def test_player_insight_creation(self):
        """Test PlayerInsight can be created with valid data."""
        insight = PlayerInsight(
            player_name="Josh Allen",
            position="QB", 
            insight_type="must_add",
            confidence="high",
            reasoning="Elite projection, team need",
            action="ADD immediately",
            urgency=9
        )
        
        assert insight.player_name == "Josh Allen"
        assert insight.urgency == 9
        assert insight.confidence == "high"

class TestTeamContext:
    """Test the TeamContext dataclass."""
    
    def test_team_context_creation(self):
        """Test TeamContext can be created with valid data."""
        context = TeamContext(
            position_strengths={'QB': 'Strong', 'RB': 'Weak'},
            bye_week_needs=['RB'],
            injury_concerns=['WR1'],
            starter_weaknesses=['TE'],
            depth_issues=['RB', 'TE'],
            trade_assets=['WR1', 'WR2']
        )
        
        assert context.position_strengths['QB'] == 'Strong'
        assert 'RB' in context.depth_issues
        assert len(context.trade_assets) == 2

# Integration Tests
class TestStrategicAnalyzerIntegration:
    """Integration tests for the strategic analyzer."""
    
    def test_full_analysis_pipeline_produces_actionable_insights(self):
        """Test the complete analysis pipeline produces meaningful results."""
        analyzer = StrategicAnalyzer()
        
        # Create realistic test data
        roster_df = pd.DataFrame([
            {'name': 'Josh Allen', 'position': 'QB', 'external_proj': 18.5, 'projected_points': 18.0, 'is_starting': True},
            {'name': 'Bad RB', 'position': 'RB', 'external_proj': 6.0, 'projected_points': 6.0, 'is_starting': True},  # Weak starter
            {'name': 'Decent WR', 'position': 'WR', 'external_proj': 10.0, 'projected_points': 10.0, 'is_starting': True},
        ])
        
        waiver_df = pd.DataFrame([
            {'name': 'Elite RB Available', 'position': 'RB', 'external_proj': 14.0, 'status': 'Available', 'percent_owned': 25.0, 'trend_category': 'neutral', 'season_projection': 200.0, 'dfs_value': 2.4}
        ])
        
        enhanced_df = analyzer.enhance_waiver_recommendations(waiver_df, roster_df)
        
        # Should identify RB as high priority due to weak starter
        rb_rec = enhanced_df[enhanced_df['position'] == 'RB'].iloc[0]
        assert rb_rec['urgency'] >= 7  # Should be high urgency
        assert 'ADD' in rb_rec['recommendation'] or '🔥' in rb_rec['recommendation']
        assert len(rb_rec['strategic_reasoning']) > 10  # Should have substantial reasoning