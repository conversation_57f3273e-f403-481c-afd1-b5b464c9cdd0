#!/usr/bin/env python3
"""Test the new lineup and waiver suggestion functionality"""

from src.fantasy_manager import FantasyManager
import pandas as pd

def test_suggestions():
    """
    Tests the generate_lineup_and_waiver_suggestions method.
    """
    fm = FantasyManager()

    print("--- Testing Lineup and Waiver Suggestions ---")
    suggestions = fm.generate_lineup_and_waiver_suggestions()

    if not suggestions:
        print("Could not generate suggestions.")
        return

    print("\n--- Top 10 Waiver Suggestions ---")
    waiver_df = pd.DataFrame(suggestions['waiver_analysis'])
    if not waiver_df.empty:
        print(waiver_df[['name', 'position', 'proTeam', 'external_proj']].to_string(index=False))
    else:
        print("No waiver suggestions available.")

    

    print("\n--- Test Complete ---")

if __name__ == "__main__":
    test_suggestions()
