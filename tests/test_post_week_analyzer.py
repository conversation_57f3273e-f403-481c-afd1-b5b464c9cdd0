"""
Tests for PostWeekAnalyzer

Comprehensive test suite covering projection accuracy analysis,
position-level insights, and week summary generation.
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, MagicMock, patch
from datetime import datetime, timedelta

from src.post_week_analyzer import (
    PostWeekAnalyzer, 
    ProjectionAccuracy, 
    PositionAnalysis, 
    WeekSummary
)

class MockBoxPlayer:
    """Mock BoxPlayer for testing"""
    def __init__(self, name, position, points, projected_points, game_played=100):
        self.name = name
        self.position = position
        self.points = points
        self.projected_points = projected_points
        self.game_played = game_played

class MockMatchup:
    """Mock Matchup for testing"""
    def __init__(self, home_lineup, away_lineup):
        self.home_lineup = home_lineup
        self.away_lineup = away_lineup
        self.home_team = Mock()
        self.away_team = Mock()

@pytest.fixture
def mock_league():
    """Create a mock ESPN League"""
    league = Mock()
    league.current_week = 2
    
    # Mock team for team analysis
    mock_team = Mock()
    mock_team.team_name = "Test Team"
    league.teams = [mock_team]
    
    return league

@pytest.fixture
def analyzer(mock_league):
    """Create PostWeekAnalyzer instance"""
    return PostWeekAnalyzer(mock_league)

@pytest.fixture
def sample_completed_players():
    """Sample player data for a completed week"""
    return [
        MockBoxPlayer("Josh Allen", "QB", 28.5, 24.2),
        MockBoxPlayer("Saquon Barkley", "RB", 15.8, 18.3),
        MockBoxPlayer("Tyreek Hill", "WR", 22.1, 16.9),
        MockBoxPlayer("Travis Kelce", "TE", 8.2, 12.4),
        MockBoxPlayer("Justin Tucker", "K", 9.0, 8.5),
        MockBoxPlayer("49ers D/ST", "D/ST", 12.0, 8.8),
    ]

@pytest.fixture
def sample_incomplete_players():
    """Sample player data with incomplete games"""
    return [
        MockBoxPlayer("Player A", "QB", 0, 20.0, game_played=50),  # Game in progress
        MockBoxPlayer("Player B", "RB", 12.0, 15.0, game_played=100),  # Complete
    ]

@pytest.fixture
def external_projections():
    """Sample external projections"""
    return {
        'FanDuel': pd.DataFrame({
            'name': ['Josh Allen', 'Saquon Barkley', 'Tyreek Hill'],
            'fantasy': [26.1, 19.2, 18.5]
        }),
        'WinWithOdds': pd.DataFrame({
            'name': ['Josh Allen', 'Travis Kelce', 'Justin Tucker'], 
            'projected_points': [25.8, 11.9, 9.2]
        })
    }

class TestPostWeekAnalyzer:
    
    def test_init_creates_analyzer_with_league(self, mock_league):
        """Test analyzer initialization"""
        analyzer = PostWeekAnalyzer(mock_league)
        assert analyzer.league == mock_league
        assert hasattr(analyzer, 'logger')
    
    def test_is_week_complete_returns_true_for_completed_week(self, analyzer, sample_completed_players):
        """Test week completion detection for completed week"""
        mock_matchup = MockMatchup(sample_completed_players[:3], sample_completed_players[3:])
        analyzer.league.box_scores.return_value = [mock_matchup]
        
        assert analyzer.is_week_complete(1) == True
    
    def test_is_week_complete_returns_false_for_incomplete_week(self, analyzer, sample_incomplete_players):
        """Test week completion detection for incomplete week"""
        mock_matchup = MockMatchup(sample_incomplete_players[:1], sample_incomplete_players[1:])
        analyzer.league.box_scores.return_value = [mock_matchup]
        
        assert analyzer.is_week_complete(1) == False
    
    def test_is_week_complete_handles_api_errors(self, analyzer):
        """Test graceful handling of API errors"""
        analyzer.league.box_scores.side_effect = Exception("API Error")
        
        assert analyzer.is_week_complete(1) == False
    
    def test_get_completed_week_data_returns_all_players(self, analyzer, sample_completed_players):
        """Test getting all player data for completed week"""
        mock_matchup = MockMatchup(sample_completed_players[:3], sample_completed_players[3:])
        analyzer.league.box_scores.return_value = [mock_matchup]
        
        with patch.object(analyzer, 'is_week_complete', return_value=True):
            players = analyzer.get_completed_week_data(1)
            
        assert len(players) == 6
        assert players[0].name == "Josh Allen"
        assert players[-1].name == "49ers D/ST"
    
    def test_get_completed_week_data_raises_error_for_incomplete_week(self, analyzer):
        """Test error handling for incomplete week"""
        with patch.object(analyzer, 'is_week_complete', return_value=False):
            with pytest.raises(ValueError, match="Week 1 is not yet complete"):
                analyzer.get_completed_week_data(1)
    
    def test_calculate_accuracy_computes_correct_metrics(self, analyzer):
        """Test projection accuracy calculation"""
        accuracy = analyzer._calculate_accuracy(
            source="ESPN",
            player_name="Josh Allen",
            position="QB",
            projected=20.0,
            actual=25.0
        )
        
        assert accuracy.source == "ESPN"
        assert accuracy.player_name == "Josh Allen"
        assert accuracy.position == "QB"
        assert accuracy.projected_points == 20.0
        assert accuracy.actual_points == 25.0
        assert accuracy.difference == 5.0
        assert accuracy.absolute_error == 5.0
        assert accuracy.percentage_error == 25.0
    
    def test_calculate_accuracy_handles_zero_projection(self, analyzer):
        """Test accuracy calculation with zero projection"""
        accuracy = analyzer._calculate_accuracy(
            source="ESPN",
            player_name="Bench Player",
            position="RB", 
            projected=0.0,
            actual=0.0
        )
        
        assert accuracy.percentage_error == 0.0
    
    def test_find_external_projection_exact_match(self, analyzer, external_projections):
        """Test finding external projections with exact name match"""
        mock_player = MockBoxPlayer("Josh Allen", "QB", 25.0, 20.0)
        
        projection = analyzer._find_external_projection(mock_player, external_projections['FanDuel'])
        
        assert projection == 26.1
    
    def test_find_external_projection_partial_match(self, analyzer, external_projections):
        """Test finding external projections with partial name match"""
        mock_player = MockBoxPlayer("Allen", "QB", 25.0, 20.0)  # Use "Allen" which appears in "Josh Allen"
        
        projection = analyzer._find_external_projection(mock_player, external_projections['FanDuel'])
        
        assert projection == 26.1
    
    def test_find_external_projection_no_match_returns_none(self, analyzer, external_projections):
        """Test no match returns None"""
        mock_player = MockBoxPlayer("Unknown Player", "QB", 25.0, 20.0)
        
        projection = analyzer._find_external_projection(mock_player, external_projections['FanDuel'])
        
        assert projection is None
    
    def test_analyze_projection_accuracy_espn_only(self, analyzer, sample_completed_players):
        """Test projection accuracy analysis with ESPN data only"""
        mock_matchup = MockMatchup(sample_completed_players[:3], sample_completed_players[3:])
        
        with patch.object(analyzer, 'get_completed_week_data', return_value=sample_completed_players):
            results = analyzer.analyze_projection_accuracy(1)
        
        assert len(results) == 6  # One result per player
        
        # Test Josh Allen specifically
        josh_result = next(r for r in results if r.player_name == "Josh Allen")
        assert josh_result.source == "ESPN"
        assert josh_result.projected_points == 24.2
        assert josh_result.actual_points == 28.5
        assert abs(josh_result.difference - 4.3) < 0.01  # Account for floating point precision
    
    def test_analyze_projection_accuracy_with_external_projections(self, analyzer, sample_completed_players, external_projections):
        """Test projection accuracy with external projections"""
        with patch.object(analyzer, 'get_completed_week_data', return_value=sample_completed_players):
            results = analyzer.analyze_projection_accuracy(1, external_projections)
        
        # Should have ESPN + external projections
        sources = set(r.source for r in results)
        assert "ESPN" in sources
        assert "FanDuel" in sources
        assert "WinWithOdds" in sources
        
        # Josh Allen should have projections from all three sources
        josh_results = [r for r in results if r.player_name == "Josh Allen"]
        assert len(josh_results) == 3  # ESPN + FanDuel + WinWithOdds
    
    def test_generate_position_analysis_calculates_correctly(self, analyzer):
        """Test position-level analysis generation"""
        sample_accuracies = [
            ProjectionAccuracy("ESPN", "Josh Allen", "QB", 20.0, 25.0, 5.0, 5.0, 25.0),
            ProjectionAccuracy("FanDuel", "Josh Allen", "QB", 22.0, 25.0, 3.0, 3.0, 13.6),
            ProjectionAccuracy("ESPN", "Saquon Barkley", "RB", 15.0, 12.0, -3.0, 3.0, 20.0),
        ]
        
        position_analyses = analyzer.generate_position_analysis(sample_accuracies)
        
        qb_analysis = next(p for p in position_analyses if p.position == "QB")
        assert qb_analysis.total_players == 2
        assert qb_analysis.avg_projected == 21.0  # (20 + 22) / 2
        assert qb_analysis.avg_actual == 25.0
        assert qb_analysis.best_projection_source == "FanDuel"  # Lower abs error
        assert qb_analysis.worst_projection_source == "ESPN"
        
        rb_analysis = next(p for p in position_analyses if p.position == "RB")
        assert rb_analysis.total_players == 1
        assert rb_analysis.avg_projected == 15.0
        assert rb_analysis.avg_actual == 12.0
    
    def test_generate_week_summary_comprehensive_analysis(self, analyzer):
        """Test comprehensive week summary generation"""
        sample_accuracies = [
            ProjectionAccuracy("ESPN", "Josh Allen", "QB", 20.0, 25.0, 5.0, 5.0, 25.0),
            ProjectionAccuracy("FanDuel", "Josh Allen", "QB", 22.0, 25.0, 3.0, 3.0, 13.6),
            ProjectionAccuracy("ESPN", "Bust Player", "RB", 20.0, 8.0, -12.0, 12.0, 60.0),  # Biggest bust
            ProjectionAccuracy("ESPN", "Boom Player", "WR", 10.0, 25.0, 15.0, 15.0, 150.0),  # Biggest boom
        ]
        
        summary = analyzer.generate_week_summary(1, sample_accuracies)
        
        assert summary.week == 1
        assert summary.total_players_analyzed == 4
        assert summary.most_accurate_source == "FanDuel"  # Lowest MAE
        assert summary.least_accurate_source == "ESPN"  # Highest MAE
        assert summary.biggest_boom.player_name == "Boom Player"
        assert summary.biggest_bust.player_name == "Bust Player"
        assert len(summary.position_summaries) == 3  # QB, RB, WR
        assert summary.overall_mae > 0
        assert summary.overall_rmse > 0
    
    def test_generate_week_summary_raises_error_for_empty_results(self, analyzer):
        """Test error handling for empty accuracy results"""
        with pytest.raises(ValueError, match="No accuracy results to analyze"):
            analyzer.generate_week_summary(1, [])
    
    def test_analyze_week_complete_pipeline(self, analyzer, sample_completed_players, external_projections):
        """Test complete week analysis pipeline"""
        mock_matchup = MockMatchup(sample_completed_players[:3], sample_completed_players[3:])
        mock_matchup.home_team = analyzer.league.teams[0]  # Set to our mock team
        
        with patch.object(analyzer, 'get_completed_week_data', return_value=sample_completed_players), \
             patch.object(analyzer.league, 'box_scores', return_value=[mock_matchup]):
            summary = analyzer.analyze_week(1, external_projections)
        
        assert isinstance(summary, WeekSummary)
        assert summary.week == 1
        assert summary.total_players_analyzed > 0
        assert summary.most_accurate_source in ["ESPN", "FanDuel", "WinWithOdds"]
        assert len(summary.position_summaries) > 0
        assert summary.overall_mae > 0
    
    def test_analyze_week_without_external_projections(self, analyzer, sample_completed_players):
        """Test week analysis with ESPN projections only"""
        mock_matchup = MockMatchup(sample_completed_players[:3], sample_completed_players[3:])
        mock_matchup.home_team = analyzer.league.teams[0]
        
        with patch.object(analyzer, 'get_completed_week_data', return_value=sample_completed_players), \
             patch.object(analyzer.league, 'box_scores', return_value=[mock_matchup]):
            summary = analyzer.analyze_week(1)
        
        assert isinstance(summary, WeekSummary)
        assert summary.most_accurate_source == "ESPN"  # Only source available
        assert summary.least_accurate_source == "ESPN"

class TestProjectionAccuracy:
    
    def test_dataclass_creation(self):
        """Test ProjectionAccuracy dataclass creation"""
        accuracy = ProjectionAccuracy(
            source="ESPN",
            player_name="Josh Allen",
            position="QB", 
            projected_points=20.0,
            actual_points=25.0,
            difference=5.0,
            absolute_error=5.0,
            percentage_error=25.0
        )
        
        assert accuracy.source == "ESPN"
        assert accuracy.player_name == "Josh Allen"
        assert accuracy.difference == 5.0

class TestPositionAnalysis:
    
    def test_dataclass_creation(self):
        """Test PositionAnalysis dataclass creation"""
        analysis = PositionAnalysis(
            position="QB",
            total_players=5,
            avg_projected=20.0,
            avg_actual=18.5,
            avg_error=-1.5,
            avg_abs_error=3.2,
            best_projection_source="FanDuel",
            worst_projection_source="ESPN"
        )
        
        assert analysis.position == "QB"
        assert analysis.total_players == 5
        assert analysis.best_projection_source == "FanDuel"

class TestWeekSummary:
    
    def test_dataclass_creation(self):
        """Test WeekSummary dataclass creation"""
        mock_boom = ProjectionAccuracy("ESPN", "Boom", "WR", 10.0, 25.0, 15.0, 15.0, 150.0)
        mock_bust = ProjectionAccuracy("ESPN", "Bust", "RB", 20.0, 5.0, -15.0, 15.0, 75.0)
        
        summary = WeekSummary(
            week=1,
            total_players_analyzed=50,
            most_accurate_source="FanDuel",
            least_accurate_source="ESPN",
            biggest_boom=mock_boom,
            biggest_bust=mock_bust,
            position_summaries=[],
            overall_mae=5.2,
            overall_rmse=7.8
        )
        
        assert summary.week == 1
        assert summary.total_players_analyzed == 50
        assert summary.biggest_boom.player_name == "Boom"
        assert summary.biggest_bust.player_name == "Bust"