#!/usr/bin/env python3
"""Test external projection integration"""

from src.fantasy_manager import FantasyManager
import pandas as pd

def test_projection_features():
    fm = FantasyManager()
    
    print("=== Testing External Projection Integration ===\n")
    
    # Test external projection fetching
    print("1. Testing external projection fetch...")
    external_projections = fm.waiver_analyzer.projection_matcher.get_external_projections()
    
    if not external_projections.empty:
        print(f"✅ Fetched {len(external_projections)} external projections")
        print(f"Columns: {external_projections.columns.tolist()}")
        print(f"Sample data:\n{external_projections.head()}")
    else:
        print("⚠️  No external projections fetched (may need site structure updates)")
    
    print("\n" + "="*50 + "\n")
    
    print("=== Projection Integration Tests Complete ===")

if __name__ == "__main__":
    test_projection_features()
