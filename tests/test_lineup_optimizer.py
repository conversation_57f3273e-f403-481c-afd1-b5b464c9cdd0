import unittest
from unittest.mock import MagicMock
import pandas as pd
from src.lineup_optimizer import LineupOptimizer

class TestLineupOptimizer(unittest.TestCase):

    def setUp(self):
        self.mock_league = MagicMock()
        self.lineup_optimizer = LineupOptimizer(self.mock_league)

    def test_get_optimal_lineup(self):
        roster_data = [
            {'player_id': 1, 'name': 'QB1', 'position': 'QB', 'external_proj': 25, 'projected_points': 20, 'bye_week': 2, 'injury_status': 'ACTIVE', 'vegas_boost': 1.1},
            {'player_id': 2, 'name': 'RB1', 'position': 'RB', 'external_proj': 18, 'projected_points': 15, 'bye_week': 2, 'injury_status': 'ACTIVE', 'vegas_boost': 1.0},
            {'player_id': 3, 'name': 'RB2', 'position': 'RB', 'external_proj': 16, 'projected_points': 14, 'bye_week': 2, 'injury_status': 'ACTIVE', 'vegas_boost': 1.0},
            {'player_id': 4, 'name': 'WR1', 'position': 'WR', 'external_proj': 20, 'projected_points': 18, 'bye_week': 2, 'injury_status': 'ACTIVE', 'vegas_boost': 1.2},
            {'player_id': 5, 'name': 'WR2', 'position': 'WR', 'external_proj': 17, 'projected_points': 16, 'bye_week': 2, 'injury_status': 'ACTIVE', 'vegas_boost': 1.1},
            {'player_id': 6, 'name': 'TE1', 'position': 'TE', 'external_proj': 12, 'projected_points': 10, 'bye_week': 2, 'injury_status': 'ACTIVE', 'vegas_boost': 1.0},
            {'player_id': 7, 'name': 'K1', 'position': 'K', 'external_proj': 8, 'projected_points': 7, 'bye_week': 2, 'injury_status': 'ACTIVE', 'vegas_boost': 1.0},
            {'player_id': 8, 'name': 'D/ST1', 'position': 'D/ST', 'external_proj': 6, 'projected_points': 5, 'bye_week': 2, 'injury_status': 'ACTIVE', 'vegas_boost': 1.0},
            {'player_id': 9, 'name': 'FLEX_RB', 'position': 'RB', 'external_proj': 15, 'projected_points': 13, 'bye_week': 2, 'injury_status': 'ACTIVE', 'vegas_boost': 1.0},
        ]
        roster_df = pd.DataFrame(roster_data)

        optimal_lineup, used_player_ids = self.lineup_optimizer.get_optimal_lineup(roster_df, 1)

        self.assertIn('QB', optimal_lineup)
        self.assertIn('RB1', optimal_lineup)
        self.assertIn('RB2', optimal_lineup)
        self.assertIn('WR1', optimal_lineup)
        self.assertIn('WR2', optimal_lineup)
        self.assertIn('TE', optimal_lineup)
        self.assertIn('FLEX', optimal_lineup)
        self.assertIn('K', optimal_lineup)
        self.assertIn('D/ST', optimal_lineup)

        self.assertEqual(len(used_player_ids), 9)

if __name__ == '__main__':
    unittest.main()
