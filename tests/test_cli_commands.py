import pytest
from unittest.mock import patch, MagicMock
import sys
import argparse

class TestCLIArgumentParsing:
    """Test CLI argument parsing and command structure."""

    def test_cli_imports_correctly(self):
        """Test that all CLI imports work without errors."""
        from cli import main
        # If we can import main without errors, the structure is correct
        assert callable(main)

    def test_argument_parser_setup(self):
        """Test that the argument parser can be set up without errors."""
        # Import required classes directly to test they exist
        from src.commands.waiver import WaiverCommand
        from src.commands.startsit import StartSitCommand
        from src.commands.claude import ClaudeCommand
        from src.commands.ai_analyze import AiAnalyzeCommand
        from src.commands.pre_waiver import PreWaiverCommand
        from src.commands.post_waiver import PostWaiverCommand
        from src.commands.pre_game import PreGameCommand
        from src.commands.quick import QuickCommand
        from src.commands.status import StatusCommand
        from src.commands.email import EmailCommand
        from src.commands.postweek import PostweekCommand
        from src.commands.capture import CaptureCommand
        from src.commands.capture_db import CaptureDb<PERSON>ommand
        from src.commands.stream_import import StreamImportCommand
        
        # Test that we can create the command registry
        command_registry = {
            "waiver": WaiverCommand(),
            "startsit": StartSitCommand(),
            "claude": ClaudeCommand(),
            "ai-analyze": AiAnalyzeCommand(),
            "pre-waiver": PreWaiverCommand(),
            "post-waiver": PostWaiverCommand(),
            "pre-game": PreGameCommand(),
            "quick": QuickCommand(),
            "status": StatusCommand(),
            "email": EmailCommand(),
            "postweek": PostweekCommand(),
            "capture": CaptureCommand(),
            "capture-db": CaptureDbCommand(),
            "stream-import": StreamImportCommand(),
        }
        
        # Test that we can set up the parser structure
        parent_parser = argparse.ArgumentParser(add_help=False)
        parent_parser.add_argument("--week", type=int, help="NFL week number")
        parent_parser.add_argument("--open", action="store_true", help="Open file in browser")
        parent_parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
        
        parser = argparse.ArgumentParser(description="Fantasy AI CLI")
        subparsers = parser.add_subparsers(dest="command", help="Available commands")
        
        # Test that each command can add its arguments without conflicts
        for command_name, command_instance in command_registry.items():
            cmd_parser = subparsers.add_parser(command_name, parents=[parent_parser])
            command_instance.add_arguments(cmd_parser)
        
        # If we reach here without errors, the parser setup is working
        assert len(command_registry) == 14

    def test_specific_command_arguments(self):
        """Test that specific commands can parse their arguments correctly."""
        # Test waiver command specifically
        from src.commands.waiver import WaiverCommand
        
        parent_parser = argparse.ArgumentParser(add_help=False)
        parent_parser.add_argument("--week", type=int, help="NFL week number")
        parent_parser.add_argument("--open", action="store_true", help="Open file in browser")
        parent_parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
        
        parser = argparse.ArgumentParser(description="Test")
        subparsers = parser.add_subparsers(dest="command")
        
        waiver_parser = subparsers.add_parser('waiver', parents=[parent_parser])
        waiver_cmd = WaiverCommand()
        waiver_cmd.add_arguments(waiver_parser)
        
        # Test parsing waiver arguments
        args = parser.parse_args(['waiver', '--week', '5', '--size', '100', '--open'])
        assert args.command == 'waiver'
        assert args.week == 5
        assert args.size == 100
        assert args.open is True

    def test_no_command_behavior(self):
        """Test that CLI shows help when no command is provided."""
        with patch('sys.argv', ['cli.py']):
            with patch('argparse.ArgumentParser.print_help') as mock_help:
                from cli import main
                main()
                mock_help.assert_called_once()

    @patch('cli.FantasyManager')
    @patch('src.commands.waiver.WaiverCommand.handle')
    def test_command_execution_flow(self, mock_handle, mock_fm_class):
        """Test that commands are called with correct arguments."""
        # Setup mocks
        mock_fm = mock_fm_class.return_value
        mock_fm.league.current_week = 1
        mock_fm.league.settings.name = "Test League"
        mock_fm.year = 2025
        mock_fm.league.refresh = MagicMock()
        
        # Test waiver command execution
        with patch('sys.argv', ['cli.py', 'waiver', '--week', '5', '--size', '100']):
            from cli import main
            main()
            
            # Verify the handle method was called
            mock_handle.assert_called_once()
            
            # Get the arguments passed to handle
            call_args = mock_handle.call_args
            args = call_args[0][0]  # First positional argument
            
            # Verify arguments were parsed correctly
            assert args.command == 'waiver'
            assert args.week == 5
            assert args.size == 100
            
            # Verify fm was passed as keyword argument
            assert 'fm' in call_args[1]
            assert call_args[1]['fm'] == mock_fm

    @patch('src.commands.capture_db.CaptureDbCommand.handle')
    def test_no_fm_command_execution(self, mock_handle):
        """Test that commands marked as no-fm don't receive fm parameter."""
        with patch('sys.argv', ['cli.py', 'capture-db', '--sources', 'espn']):
            from cli import main
            main()
            
            # Verify the handle method was called
            mock_handle.assert_called_once()
            
            # Get the arguments passed to handle
            call_args = mock_handle.call_args
            args = call_args[0][0]  # First positional argument
            
            # Verify arguments were parsed correctly
            assert args.command == 'capture-db'
            assert 'espn' in args.sources
            
            # Verify fm was NOT passed (should only have args, no kwargs)
            assert len(call_args[0]) == 1  # Only args parameter
            assert len(call_args[1]) == 0  # No keyword arguments

    def test_help_commands(self):
        """Test that help works for main CLI and subcommands."""
        test_cases = [
            ['cli.py', '--help'],
            ['cli.py', 'waiver', '--help'],
            ['cli.py', 'status', '--help'],
        ]
        
        for argv in test_cases:
            with patch('sys.argv', argv):
                with patch('argparse.ArgumentParser.parse_args') as mock_parse:
                    mock_parse.side_effect = SystemExit(0)  # Help exits with 0
                    
                    from cli import main
                    try:
                        main()
                    except SystemExit as e:
                        # Help should exit with code 0
                        assert e.code == 0
                    else:
                        # If no SystemExit, something went wrong
                        assert False, f"Expected SystemExit for help command: {argv}"