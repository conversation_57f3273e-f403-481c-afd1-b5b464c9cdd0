import unittest
from unittest.mock import patch
import pandas as pd
from src.trade_analyzer import TradeAnalyzer

class TestTradeAnalyzer(unittest.TestCase):

    @patch('src.trade_analyzer.WinWithOddsTrends')
    def test_get_trade_insights(self, mock_wwo_trends):
        trade_analyzer = TradeAnalyzer()
        trade_analyzer.wwo_trends = mock_wwo_trends.return_value

        roster_data = [
            {'name': 'Player A', 'position': 'WR'},
            {'name': 'Player B', 'position': 'RB'},
        ]
        roster_df = pd.DataFrame(roster_data)

        buy_low_data = [{'name': 'Player C', 'position': 'WR', 'trend_pct': -25.0}]
        sell_high_data = [{'name': 'Player A', 'position': 'WR', 'trend_pct': 30.0}]
        undervalued_data = [{'name': 'Player D', 'position': 'TE', 'value': 3.0}]

        mock_wwo_trends.return_value.get_trade_targets.return_value = (pd.DataFrame(buy_low_data), pd.DataFrame(sell_high_data))
        mock_wwo_trends.return_value.get_undervalued_players.return_value = pd.DataFrame(undervalued_data)

        trade_insights = trade_analyzer.get_trade_insights(roster_df)

        self.assertEqual(len(trade_insights['my_sell_high_candidates']), 1)
        self.assertEqual(trade_insights['my_sell_high_candidates'][0]['name'], 'Player A')
        self.assertEqual(len(trade_insights['external_buy_targets']), 1)
        self.assertEqual(trade_insights['external_buy_targets'][0]['name'], 'Player C')
        self.assertEqual(len(trade_insights['waiver_wire_targets']), 1)
        self.assertEqual(trade_insights['waiver_wire_targets'][0]['name'], 'Player D')

if __name__ == '__main__':
    unittest.main()
