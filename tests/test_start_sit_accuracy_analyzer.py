"""
Tests for Start/Sit Accuracy Analyzer

Comprehensive test suite for the start/sit accuracy analysis system.
"""

import pytest
import pandas as pd
from unittest.mock import Mock, MagicMock, patch
from dataclasses import dataclass
from typing import List, Dict

from src.start_sit_accuracy_analyzer import (
    StartSitAccuracyAnalyzer,
    StartSitDecision,
    TeamStartSitPerformance,
    LeagueStartSitIntelligence,
    ProjectionSourceComparison
)


@pytest.fixture
def mock_league():
    """Create a mock ESPN league for testing."""
    league = Mock()
    
    # Create mock teams
    team1 = Mock()
    team1.team_name = "Team Alpha"
    team1.team_id = 1
    team1.roster = []
    
    team2 = Mock()
    team2.team_name = "Team Beta"  
    team2.team_id = 2
    team2.roster = []
    
    league.teams = [team1, team2]
    return league


@pytest.fixture
def mock_players():
    """Create mock player data for testing."""
    players = {}
    
    # Starter players with good performance
    players['good_qb'] = <PERSON><PERSON>()
    players['good_qb'].name = "<PERSON>"
    players['good_qb'].position = "QB"
    players['good_qb'].points = 25.0
    players['good_qb'].projected_points = 20.0
    players['good_qb'].slot_position = "QB"
    
    players['good_rb'] = Mock()
    players['good_rb'].name = "Christian McCaffrey"
    players['good_rb'].position = "RB"
    players['good_rb'].points = 18.5
    players['good_rb'].projected_points = 15.0
    players['good_rb'].slot_position = "RB"
    
    # Bench players who outperformed starters
    players['bench_qb'] = Mock()
    players['bench_qb'].name = "Lamar Jackson"
    players['bench_qb'].position = "QB"
    players['bench_qb'].points = 30.0
    players['bench_qb'].projected_points = 18.0
    players['bench_qb'].slot_position = "BE"
    
    players['bench_rb'] = Mock()
    players['bench_rb'].name = "Derrick Henry"
    players['bench_rb'].position = "RB"
    players['bench_rb'].points = 22.0
    players['bench_rb'].projected_points = 12.0
    players['bench_rb'].slot_position = "BE"
    
    # Poor performing starters
    players['bad_wr'] = Mock()
    players['bad_wr'].name = "Cooper Kupp"
    players['bad_wr'].position = "WR"
    players['bad_wr'].points = 3.2
    players['bad_wr'].projected_points = 15.0
    players['bad_wr'].slot_position = "WR"
    
    # Better bench WR
    players['bench_wr'] = Mock()
    players['bench_wr'].name = "Tyreek Hill"
    players['bench_wr'].position = "WR"
    players['bench_wr'].points = 19.8
    players['bench_wr'].projected_points = 14.0
    players['bench_wr'].slot_position = "BE"
    
    return players


@pytest.fixture
def start_sit_analyzer(mock_league):
    """Create StartSitAccuracyAnalyzer instance for testing."""
    return StartSitAccuracyAnalyzer(mock_league)


class TestStartSitAccuracyAnalyzer:
    """Test the main StartSitAccuracyAnalyzer class."""
    
    def test_initialization(self, start_sit_analyzer, mock_league):
        """Test analyzer initializes correctly."""
        assert start_sit_analyzer.league == mock_league
        assert start_sit_analyzer.logger is not None
    
    @patch('src.start_sit_accuracy_analyzer.StartSitAccuracyAnalyzer._calculate_optimal_lineup_points')
    def test_analyze_team_start_sit_performance(self, mock_optimal_calc, start_sit_analyzer, mock_players):
        """Test team start/sit performance analysis."""
        mock_optimal_calc.return_value = 85.0
        
        # Mock team and box scores
        team = Mock()
        team.team_name = "Test Team"
        team.roster = list(mock_players.values())
        
        # Create mock lineup with starters and bench
        mock_lineup = [
            mock_players['good_qb'],
            mock_players['good_rb'], 
            mock_players['bad_wr'],
            mock_players['bench_qb'],
            mock_players['bench_rb'],
            mock_players['bench_wr']
        ]
        
        # Mock box scores
        mock_matchup = Mock()
        mock_matchup.home_team = team
        mock_matchup.home_lineup = mock_lineup
        
        start_sit_analyzer.league.box_scores.return_value = [mock_matchup]
        
        # Run analysis
        performance = start_sit_analyzer.analyze_team_start_sit_performance(team, week=1)
        
        # Verify results
        assert performance.team_name == "Test Team"
        assert performance.week == 1
        assert performance.total_starting_points > 0
        assert performance.points_left_on_bench >= 0
        assert performance.lineup_efficiency <= 100.0
        assert isinstance(performance.position_breakdown, dict)
    
    def test_calculate_optimal_lineup_points(self, start_sit_analyzer, mock_players):
        """Test optimal lineup point calculation."""
        all_players = list(mock_players.values())
        
        optimal_points = start_sit_analyzer._calculate_optimal_lineup_points(all_players)
        
        # Should be > 0 with valid players
        assert optimal_points > 0
        # Should be less than sum of all players (can't start everyone)
        total_points = sum(getattr(p, 'points', 0) for p in all_players)
        assert optimal_points <= total_points
    
    def test_analyze_start_sit_decisions(self, start_sit_analyzer, mock_players):
        """Test individual start/sit decision analysis."""
        starters = [mock_players['good_qb'], mock_players['bad_wr']]
        bench = [mock_players['bench_qb'], mock_players['bench_wr']]
        
        decisions = start_sit_analyzer._analyze_start_sit_decisions(
            starters, bench, week=1, team_name="Test Team"
        )
        
        assert len(decisions) == len(starters)
        
        # Check QB decision (should be negative - bench QB scored more)
        qb_decision = next(d for d in decisions if d.position == "QB")
        assert qb_decision.decision_impact < 0  # Bad decision
        assert qb_decision.was_started is True
        
        # Check WR decision (should be very negative - huge miss)
        wr_decision = next(d for d in decisions if d.position == "WR")
        assert wr_decision.decision_impact < -10  # Very bad decision
    
    def test_calculate_position_breakdown(self, start_sit_analyzer, mock_players):
        """Test position breakdown calculation."""
        starters = [mock_players['good_qb'], mock_players['good_rb']]
        bench = [mock_players['bench_qb'], mock_players['bench_wr']]
        
        breakdown = start_sit_analyzer._calculate_position_breakdown(starters, bench)
        
        # Should have QB in breakdown
        assert 'QB' in breakdown
        qb_data = breakdown['QB']
        assert qb_data['starter_total'] == 25.0
        assert qb_data['starter_count'] == 1
        assert qb_data['bench_total'] == 30.0
        assert qb_data['bench_count'] == 1
        assert qb_data['starter_avg'] == 25.0
        assert qb_data['bench_avg'] == 30.0
    
    @patch('src.start_sit_accuracy_analyzer.StartSitAccuracyAnalyzer.analyze_team_start_sit_performance')
    def test_analyze_league_start_sit_intelligence(self, mock_team_analysis, start_sit_analyzer):
        """Test league-wide start/sit intelligence analysis."""
        # Mock team performances
        perf1 = TeamStartSitPerformance(
            team_name="Team Alpha",
            week=1,
            total_starting_points=75.0,
            total_bench_points=45.0,
            points_left_on_bench=8.0,
            optimal_lineup_points=83.0,
            actual_vs_optimal_diff=-8.0,
            lineup_efficiency=90.4,
            best_start_decision=None,
            worst_start_decision=None,
            missed_opportunities=[],
            position_breakdown={}
        )
        
        perf2 = TeamStartSitPerformance(
            team_name="Team Beta",
            week=1,
            total_starting_points=65.0,
            total_bench_points=55.0,
            points_left_on_bench=12.0,
            optimal_lineup_points=77.0,
            actual_vs_optimal_diff=-12.0,
            lineup_efficiency=84.4,
            best_start_decision=None,
            worst_start_decision=None,
            missed_opportunities=[],
            position_breakdown={}
        )
        
        mock_team_analysis.side_effect = [perf1, perf2]
        
        # Run analysis
        intelligence = start_sit_analyzer.analyze_league_start_sit_intelligence(week=1)
        
        # Verify results
        assert intelligence.week == 1
        assert len(intelligence.team_performances) == 2
        assert intelligence.best_decision_maker == "Team Alpha"  # Higher efficiency
        assert intelligence.worst_decision_maker == "Team Beta"   # Lower efficiency
        assert intelligence.most_points_left_on_bench == "Team Beta"  # More points on bench
        assert intelligence.league_avg_points_on_bench == 10.0  # (8+12)/2
        assert intelligence.league_avg_lineup_efficiency == 87.4  # (90.4+84.4)/2
    
    def test_calculate_league_position_trends(self, start_sit_analyzer):
        """Test league position trends calculation."""
        # Create mock team performances with position breakdowns
        performances = []
        
        # Team 1: Good QB decisions, bad RB decisions
        perf1 = TeamStartSitPerformance(
            team_name="Team 1",
            week=1,
            total_starting_points=0,
            total_bench_points=0,
            points_left_on_bench=0,
            optimal_lineup_points=0,
            actual_vs_optimal_diff=0,
            lineup_efficiency=0,
            best_start_decision=None,
            worst_start_decision=None,
            missed_opportunities=[],
            position_breakdown={
                'QB': {'starter_total': 25, 'starter_avg': 25, 'bench_total': 20, 'bench_avg': 20},
                'RB': {'starter_total': 15, 'starter_avg': 15, 'bench_total': 25, 'bench_avg': 25}
            }
        )
        
        # Team 2: Bad QB decisions, good RB decisions  
        perf2 = TeamStartSitPerformance(
            team_name="Team 2",
            week=1,
            total_starting_points=0,
            total_bench_points=0,
            points_left_on_bench=0,
            optimal_lineup_points=0,
            actual_vs_optimal_diff=0,
            lineup_efficiency=0,
            best_start_decision=None,
            worst_start_decision=None,
            missed_opportunities=[],
            position_breakdown={
                'QB': {'starter_total': 12, 'starter_avg': 12, 'bench_total': 22, 'bench_avg': 22},
                'RB': {'starter_total': 28, 'starter_avg': 28, 'bench_total': 18, 'bench_avg': 18}
            }
        )
        
        performances = [perf1, perf2]
        
        trends = start_sit_analyzer._calculate_league_position_trends(performances)
        
        # Verify QB trends (mixed performance across teams)
        assert 'QB' in trends
        qb_trends = trends['QB']
        assert qb_trends['avg_starter_points'] == 18.5  # (25+12)/2
        assert qb_trends['avg_bench_points'] == 21.0    # (20+22)/2
        assert qb_trends['avg_points_left_on_bench'] == 5.0  # ((0+10)/2)
        
        # Verify RB trends
        assert 'RB' in trends
        rb_trends = trends['RB']
        assert rb_trends['avg_starter_points'] == 21.5  # (15+28)/2
        assert rb_trends['avg_bench_points'] == 21.5    # (25+18)/2
        assert rb_trends['avg_points_left_on_bench'] == 5.0  # ((10+0)/2)


class TestStartSitDecision:
    """Test the StartSitDecision dataclass."""
    
    def test_start_sit_decision_creation(self):
        """Test creating StartSitDecision objects."""
        decision = StartSitDecision(
            team_name="Test Team",
            player_name="Josh Allen",
            position="QB",
            week=1,
            was_started=True,
            actual_points=25.0,
            bench_points=18.0,
            decision_impact=7.0,
            projection_source="ESPN",
            projected_points=20.0
        )
        
        assert decision.team_name == "Test Team"
        assert decision.player_name == "Josh Allen"
        assert decision.decision_impact == 7.0
        assert decision.was_started is True


class TestTeamStartSitPerformance:
    """Test the TeamStartSitPerformance dataclass."""
    
    def test_team_performance_creation(self):
        """Test creating TeamStartSitPerformance objects."""
        performance = TeamStartSitPerformance(
            team_name="Test Team",
            week=1,
            total_starting_points=75.0,
            total_bench_points=45.0,
            points_left_on_bench=8.0,
            optimal_lineup_points=83.0,
            actual_vs_optimal_diff=-8.0,
            lineup_efficiency=90.4,
            best_start_decision=None,
            worst_start_decision=None,
            missed_opportunities=[],
            position_breakdown={}
        )
        
        assert performance.team_name == "Test Team"
        assert performance.lineup_efficiency == 90.4
        assert performance.points_left_on_bench == 8.0
        assert performance.actual_vs_optimal_diff == -8.0


class TestLeagueStartSitIntelligence:
    """Test the LeagueStartSitIntelligence dataclass."""
    
    def test_league_intelligence_creation(self):
        """Test creating LeagueStartSitIntelligence objects."""
        intelligence = LeagueStartSitIntelligence(
            week=1,
            team_performances=[],
            league_avg_points_on_bench=10.5,
            league_avg_lineup_efficiency=85.2,
            best_decision_maker="Team Alpha",
            worst_decision_maker="Team Beta",
            most_points_left_on_bench="Team Gamma",
            best_start_decision_league_wide=None,
            worst_start_decision_league_wide=None,
            position_trends={}
        )
        
        assert intelligence.week == 1
        assert intelligence.league_avg_points_on_bench == 10.5
        assert intelligence.best_decision_maker == "Team Alpha"


class TestProjectionSourceComparison:
    """Test projection source comparison functionality."""
    
    def test_projection_comparison_creation(self):
        """Test creating ProjectionSourceComparison objects."""
        comparison = ProjectionSourceComparison(
            source_name="ESPN",
            week=1,
            total_decisions_tracked=50,
            correct_start_decisions=35,
            correct_sit_decisions=28,
            overall_accuracy=84.0,
            points_gained_vs_actual_decisions=12.5,
            position_accuracy={"QB": 90.0, "RB": 78.5},
            best_position="QB",
            worst_position="TE"
        )
        
        assert comparison.source_name == "ESPN"
        assert comparison.overall_accuracy == 84.0
        assert comparison.best_position == "QB"
        assert comparison.position_accuracy["QB"] == 90.0


class TestIntegration:
    """Integration tests for the complete start/sit analysis system."""
    
    @patch('src.start_sit_accuracy_analyzer.StartSitAccuracyAnalyzer._calculate_optimal_lineup_points')
    def test_complete_analysis_pipeline(self, mock_optimal_calc, start_sit_analyzer, mock_players):
        """Test the complete analysis pipeline from start to finish."""
        mock_optimal_calc.return_value = 100.0
        
        # Setup mock data
        team1 = start_sit_analyzer.league.teams[0]
        team1.roster = list(mock_players.values())
        
        team2 = start_sit_analyzer.league.teams[1]  
        team2.roster = list(mock_players.values())
        
        # Mock box scores for both teams
        mock_lineup1 = list(mock_players.values())
        mock_lineup2 = list(mock_players.values())
        
        mock_matchup1 = Mock()
        mock_matchup1.home_team = team1
        mock_matchup1.home_lineup = mock_lineup1
        
        mock_matchup2 = Mock()
        mock_matchup2.home_team = team2
        mock_matchup2.home_lineup = mock_lineup2
        
        start_sit_analyzer.league.box_scores.return_value = [mock_matchup1, mock_matchup2]
        
        # Run complete analysis
        intelligence = start_sit_analyzer.analyze_league_start_sit_intelligence(week=1)
        
        # Verify comprehensive results
        assert intelligence.week == 1
        assert len(intelligence.team_performances) == 2
        assert intelligence.league_avg_points_on_bench >= 0
        assert intelligence.league_avg_lineup_efficiency >= 0
        assert intelligence.best_decision_maker in ["Team Alpha", "Team Beta"]
        assert intelligence.worst_decision_maker in ["Team Alpha", "Team Beta"]
        assert isinstance(intelligence.position_trends, dict)
    
    def test_error_handling_empty_data(self, start_sit_analyzer):
        """Test error handling with empty or invalid data."""
        # Test with no box scores
        start_sit_analyzer.league.box_scores.return_value = []
        
        intelligence = start_sit_analyzer.analyze_league_start_sit_intelligence(week=1)
        
        # Should handle gracefully - returns empty performances but still analyzes teams
        assert intelligence.week == 1
        assert len(intelligence.team_performances) == 2  # Still processes teams, but with empty data
        assert intelligence.league_avg_points_on_bench == 0.0
        # All team performances should be empty/zero
        for perf in intelligence.team_performances:
            assert perf.total_starting_points == 0.0
            assert perf.total_bench_points == 0.0
    
    def test_edge_case_single_team(self, start_sit_analyzer, mock_players):
        """Test analysis with only one team in league."""
        # Remove second team
        start_sit_analyzer.league.teams = [start_sit_analyzer.league.teams[0]]
        
        team = start_sit_analyzer.league.teams[0]
        team.roster = list(mock_players.values())
        
        mock_lineup = list(mock_players.values())
        mock_matchup = Mock()
        mock_matchup.home_team = team
        mock_matchup.home_lineup = mock_lineup
        
        start_sit_analyzer.league.box_scores.return_value = [mock_matchup]
        
        # Should work with single team
        intelligence = start_sit_analyzer.analyze_league_start_sit_intelligence(week=1)
        
        assert len(intelligence.team_performances) == 1
        assert intelligence.best_decision_maker == intelligence.worst_decision_maker


if __name__ == "__main__":
    pytest.main([__file__])