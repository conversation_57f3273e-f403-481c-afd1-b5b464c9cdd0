🏈 **POST-WEEK STRATEGIC VALIDATION - WEEK {{ week }}**

## **LEAGUE CONTEXT**
- **League**: {{ league_name }} 
- **Week Analyzed**: {{ week }}
- **Season**: {{ year }}

## **PROJECTION ACCURACY SUMMARY**
{% if projection_summary %}
📊 **Overall Accuracy Metrics:**
- Total Players Analyzed: {{ projection_summary.total_players }}
- Most Accurate Source: {{ projection_summary.most_accurate_source }}
- Least Accurate Source: {{ projection_summary.least_accurate_source }}
- Overall MAE: {{ "%.2f"|format(projection_summary.overall_mae) }} points
- Overall RMSE: {{ "%.2f"|format(projection_summary.overall_rmse) }} points

💥 **Biggest Boom:** {{ projection_summary.biggest_boom.player_name }} ({{ projection_summary.biggest_boom.position }})
   - Projected: {{ "%.1f"|format(projection_summary.biggest_boom.projected_points) }}, Actual: {{ "%.1f"|format(projection_summary.biggest_boom.actual_points) }}
   - Outperformed by: +{{ "%.1f"|format(projection_summary.biggest_boom.difference) }} points

💸 **Biggest Bust:** {{ projection_summary.biggest_bust.player_name }} ({{ projection_summary.biggest_bust.position }})
   - Projected: {{ "%.1f"|format(projection_summary.biggest_bust.projected_points) }}, Actual: {{ "%.1f"|format(projection_summary.biggest_bust.actual_points) }}
   - Underperformed by: {{ "%.1f"|format(projection_summary.biggest_bust.difference) }} points

📋 **Position-Level Accuracy:**
{% for pos_analysis in projection_summary.position_summaries %}
- **{{ pos_analysis.position }}**: {{ pos_analysis.total_players }} players analyzed
  * Avg Projected: {{ "%.1f"|format(pos_analysis.avg_projected) }}, Avg Actual: {{ "%.1f"|format(pos_analysis.avg_actual) }}
  * Best Source: {{ pos_analysis.best_projection_source }}
  * Avg Error: {{ "%.2f"|format(pos_analysis.avg_abs_error) }} points
{% endfor %}
{% endif %}

{% if my_team_performance %}
## **YOUR TEAM PERFORMANCE: {{ my_team_performance.team_name }}**
📊 **Team Totals:**
- Total Projected: {{ "%.1f"|format(my_team_performance.total_projected) }} points
- Total Actual: {{ "%.1f"|format(my_team_performance.total_actual) }} points
- Difference: {{ "%.1f"|format(my_team_performance.difference) }} points {% if my_team_performance.difference >= 0 %}(BEAT projections!){% else %}(missed projections){% endif %}

👥 **Roster Breakdown:**
- Players Analyzed: {{ my_team_performance.players_analyzed }}
- Lineup Players: {{ my_team_performance.lineup_players }}
- Bench Players: {{ my_team_performance.bench_players }}
- Lineup Accuracy (MAE): {{ "%.2f"|format(my_team_performance.lineup_accuracy) }} points
{% if my_team_performance.bench_players > 0 %}
- Bench Accuracy (MAE): {{ "%.2f"|format(my_team_performance.bench_accuracy) }} points
{% endif %}

{% if my_team_performance.best_performer %}
⭐ **Your Best Performer:** {{ my_team_performance.best_performer.player_name }} ({{ my_team_performance.best_performer.position }})
   - Projected: {{ "%.1f"|format(my_team_performance.best_performer.projected_points) }}, Actual: {{ "%.1f"|format(my_team_performance.best_performer.actual_points) }}
   - Outperformed by: +{{ "%.1f"|format(my_team_performance.best_performer.difference) }} points
{% endif %}

{% if my_team_performance.worst_performer %}
💔 **Your Worst Performer:** {{ my_team_performance.worst_performer.player_name }} ({{ my_team_performance.worst_performer.position }})
   - Projected: {{ "%.1f"|format(my_team_performance.worst_performer.projected_points) }}, Actual: {{ "%.1f"|format(my_team_performance.worst_performer.actual_points) }}
   - Underperformed by: {{ "%.1f"|format(my_team_performance.worst_performer.difference) }} points
{% endif %}
{% endif %}

{% if strategic_decisions %}
## **STRATEGIC DECISION VALIDATION**

**Pre-Game Analysis Source:** {{ strategic_analysis_file }}

{% for outcome in strategic_decisions %}
### **DECISION {{ loop.index }}: {{ outcome.decision.position }}**
**Choice Made:** {{ outcome.decision.player_started }} over {{ outcome.decision.player_benched }}
**Confidence Level:** {{ outcome.decision.confidence }}/10
**Strategic Type:** {{ outcome.decision.decision_type.upper() }}

**Rationale:** {{ outcome.decision.rationale }}

{% if outcome.decision.actual_started is not none and outcome.decision.actual_benched is not none %}
**Results:**
- {{ outcome.decision.player_started }}: {{ "%.1f"|format(outcome.decision.actual_started) }} points
- {{ outcome.decision.player_benched }}: {{ "%.1f"|format(outcome.decision.actual_benched) }} points
- **Impact:** {{ "%.1f"|format(outcome.points_gained_lost) }} points
- **Outcome:** {% if outcome.was_correct %}✅ CORRECT DECISION{% else %}❌ INCORRECT DECISION{% endif %}

**Lessons Learned:** {{ outcome.lessons_learned }}
{% else %}
**Results:** Missing actual performance data for validation
{% endif %}

{% endfor %}

### **STRATEGIC INSIGHTS SUMMARY**
{% set decision_types = {} %}
{% for outcome in strategic_decisions %}
  {% set dt = outcome.decision.decision_type %}
  {% if dt not in decision_types %}
    {% set _ = decision_types.update({dt: {'total': 0, 'correct': 0, 'points': 0}}) %}
  {% endif %}
  {% set _ = decision_types[dt].update({'total': decision_types[dt]['total'] + 1}) %}
  {% if outcome.was_correct %}
    {% set _ = decision_types[dt].update({'correct': decision_types[dt]['correct'] + 1}) %}
  {% endif %}
  {% set _ = decision_types[dt].update({'points': decision_types[dt]['points'] + outcome.points_gained_lost}) %}
{% endfor %}

📊 **Strategy Performance:**
{% for strategy_type, stats in decision_types.items() %}
- **{{ strategy_type.upper() }}**: {{ stats.correct }}/{{ stats.total }} decisions correct ({{ "%.1f"|format(stats.correct / stats.total * 100) }}%)
  * Points Impact: {{ "%.1f"|format(stats.points) }} points
{% endfor %}

{% set total_correct = strategic_decisions | selectattr('was_correct') | list | length %}
{% set total_decisions = strategic_decisions | length %}
{% set total_impact = strategic_decisions | map(attribute='points_gained_lost') | sum %}

**Overall Strategic Performance:**
- ✅ Correct Decisions: {{ total_correct }}/{{ total_decisions }} ({{ "%.1f"|format(total_correct / total_decisions * 100) }}%)
- 📊 Total Points Impact: {{ "%.1f"|format(total_impact) }} points
{% endif %}

## **ANALYSIS QUESTIONS FOR REFLECTION**

### **PROJECTION SOURCE ANALYSIS**
1. **Which projection source was most reliable this week?** Look at the accuracy metrics above
2. **Were there systematic biases?** Did certain positions or player types consistently over/underperform projections?
3. **Should you adjust your projection weighting?** Consider giving more weight to the most accurate source

### **STRATEGIC DECISION ANALYSIS**  
{% if strategic_decisions %}
1. **Which decision types worked best this week?** Review the strategy performance breakdown
2. **Was your confidence calibration accurate?** Did high-confidence decisions actually have bigger point differences?
3. **What patterns emerge from incorrect decisions?** Look for common themes in failed logic
4. **How can you improve future start/sit decisions?** Apply lessons learned to upcoming weeks
{% else %}
1. **Why wasn't strategic analysis available?** Missing pre-game analysis files or actual performance data?
2. **How can you better capture strategic decisions?** Consider more detailed pre-game documentation
{% endif %}

### **ROSTER MANAGEMENT INSIGHTS**
{% if my_team_performance %}
1. **Did your starting lineup outperform your bench?** Compare lineup vs bench accuracy
2. **Were there obvious start/sit mistakes?** Look at bench players who significantly outperformed starters
3. **What does this tell you about your roster construction?** Do you have good depth or need to make moves?
{% endif %}

### **FUTURE WEEK PREPARATION**
1. **What adjustments should you make to your analysis process?**
2. **Which projection sources deserve more trust going forward?**
3. **How will you incorporate these lessons into next week's decisions?**
4. **Are there roster moves you should prioritize based on this analysis?**

**Provide specific, actionable insights for improving decision-making in future weeks. Focus on patterns, systematic biases, and strategic adjustments rather than just recapping what happened.**