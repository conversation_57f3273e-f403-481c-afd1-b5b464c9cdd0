🏈 **COMPREHENSIVE PRE-GAME LINEUP OPTIMIZATION - WEEK {{ week }}**

## **TEAM CONTEXT & STRATEGY**
- **League Format**: {{ league_settings.scoring_type }} scoring
- **Current Record**: {{ my_team.wins }}-{{ my_team.losses }} (Week {{ week }})
- **Points For**: {{ my_team.points_for|round(1) }} pts
- **League Rank**: {{ my_team.final_standing }}

## **MY COMPLETE ROSTER ANALYSIS**
{% for pos in ['QB', 'RB', 'WR', 'TE', 'K', 'D/ST'] %}
{% set pos_players = roster_df[roster_df['position'] == pos] %}
{% if not pos_players.empty %}
### **{{ pos }} DEPTH CHART & DECISIONS**
{% for _, player in pos_players.iterrows() %}
{% set espn_proj = player['projected_points'] %}
{% set fd_proj = player.get('fanduel_proj', 'N/A') %}
{% set external_proj = player.get('external_proj', espn_proj) %}
{% set status = "🟢 STARTING" if player.get('is_starting', False) else "🔴 BENCH" %}
{% set injury_status = player.get('injury_status', 'ACTIVE') %}
{% set injury_note = "" %}
{% if injury_status and injury_status not in ['ACTIVE', None] %}
    {% if injury_status == 'QUESTIONABLE' %}
        {% set injury_note = " - ⚠️ QUESTIONABLE (game-time decision)" %}
    {% elif injury_status == 'DOUBTFUL' %}
        {% set injury_note = " - 🚨 DOUBTFUL (unlikely to play)" %}
    {% elif injury_status == 'OUT' %}
        {% set injury_note = " - ❌ OUT (ruled out)" %}
    {% elif injury_status == 'INJURY_RESERVE' %}
        {% set injury_note = " - 🏥 IR (season-ending)" %}
    {% else %}
        {% set injury_note = " - ⚠️ " ~ injury_status %}
    {% endif %}
{% else %}
    {% set injury_note = " - ✅ HEALTHY" %}
{% endif %}
{% set opponent = player.get('opponent', 'Unknown') %}
{% set matchup_note = " vs " ~ opponent if opponent != 'Unknown' else "" %}
{% set variance_note = "" %}
{% if fd_proj != 'N/A' and fd_proj is number %}
    {% set proj_diff = external_proj - fd_proj %}
    {% if proj_diff|abs > 2 %}
        {% set variance_note = " (⚠️ " ~ "+.1f"|format(proj_diff) ~ " variance)" %}
    {% else %}
        {% set variance_note = " (" ~ "+.1f"|format(proj_diff) ~ " variance)" %}
    {% endif %}
{% endif %}
• **{{ player['name'] }}** {{ status }}
  - ESPN: {{ "%.1f"|format(espn_proj) }} | FanDuel: {{ fd_proj }} | Final: {{ "%.1f"|format(external_proj) }}{{ variance_note }}
  - Matchup: {{ matchup_note }}{{ injury_note }}
{% endfor %}
{% endif %}
{% endfor %}

{% if available_starters and not available_starters.empty %}
## **LAST-MINUTE PICKUP OPTIONS** 
*Available players with starter upside if needed for injuries:*

{% for _, player in available_starters.iterrows() %}
{% set proj = player.get('external_proj', player['projected_points']) %}
{% set ownership = player.get('percent_owned', 0) %}
{% set matchup = player.get('opponent', 'Unknown') %}
• **{{ player['name'] }}** ({{ player['position'] }}) - {{ "%.1f"|format(proj) }} proj vs {{ matchup }} ({{ "%.1f"|format(ownership) }}% owned)
{% endfor %}
{% else %}
*Waiver wire data unavailable: {{ waiver_error }}*
{% endif %}

## **STRATEGIC DECISION FRAMEWORK**

### **CEILING vs FLOOR ANALYSIS NEEDED**
- **When to prioritize CEILING** (boom/bust players):
  * Projected to lose this week based on opponent's lineup
  * Need big performances to make playoffs
  * Opponent has high-floor, low-ceiling lineup
  
- **When to prioritize FLOOR** (safe, consistent players):
  * Projected to win comfortably 
  * Locked in playoff position
  * Opponent has boom/bust lineup that could implode

### **GAME ENVIRONMENT FACTORS**
1. **Weather Conditions**: Check for wind, rain, snow affecting passing games
2. **Game Scripts**: Identify likely blowouts (garbage time) vs close games
3. **Vegas Context**: High total games favor skill positions, low totals favor defenses
4. **Pace of Play**: Fast-paced teams create more opportunities
5. **Red Zone Efficiency**: Goal-line backs and red zone threats get priority

### **MATCHUP ADVANTAGES TO EXPLOIT**
1. **Defensive Rankings**: Target players facing weak positional defenses
2. **Recent Trends**: Players with increasing target share or touches
3. **Home/Away Splits**: Some players perform significantly better at home
4. **Divisional Games**: Often lower-scoring, more defensive
5. **Prime Time Games**: Can affect player performance (positively or negatively)

### **INJURY IMPACT ANALYSIS**
1. **Questionable Players**: Have backup plans ready, check inactives 90 minutes before kickoff
2. **Target Upgrades**: When star players are out, their replacements and teammates benefit
3. **O-Line Injuries**: Significantly impact RB performance and QB protection
4. **Weather + Injuries**: Compound negative effects on passing games

## **SPECIFIC DECISIONS TO MAKE**

**Focus on positions where you have genuine decisions between similarly projected players.**

For each start/sit decision, consider:
1. **Projection variance** - Which player has more upside?
2. **Floor protection** - Who is least likely to bust?
3. **Game environment** - Does weather/script favor certain positions?
4. **Opponent strategy** - Do they have weaknesses to exploit?
5. **Your needs** - Are you projected to win/lose and need ceiling/floor?

**PROVIDE SPECIFIC START/SIT RECOMMENDATIONS WITH:**
- Confidence level (1-10)
- Primary reasoning (matchup/game script/injury)
- Backup plans for questionable players
- Strategic rationale (ceiling vs floor)
