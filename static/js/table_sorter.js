function sortTable(table, column, dataType = 'text') {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const header = table.querySelector(`th:nth-child(${column + 1})`);
    const isAsc = header.classList.contains('sort-desc');
    
    // Clear all sort indicators
    table.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
        th.classList.add('sortable');
    });
    
    // Set current sort indicator
    header.classList.add(isAsc ? 'sort-asc' : 'sort-desc');
    
    rows.sort((a, b) => {
        let aVal = a.children[column].textContent.trim();
        let bVal = b.children[column].textContent.trim();
        
        if (dataType === 'number') {
            // Handle salary values with $ and - characters
            aVal = aVal.replace(/[$,-]/g, '');
            bVal = bVal.replace(/[$,-]/g, '');
            aVal = parseFloat(aVal) || 0;
            bVal = parseFloat(bVal) || 0;
            return isAsc ? aVal - bVal : bVal - aVal;
        } else if (dataType === 'percentage') {
            aVal = parseFloat(aVal.replace('%', '')) || 0;
            bVal = parseFloat(bVal.replace('%', '')) || 0;
            return isAsc ? aVal - bVal : bVal - aVal;
        } else {
            return isAsc ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
        }
    });
    
    rows.forEach(row => tbody.appendChild(row));
}

// Initialize sorting for all tables
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('table').forEach(table => {
        const headers = table.querySelectorAll('th');
        headers.forEach((header, index) => {
            header.classList.add('sortable');
            header.addEventListener('click', () => {
                let dataType = 'text';
                const headerText = header.textContent.trim();
                if (headerText === 'ESPN' || headerText === 'FD' || headerText.includes('FD Salary') || headerText.includes('FD Value')) {
                    dataType = 'number';
                } else if (headerText === 'Own %') {
                    dataType = 'percentage';
                }
                sortTable(table, index, dataType);
            });
        });
    });
});