
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px;
            background-color: #f8f9fa;
            color: #212529;
        }
        .header { 
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white; 
            padding: 20px; 
            border-radius: 8px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .position-section { 
            margin: 25px 0; 
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .position-header {
            background-color: #495057;
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        }
        table { 
            width: 100%; 
            border-collapse: collapse;
            margin: 0;
            background: white;
        }
        th { 
            background-color: #f8f9fa;
            padding: 12px 15px;
            text-align: left;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            cursor: pointer;
            user-select: none;
            position: relative;
        }
        th:hover {
            background-color: #e9ecef;
        }
        th.sortable::after {
            content: ' ↕';
            font-size: 0.8em;
            color: #6c757d;
        }
        th.sort-asc::after {
            content: ' ↑';
            color: #007bff;
        }
        th.sort-desc::after {
            content: ' ↓';
            color: #007bff;
        }
        td { 
            padding: 12px 15px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        .player-name {
            font-weight: 600;
            color: #007bff;
        }
        .roster-starting .player-name {
            color: #0056b3;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0,123,255,0.2);
        }
        .roster-bench .player-name {
            color: #495057;
            font-weight: 600;
        }
        .high-priority {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
        }
        .medium-priority {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .low-priority {
            background-color: #f8f9fa;
            border-left: 4px solid #6c757d;
        }
        .roster-starting {
            background-color: #e7f3ff;
            border-left: 6px solid #007bff;
            font-weight: 600;
            position: relative;
        }
        .roster-starting::after {
            content: '🏆';
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.2em;
            opacity: 0.6;
            pointer-events: none;
        }
        .roster-bench {
            background-color: #f0f0f0;
            border-left: 6px solid #6c757d;
            position: relative;
        }
        .roster-bench::after {
            content: '📋';
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.2em;
            opacity: 0.6;
            pointer-events: none;
        }
        .proj-points {
            font-weight: bold;
            color: #007bff;
        }
        .ownership {
            color: #6c757d;
            font-size: 0.9em;
        }
        .recommendation {
            font-size: 0.85em;
            font-weight: 500;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
        }
        .roster-starting td:last-child,
        .roster-bench td:last-child {
            padding-right: 35px;
        }
        .rec-high { background-color: #d4edda; color: #155724; }
        .rec-medium { background-color: #fff3cd; color: #856404; }
        .rec-low { background-color: #f8f9fa; color: #6c757d; }
        .rec-roster { background-color: #e7f3ff; color: #0056b3; }
        .team-name {
            font-size: 0.9em;
            color: #6c757d;
            font-weight: 500;
        }
        /* New styles for Start/Sit Report */
        .injured-player {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        .bye-player {
            background-color: #e2e3e5;
            border-left: 4px solid #6c757d;
        }
        .rec-injured { background-color: #f8d7da; color: #dc3545; }
        .rec-bye { background-color: #e2e3e5; color: #6c757d; }
        .rec-start { background-color: #d4edda; color: #155724; }
        .rec-bench { background-color: #fff3cd; color: #856404; }
        /* New styles for side-by-side lineup comparison */
        .lineup-comparison-container {
            display: flex;
            justify-content: space-around;
            gap: 20px;
            flex-wrap: wrap;
            margin-top: 30px;
        }
        .my-lineup-section, .opponent-lineup-section {
            flex: 1;
            min-width: 45%;
            max-width: 48%;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
            background-color: #ffffff;
        }
        @media (max-width: 768px) {
            .my-lineup-section, .opponent-lineup-section {
                min-width: 100%;
                max-width: 100%;
            }
        }
