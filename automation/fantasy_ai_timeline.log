2025-09-04 22:05:19,795 - INFO - 🚀 Starting pre-waiver automation with claude
2025-09-04 22:05:20,697 - INFO - Executing: uv run python cli.py pre-waiver --ai claude
2025-09-04 22:05:55,825 - INFO - ✅ pre-waiver completed successfully
2025-09-04 22:05:55,826 - INFO - 📁 Analysis saved to automation/analysis_archive/pre-waiver_claude_week_1_20250904_220555.md
2025-09-04 22:05:55,826 - INFO - 📡 Sending analysis via webhook...
2025-09-04 22:05:55,828 - ERROR - ❌ Webhook request failed: name 'requests' is not defined
2025-09-04 22:05:55,828 - ERROR - ❌ Webhook failed
2025-09-04 22:06:11,593 - INFO - 🚀 Starting pre-waiver automation with claude
2025-09-04 22:06:12,561 - INFO - Executing: uv run python cli.py pre-waiver --ai claude
2025-09-04 22:06:43,917 - INFO - ✅ pre-waiver completed successfully
2025-09-04 22:06:43,918 - INFO - 📁 Analysis saved to automation/analysis_archive/pre-waiver_claude_week_1_20250904_220643.md
2025-09-04 22:06:43,918 - INFO - 📡 Sending analysis via webhook...
2025-09-04 22:06:47,030 - INFO - ✅ Successfully sent pre-waiver analysis to webhook
2025-09-04 22:06:47,032 - INFO - ✅ Webhook sent successfully
2025-09-04 22:06:47,032 - INFO - 🎉 pre-waiver automation completed successfully!
2025-09-04 22:09:15,007 - INFO - 🚀 Starting post-waiver automation with claude
2025-09-04 22:09:16,017 - INFO - Executing: uv run python cli.py post-waiver --ai claude
2025-09-04 22:11:19,841 - INFO - 🚀 Starting pre-waiver automation with claude
2025-09-04 22:11:20,709 - INFO - Executing: uv run python cli.py pre-waiver --ai claude
2025-09-04 22:12:03,290 - INFO - ✅ pre-waiver completed successfully
2025-09-04 22:12:03,291 - INFO - 📡 Sending analysis via webhook...
2025-09-04 22:12:06,034 - INFO - ✅ Successfully sent pre-waiver analysis to webhook
2025-09-04 22:12:06,038 - INFO - ✅ Webhook sent successfully
2025-09-04 22:12:06,038 - INFO - 🎉 pre-waiver automation completed successfully!
