#!/usr/bin/env python3
"""
Generate and Send Fantasy AI HTML Reports via Webhook

This script automates the generation of HTML reports (waiver wire, start/sit)
and sends them using the webhook sender. It's designed to be called by a scheduler
like cron.
"""

import argparse
import sys
import os
import logging
from datetime import datetime

# Add the project root to the Python path to allow for absolute imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from src.fantasy_manager import FantasyManager
from automation.webhook_sender import send_report_webhook

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def main():
    """Main function to generate and send reports."""
    parser = argparse.ArgumentParser(description="Generate and send Fantasy AI HTML reports.")
    parser.add_argument(
        "--report-type",
        required=True,
        choices=['waiver-wire', 'start-sit', 'post-week-analysis'],
        help="Type of report to generate and send."
    )
    parser.add_argument("--week", type=int, help="NFL week number. Defaults to the current week.")
    parser.add_argument("--year", type=int, help="Season year. Defaults to the current year.")

    args = parser.parse_args()

    try:
        logging.info("Initializing FantasyManager...")
        fm = FantasyManager(year=args.year) # Year can be passed here
        
        # If week is not provided, use the current week from the league object
        week = args.week or fm.league.current_week
        year = fm.year # Get the year from the manager

        html_content = ""
        report_name = ""

        if args.report_type == 'waiver-wire':
            logging.info(f"Generating waiver wire report for Week {week}...")
            html_content = fm.generate_weekly_html_report(week=week)
            report_name = "Waiver Wire Cheatsheet"
        elif args.report_type == 'start-sit':
            logging.info(f"Generating start/sit report for Week {week}...")
            html_content = fm.generate_start_sit_html_report(week=week)
            report_name = "Start/Sit Cheatsheet"
        elif args.report_type == 'post-week-analysis':
            logging.info(f"Generating post-week analysis for Week {week}...")
            
            # Use the existing CLI postweek command to generate HTML
            import subprocess
            import tempfile
            
            # For post-week analysis, analyze the previous week (likely completed)
            analysis_week = max(1, week - 1)
            logging.info(f"Analyzing completed Week {analysis_week}")
            
            try:
                # Create temporary file for HTML output
                with tempfile.NamedTemporaryFile(mode='w+', suffix='.html', delete=False) as temp_file:
                    temp_path = temp_file.name
                
                # Run the existing postweek CLI command
                cmd = [
                    'uv', 'run', 'python', 'cli.py', 'postweek',
                    '--week', str(analysis_week),
                    '--html',
                    '--sources', 'espn', 'fanduel', 'wwo',
                    '--save', temp_path.replace('.html', '.json')  # Also save JSON
                ]
                
                logging.info(f"Running command: {' '.join(cmd)}")
                
                result = subprocess.run(
                    cmd,
                    cwd=project_root,
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 minute timeout
                )
                
                if result.returncode == 0:
                    # The CLI postweek command generates HTML through its normal flow
                    # We need to find and read the generated HTML file
                    
                    # The CLI should generate an HTML file - check for it
                    html_files = []
                    for file in os.listdir(project_root):
                        if file.startswith('postweek_analysis') and file.endswith('.html'):
                            html_files.append(file)
                    
                    if html_files:
                        # Use the most recent HTML file
                        latest_html = sorted(html_files)[-1]
                        html_path = os.path.join(project_root, latest_html)
                        
                        with open(html_path, 'r', encoding='utf-8') as f:
                            html_content = f.read()
                        
                        logging.info(f"Successfully read generated HTML report: {latest_html}")
                        
                        # Clean up the generated file
                        try:
                            os.unlink(html_path)
                        except:
                            pass
                    else:
                        # Fallback: create simple HTML wrapper around CLI output
                        cli_output = result.stdout
                        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Week {analysis_week} Post-Game Analysis</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ text-align: center; color: #2c3e50; margin-bottom: 30px; }}
        .content {{ background: #f8f9fa; padding: 20px; border-radius: 8px; }}
        pre {{ background: #ffffff; padding: 15px; border-left: 4px solid #007bff; overflow-x: auto; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Week {analysis_week} Post-Game Analysis</h1>
        <h2>Fantasy AI Comprehensive Report</h2>
        <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    <div class="content">
        <pre>{cli_output}</pre>
    </div>
    <div style="text-align: center; margin-top: 30px; color: #666;">
        <p>🤖 Generated by Fantasy AI Post-Week Analysis</p>
    </div>
</body>
</html>
                        """
                    
                    report_name = f"Week {analysis_week} Post-Game Analysis"
                    week = analysis_week  # Update week for webhook
                    
                else:
                    logging.error(f"CLI postweek command failed: {result.stderr}")
                    raise Exception(f"CLI command failed with return code {result.returncode}")
                    
                # Cleanup temp file
                try:
                    os.unlink(temp_path)
                except:
                    pass
                    
            except subprocess.TimeoutExpired:
                logging.error("CLI postweek command timed out after 5 minutes")
                raise Exception("Post-week analysis timed out")
            except Exception as e:
                logging.error(f"Error running CLI postweek command: {e}")
                raise

        if not html_content:
            logging.error("HTML content is empty. Report generation may have failed.")
            sys.exit(1)

        logging.info("Report generated successfully. Sending via webhook...")

        success = send_report_webhook(
            week=week,
            year=year,
            report_type=args.report_type,
            html_content=html_content
        )

        if success:
            logging.info(f"✅ Successfully sent '{report_name}' for Week {week}.")
            sys.exit(0)
        else:
            logging.error(f"❌ Failed to send '{report_name}' for Week {week}.")
            sys.exit(1)

    except Exception as e:
        logging.error(f"An unexpected error occurred: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
