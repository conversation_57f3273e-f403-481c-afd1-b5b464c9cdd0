#!/bin/bash

# Setup Local Cron Jobs for Fantasy AI Automation
# Run this script to install automated scheduling on your Mac

set -e

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
AUTOMATION_DIR="$PROJECT_DIR/automation"
SCHEDULER_SCRIPT="$AUTOMATION_DIR/local_scheduler.py"

echo "🤖 Setting up Fantasy AI Local Automation"
echo "Project Directory: $PROJECT_DIR"

# Create automation directory and log file
mkdir -p "$AUTOMATION_DIR"
touch "$AUTOMATION_DIR/fantasy_ai.log"

# Make scheduler executable
chmod +x "$SCHEDULER_SCRIPT"

# Create cron job entries
CRON_ENTRIES="# Fantasy AI Automation - Installed $(date)
# Capture projections: Thursday 6 PM ET (before first game)
0 18 * * 4 cd $PROJECT_DIR && /usr/bin/python3 $SCHEDULER_SCRIPT capture >> $AUTOMATION_DIR/cron.log 2>&1

# Post-week analysis: Tuesday 8 AM ET (after Monday Night Football)
0 8 * * 2 cd $PROJECT_DIR && /usr/bin/python3 $SCHEDULER_SCRIPT analyze >> $AUTOMATION_DIR/cron.log 2>&1

# Weekly status check: Sunday 6 PM ET (before waivers)
0 18 * * 0 cd $PROJECT_DIR && /usr/bin/python3 $SCHEDULER_SCRIPT status >> $AUTOMATION_DIR/cron.log 2>&1"

# Backup existing crontab
echo "📋 Backing up existing crontab..."
crontab -l > "$AUTOMATION_DIR/crontab_backup_$(date +%Y%m%d_%H%M%S).txt" 2>/dev/null || echo "No existing crontab found"

# Install new cron jobs
echo "⚙️  Installing Fantasy AI cron jobs..."
(crontab -l 2>/dev/null || true; echo "$CRON_ENTRIES") | crontab -

echo "✅ Fantasy AI automation installed successfully!"
echo ""
echo "📅 Scheduled Jobs:"
echo "   Thursday 6 PM ET: Capture projections"
echo "   Sunday 6 PM ET:   Weekly status & waiver analysis"  
echo "   Tuesday 8 AM ET:  Post-week analysis"
echo ""
echo "📁 Logs will be written to:"
echo "   $AUTOMATION_DIR/fantasy_ai.log"
echo "   $AUTOMATION_DIR/cron.log"
echo ""
echo "🔧 To view installed cron jobs:"
echo "   crontab -l"
echo ""
echo "🗑️  To remove automation:"
echo "   crontab -r"
echo ""
echo "⚠️  Important: Keep your computer on during scheduled times"
echo "   or consider cloud deployment for 24/7 reliability"