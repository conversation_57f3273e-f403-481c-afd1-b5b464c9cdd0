INFO:src.enhanced_projection_storage:ESPN League initialized successfully.
INFO:src.enhanced_projection_storage:Enhanced projection storage initialized at data/projections_analytics.duckdb
INFO:src.projection_storage:Loaded 156 stored projections for week 1
INFO:src.post_week_analyzer:Starting post-week analysis for Week 1
INFO:src.post_week_analyzer:Analysis complete: 301 players analyzed
INFO:src.post_week_analyzer:Start/sit analysis complete for 10 teams
INFO:automation.webhook_sender:Sending webhook: post-week-analysis report for Week 1
INFO:automation.webhook_sender:✅ Webhook sent successfully: post-week-analysis report for Week 1

☘ ═══════════════════════════════════════════════════════════════════
   FantasyAI - AI-Powered Fantasy Football Analysis & Strategy
   Timeline-based workflow with enhanced projection analytics  
═══════════════════════════════════════════════════════════════════ ☘

⏳ Initializing Fantasy Manager...
🏈 League: Fairhope Father/Kids  | Year: 2025 | Week: 2

🏈 Post-Week Analysis for Week 1
==================================================
🔍 Analyzing projection accuracy...
✅ Found stored projections for Week 1
🎯 Analyzing strategic decisions for Week 1...
⚠️  No pre-game analysis files found for strategic validation
📋 Manual prompt saved: post_week_prompt_week_1.md
💡 Copy to your preferred AI for post-week analysis
Warning: Template rendering failed (integer division or modulo by zero), using fallback HTML
✅ HTML report generated: postweek_analysis_week_1.html
📧 Email sent successfully for Week 1
🗑️  Cleaned up local file: postweek_analysis_week_1.html
