#!/usr/bin/env python3
"""
AI Timeline Automation for Fantasy Football Analysis

Integrates the new timeline-based AI analysis commands (pre-waiver, post-waiver, pre-game)
with the existing Google Apps Script webhook system for automated email distribution.
"""

import argparse
import sys
import os
import logging
import requests
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from src.fantasy_manager import FantasyManager
from automation.webhook_sender import FantasyWebhookSender
import json

logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('automation/fantasy_ai_timeline.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_webhook_config():
    """Load webhook configuration"""
    config_path = Path(__file__).parent / "webhook_config.json"
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Failed to load webhook config: {e}")
        return None

def send_ai_analysis_webhook(
    analysis_type: str,
    week: int, 
    year: int,
    ai_model: str,
    analysis_content: str,
    team_name: str
) -> bool:
    """Send AI analysis via webhook using existing HTML report handler"""
    
    config = load_webhook_config()
    if not config:
        logger.error("No webhook configuration found")
        return False
    
    # Create HTML formatted analysis report (avoiding emoji encoding issues)
    html_content = f"""
    <html>
    <head>
        <title>{analysis_type.title().replace('-', ' ')} Analysis - Week {week}</title>
        <style>
            body {{ font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }}
            .header {{ background-color: #1f4e79; color: white; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
            .meta {{ background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 20px; }}
            .analysis {{ line-height: 1.6; }}
            .analysis h2 {{ color: #1f4e79; border-bottom: 2px solid #1f4e79; padding-bottom: 5px; }}
            .analysis h3 {{ color: #2c5282; }}
            .footer {{ margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }}
            .football {{ color: #d97706; font-weight: bold; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1><span class="football">[FOOTBALL]</span> {analysis_type.title().replace('-', ' ')} Analysis</h1>
            <h2>Week {week} &bull; {team_name}</h2>
        </div>
        
        <div class="meta">
            <strong>AI Model:</strong> {ai_model.title()}<br>
            <strong>Generated:</strong> {datetime.now().strftime('%B %d, %Y at %I:%M %p')}<br>
            <strong>Season:</strong> {year}
        </div>
        
        <div class="analysis">
            <pre style="white-space: pre-wrap; font-family: inherit;">{analysis_content}</pre>
        </div>
        
        <div class="footer">
            Generated by Fantasy AI Timeline System &bull; Powered by Claude Code
        </div>
    </body>
    </html>
    """
    
    # Use existing HTML report webhook format
    payload = {
        'type': 'html_report',
        'week': week,
        'year': year,
        'report_type': f"{analysis_type}-{ai_model}",
        'html_content': html_content,
        'secret': config['secret_key']
    }
    
    try:
        response = requests.post(
            config['webhook_url'],
            json=payload,
            timeout=30,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            logger.info(f"✅ Successfully sent {analysis_type} analysis to webhook")
            return True
        else:
            logger.error(f"❌ Webhook failed with status {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Webhook request failed: {e}")
        return False

def run_ai_timeline_command(command: str, ai_model: str = "claude") -> tuple[bool, str]:
    """Run AI timeline command and capture output"""
    
    import subprocess
    import tempfile
    
    try:
        # Run the CLI command and capture output
        cmd = f"uv run python cli.py {command} --ai {ai_model}"
        logger.info(f"Executing: {cmd}")
        
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True,
            cwd=project_root,
            timeout=300  # 5 minute timeout
        )
        
        if result.returncode == 0:
            logger.info(f"✅ {command} completed successfully")
            return True, result.stdout
        else:
            logger.error(f"❌ {command} failed: {result.stderr}")
            return False, result.stderr
            
    except subprocess.TimeoutExpired:
        logger.error(f"❌ {command} timed out after 5 minutes")
        return False, "Command timed out"
    except Exception as e:
        logger.error(f"❌ Error running {command}: {e}")
        return False, str(e)

def main():
    """Main automation function"""
    parser = argparse.ArgumentParser(description="AI Timeline Fantasy Analysis Automation")
    parser.add_argument(
        "command", 
        choices=['pre-waiver', 'post-waiver', 'pre-game'],
        help="Timeline analysis command to run"
    )
    parser.add_argument(
        "--ai-model",
        choices=['claude', 'gemini'],
        default='claude',
        help="AI model to use for analysis"
    )
    parser.add_argument("--week", type=int, help="NFL week number")
    parser.add_argument("--send-webhook", action="store_true", help="Send results via webhook")
    parser.add_argument("--save-local", action="store_true", help="Save analysis locally")
    
    args = parser.parse_args()
    
    try:
        logger.info(f"🚀 Starting {args.command} automation with {args.ai_model}")
        
        # Initialize Fantasy Manager to get team info
        try:
            fm = FantasyManager()
            team_name = fm.get_my_team().team_name
            week = args.week or fm.league.current_week
            year = fm.year
        except Exception as e:
            logger.warning(f"Could not initialize FantasyManager: {e}")
            team_name = "Fantasy Team"
            week = args.week or 1
            year = 2025
        
        # Run the AI analysis command
        success, output = run_ai_timeline_command(args.command, args.ai_model)
        
        if not success:
            logger.error(f"Analysis command failed: {output}")
            sys.exit(1)
        
        # Extract AI insights from output, excluding debug/system output
        ai_insights = ""
        lines = output.split('\n')
        capturing = False
        
        for line in lines:
            # Skip system output lines
            if any(skip_marker in line for skip_marker in [
                "🔄 Initializing", "📊 League:", "🗓️", "📅", "🔍 Analyzing", 
                "Fetching", "Loading", "Successfully fetched", "Applying strategic",
                "Getting", "🤖 Sending", "🚀 Running:", "📄 Analysis saved"
            ]):
                continue
            
            # Start capturing after AI analysis headers
            if any(marker in line for marker in [
                "AI Analysis Results", "CLAUDE", "AI Pre-Game Analysis", 
                "🎯 CLAUDE", "🧠 AI Analysis"
            ]):
                capturing = True
                continue
            elif capturing and "=" * 20 in line:
                # Skip separator lines
                continue
            elif capturing:
                ai_insights += line + "\n"
        
        # Clean up the insights
        ai_insights = ai_insights.strip()
        if not ai_insights:
            # Fallback: try to get everything after the last system message
            for i, line in enumerate(reversed(lines)):
                if any(skip_marker in line for skip_marker in [
                    "🤖 Sending", "🚀 Running:", "📄 Analysis saved"
                ]):
                    # Take everything after this line
                    ai_insights = "\n".join(lines[len(lines)-i:]).strip()
                    break
            
        if not ai_insights:
            ai_insights = "Analysis completed - check local files for details"
        
        # Save locally if requested
        if args.save_local:
            filename = f"automation/analysis_{args.command}_{args.ai_model}_week_{week}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            os.makedirs("automation/analysis_archive", exist_ok=True)
            archive_filename = f"automation/analysis_archive/{args.command}_{args.ai_model}_week_{week}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            
            with open(archive_filename, 'w') as f:
                f.write(f"# {args.command.title()} Analysis - Week {week}\n\n")
                f.write(f"**AI Model**: {args.ai_model}\n")
                f.write(f"**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"**Team**: {team_name}\n\n")
                f.write("## Analysis Output\n\n")
                f.write(ai_insights)
            
            logger.info(f"📁 Analysis saved to {archive_filename}")
        
        # Send via webhook if requested
        if args.send_webhook:
            logger.info("📡 Sending analysis via webhook...")
            webhook_success = send_ai_analysis_webhook(
                analysis_type=args.command,
                week=week,
                year=year,
                ai_model=args.ai_model,
                analysis_content=ai_insights,
                team_name=team_name
            )
            
            if webhook_success:
                logger.info("✅ Webhook sent successfully")
            else:
                logger.error("❌ Webhook failed")
                sys.exit(1)
        
        logger.info(f"🎉 {args.command} automation completed successfully!")
        
    except Exception as e:
        logger.error(f"💥 Automation failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()