#!/usr/bin/env python3
"""
Local Automation Scheduler for Fantasy AI

Handles both projection capture and post-week analysis with intelligent timing.
Designed to run via cron jobs on your local machine.
"""

import os
import sys
import subprocess
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(project_root / 'automation' / 'fantasy_ai.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def run_cli_command(command: list, description: str) -> bool:
    """Run a CLI command and return success status"""
    try:
        logger.info(f"Starting: {description}")
        
        # Change to project directory
        result = subprocess.run(
            ['uv', 'run', 'python'] + command,
            cwd=project_root,
            capture_output=True,
            text=True,
            timeout=600  # 10 minute timeout
        )
        
        if result.returncode == 0:
            logger.info(f"✅ Success: {description}")
            if result.stdout:
                logger.info(f"Output: {result.stdout[:500]}...")  # First 500 chars
            return True
        else:
            logger.error(f"❌ Failed: {description}")
            logger.error(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"⏰ Timeout: {description}")
        return False
    except Exception as e:
        logger.error(f"💥 Exception: {description} - {e}")
        return False

def capture_projections():
    """Capture projections before games start (Thursday evening)"""
    logger.info("🔥 PROJECTION CAPTURE - Thursday Evening")
    
    # Capture all available sources
    success = run_cli_command(
        ['cli.py', 'capture', '--sources', 'espn', 'fanduel', 'wwo', '--force'],
        'Capturing weekly projections'
    )
    
    if success:
        # Send notification email
        run_cli_command(
            ['cli.py', 'email', 'pre-waiver'],
            'Sending pre-waiver email with projections captured'
        )
        logger.info("📧 Pre-waiver email sent")
    
    return success

def analyze_completed_week():
    """Analyze completed week performance (Tuesday morning)"""
    logger.info("📊 POST-WEEK ANALYSIS - Tuesday Morning")
    
    # Run comprehensive post-week analysis
    timestamp = datetime.now().strftime("%Y%m%d_%H%M")
    
    success = run_cli_command(
        ['cli.py', 'postweek', '--html', '--sources', 'espn', 'fanduel', 'wwo', 
         '--save', f'analysis_{timestamp}.json'],
        'Running post-week projection accuracy analysis'
    )
    
    if success:
        # Send post-waiver summary
        run_cli_command(
            ['cli.py', 'email', 'post-waiver'],
            'Sending post-week analysis email'
        )
        logger.info("📧 Post-week analysis email sent")
    
    return success

def weekly_status_check():
    """Weekly status check and waiver analysis (Sunday evening)"""
    logger.info("📋 WEEKLY STATUS CHECK - Sunday Evening")
    
    # Generate comprehensive waiver analysis
    success = run_cli_command(
        ['cli.py', 'waiver', '--size', '150'],
        'Generating weekly waiver wire analysis'
    )
    
    # Quick team status
    run_cli_command(
        ['cli.py', 'status', '--verbose'],
        'Checking team status'
    )
    
    return success

def main():
    """Main automation handler based on command line argument"""
    if len(sys.argv) != 2:
        print("Usage: python local_scheduler.py [capture|analyze|status]")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    logger.info(f"🤖 Fantasy AI Automation Starting: {command}")
    logger.info(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if command == 'capture':
        success = capture_projections()
    elif command == 'analyze':
        success = analyze_completed_week()
    elif command == 'status':
        success = weekly_status_check()
    else:
        logger.error(f"Unknown command: {command}")
        sys.exit(1)
    
    if success:
        logger.info("🎉 Automation completed successfully")
        sys.exit(0)
    else:
        logger.error("💥 Automation failed")
        sys.exit(1)

if __name__ == "__main__":
    main()