# 🤖 Fantasy AI Automation Setup Guide

This guide helps you set up automated Fantasy AI reports using **Google Apps Script + Local Cron Jobs**.

## 🎯 What You'll Get

- **📊 Tuesday Morning**: Post-week analysis emailed to you automatically  
- **📸 Thursday Evening**: Projections captured before games start
- **📋 Google Sheets**: Historical data storage and tracking
- **📧 Gmail**: Professional email reports sent directly to your inbox

---

## 🚀 Quick Setup (15 minutes)

### Step 1: Set up Google Apps Script

1. **Go to [Google Apps Script](https://script.google.com)**

2. **Create a New Project**
   - Click "New Project"
   - Name it "Fantasy AI Webhook"

3. **Replace the default code** with the contents of `automation/google_apps_script.js`

4. **Update Configuration** (lines 12-16):
   ```javascript
   const CONFIG = {
     SHEET_ID: 'YOUR_GOOGLE_SHEET_ID_HERE', 
     EMAIL_RECIPIENT: '<EMAIL>',
     WEBHOOK_SECRET: 'your-unique-secret-key-2025'
   };
   ```

5. **Create a Google Sheet**:
   - Go to [Google Sheets](https://sheets.google.com)
   - Create a new sheet named "Fantasy AI Data"  
   - Copy the Sheet ID from the URL: `https://docs.google.com/spreadsheets/d/SHEET_ID_HERE/edit`
   - Paste this ID into the `SHEET_ID` config above

6. **Deploy the Web App**:
   - Click "Deploy" → "New Deployment"
   - Type: "Web app"
   - Execute as: "Me" 
   - Access: "Anyone"
   - Click "Deploy"
   - **Copy the Web App URL** - you'll need this!

7. **Test the Setup**:
   - In Apps Script, run the `testWebhook()` function
   - Check your email and Google Sheet for test data

### Step 2: Configure Local Webhook

1. **Create webhook config file**:
   ```bash
   cd /path/to/fantasy-ai/automation
   python webhook_sender.py  # Creates sample config
   ```

2. **Edit `automation/webhook_config.json`**:
   ```json
   {
     "webhook_url": "https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec",
     "secret_key": "your-unique-secret-key-2025"
   }
   ```
   Use the Web App URL from Step 1.6 and the same secret key from Step 1.4

### Step 3: Set up Automated Scheduling

**Option A: Simple Cron Jobs**
```bash
# Edit your crontab
crontab -e

# Add these lines (adjust paths to match your system):
# Capture projections: Thursday 6 PM ET
0 18 * * 4 cd /path/to/fantasy-ai && uv run python cli.py capture --sources espn fanduel

# Post-week analysis: Tuesday 8 AM ET  
0 8 * * 2 cd /path/to/fantasy-ai && uv run python cli.py postweek --html

# Weekly status: Sunday 6 PM ET
0 18 * * 0 cd /path/to/fantasy-ai && uv run python cli.py quick --top 5
```

**Option B: Use our setup script**:
```bash
cd automation
chmod +x setup_local_cron.sh
./setup_local_cron.sh
```

---

## 📅 Automation Schedule

| Day | Time | Action | What It Does |
|-----|------|--------|-------------|
| **Thursday** | 6 PM ET | Projection Capture | Captures ESPN/FanDuel projections before games |
| **Sunday** | 6 PM ET | Weekly Status | Sends waiver wire recommendations |
| **Tuesday** | 8 AM ET | Post-Week Analysis | Analyzes accuracy, sends your team performance |

---

## 📧 Email Examples

### Thursday - Projection Capture
```
🏈 Projections Captured - Week 3

Fantasy AI has successfully captured projections for Week 3!

📊 Capture Summary:
• Total Projections: 528  
• Sources: ESPN, FanDuel
• ESPN: 200 players
• FanDuel: 328 players

⏰ Captured: Thu, Oct 5 2023 6:00 PM

You're all set for accurate post-week analysis on Tuesday! 🎯
```

### Tuesday - Post-Week Analysis  
```
📊 Week 3 Analysis - BEAT Projections! 🎉

🏈 Fantasy AI Week 3 Post-Game Analysis

📊 LEAGUE-WIDE RESULTS:
• Players Analyzed: 528
• Most Accurate Source: FanDuel  
• Overall Accuracy: 4.2 points MAE

💥 League Biggest Boom: Puka Nacua (+16.3 points)
💸 League Biggest Bust: Austin Ekeler (-15.1 points)

🏈 YOUR TEAM PERFORMANCE: Mid Rustlers
🎉 BEAT PROJECTIONS!

📊 Team Summary:
• Projected: 127.4 points
• Actual: 132.8 points
• Difference: +5.4 points  

⭐ Your Best: Puka Nacua (+16.3)
💔 Your Worst: Christian McCaffrey (-17.3)

📈 View detailed analysis in your Google Sheet
```

---

## 🛠️ Troubleshooting

### No emails received?
1. Check Google Apps Script logs
2. Verify webhook URL in config file
3. Test webhook manually: `python automation/webhook_sender.py`

### Cron jobs not running?
1. Check cron is running: `ps aux | grep cron`
2. View cron logs: `tail -f /var/log/cron` (Linux) or `log show --predicate 'process == "cron"'` (Mac)
3. Ensure full paths in crontab entries

### Projection capture failing?
1. Test manually: `uv run python cli.py capture`
2. Check your ESPN credentials are still valid
3. Verify internet connection during capture time

---

## 🔧 Advanced Configuration

### Change Email Format
Edit the email templates in `google_apps_script.js` around lines 150+ 

### Add More Data to Sheets
Modify the `ensureHeaders()` calls in Apps Script to include additional columns

### Different Schedule
Adjust the cron times - remember to account for your timezone vs ET

### Multiple Email Recipients  
Update `CONFIG.EMAIL_RECIPIENT` to include multiple emails: `'<EMAIL>,<EMAIL>'`

---

## ⚡ Pro Tips

1. **Keep your computer on** during scheduled times, or consider cloud deployment
2. **Test everything manually first** before relying on automation
3. **Check your Google Sheet** weekly to ensure data is being stored
4. **Monitor the logs** in `automation/fantasy_ai.log` for any issues
5. **Backup your webhook config** - store it securely

---

## 🆘 Support

If you run into issues:
1. Check the logs: `tail -f automation/fantasy_ai.log`
2. Test components individually: capture, postweek, webhook
3. Review Google Apps Script execution logs
4. Verify all API credentials are current

Your Fantasy AI system is now automated! 🎉