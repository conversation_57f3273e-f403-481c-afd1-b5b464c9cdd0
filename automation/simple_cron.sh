#!/bin/bash

# Simple Fantasy AI Cron Setup
# This creates basic cron jobs for Fantasy AI automation

set -e

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/." && pwd)"

echo "🤖 Setting up Simple Fantasy AI Cron Jobs"
echo "Project Directory: $PROJECT_DIR"

# Verify uv and Python are available
if ! command -v uv &> /dev/null; then
    echo "❌ Error: 'uv' command not found. Please install uv first."
    exit 1
fi

# Create cron entries
CRON_ENTRIES="
# Fantasy AI Automation - Simple Setup
# Capture projections: Thursday 6 PM ET (before TNF)
0 18 * * 4 cd $PROJECT_DIR && /usr/local/bin/uv run python cli.py capture --sources espn fanduel >> $PROJECT_DIR/automation/cron.log 2>&1

# Post-week analysis: Tuesday 8 AM ET (after MNF)  
0 8 * * 2 cd $PROJECT_DIR && /usr/local/bin/uv run python cli.py postweek --html >> $PROJECT_DIR/automation/cron.log 2>&1

# Weekly status: Sunday 6 PM ET (before waivers)
0 18 * * 0 cd $PROJECT_DIR && /usr/local/bin/uv run python cli.py quick --top 5 >> $PROJECT_DIR/automation/cron.log 2>&1
"

# Backup existing crontab
echo "📋 Backing up existing crontab..."
crontab -l > "$PROJECT_DIR/automation/crontab_backup_$(date +%Y%m%d_%H%M%S).txt" 2>/dev/null || echo "No existing crontab found"

# Install cron jobs
echo "⚙️  Installing Fantasy AI cron jobs..."
(crontab -l 2>/dev/null || true; echo "$CRON_ENTRIES") | crontab -

# Create log file
mkdir -p "$PROJECT_DIR/automation"
touch "$PROJECT_DIR/automation/cron.log"

echo "✅ Simple cron jobs installed!"
echo ""
echo "📅 Schedule:"  
echo "   Thursday 6 PM ET: Capture projections"
echo "   Sunday 6 PM ET:   Weekly status check"
echo "   Tuesday 8 AM ET:  Post-week analysis" 
echo ""
echo "📁 Logs: $PROJECT_DIR/automation/cron.log"
echo ""
echo "🔧 View current cron jobs: crontab -l"
echo "📧 Set up webhook for email: see automation/SETUP.md"
echo ""
echo "⚠️  Important: Keep your computer on during scheduled times!"

# Test webhook config
if [ -f "$PROJECT_DIR/automation/webhook_config.json" ]; then
    echo "✅ Webhook config found - emails will be sent"
else
    echo "⚠️  No webhook config found - create automation/webhook_config.json for email reports"
    echo "   See automation/SETUP.md for instructions"
fi