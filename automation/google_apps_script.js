/**
 * Fantasy AI Google Apps Script Integration
 * 
 * This script runs in Google Apps Script to:
 * 1. Receive webhook data from your local Fantasy AI system
 * 2. Store analysis results in Google Sheets for historical tracking
 * 3. Send formatted email reports via Gmail
 * 4. Handle both projection capture and post-week analysis
 */

// Configuration - Update these values
const CONFIG = {
  SHEET_ID: 'YOUR_GOOGLE_SHEET_ID_HERE', // Create a new Google Sheet and get its ID
  EMAIL_RECIPIENT: '<EMAIL>', // Your email address
  WEBHOOK_SECRET: 'fantasy-ai-secret-key-2025' // Change this to something secure
};

// Sheet names for different data types
const SHEETS = {
  PROJECTIONS: 'Projection Captures',
  POST_WEEK: 'Post-Week Analysis', 
  TEAM_PERFORMANCE: 'Team Performance',
  LOG: 'Automation Log'
};

/**
 * Main webhook endpoint - handles POST requests from local system
 */
function doPost(e) {
  try {
    Logger.log('Webhook received: ' + new Date());
    
    // Parse the incoming data
    const data = JSON.parse(e.postData.contents);
    
    // Verify webhook secret for security
    if (data.secret !== CONFIG.WEBHOOK_SECRET) {
      Logger.log('Invalid webhook secret');
      return ContentService.createTextOutput('Unauthorized').setMimeType(ContentService.MimeType.TEXT);
    }
    
    // Route to appropriate handler based on type
    let result;
    switch (data.type) {
      case 'projection_capture':
        result = handleProjectionCapture(data);
        break;
      case 'post_week_analysis':
        result = handlePostWeekAnalysis(data);
        break;
      case 'weekly_status':
        result = handleWeeklyStatus(data);
        break;
      case 'html_report':
        result = handleHtmlReport(data);
        break;
      default:
        throw new Error('Unknown data type: ' + data.type);
    }
    
    // Log successful processing
    logActivity(data.type, 'SUCCESS', result.summary);
    
    return ContentService.createTextOutput('Success').setMimeType(ContentService.MimeType.TEXT);
    
  } catch (error) {
    Logger.log('Error processing webhook: ' + error.toString());
    logActivity('webhook_error', 'ERROR', error.toString());
    return ContentService.createTextOutput('Error: ' + error.toString()).setMimeType(ContentService.MimeType.TEXT);
  }
}

/**
 * Handle projection capture data
 */
function handleProjectionCapture(data) {
  Logger.log('Processing projection capture...');
  
  // Store in Sheets
  const sheet = getOrCreateSheet(SHEETS.PROJECTIONS);
  const headers = ['Timestamp', 'Week', 'Year', 'Sources', 'Total Projections', 'ESPN Count', 'FanDuel Count', 'WinWithOdds Count'];
  ensureHeaders(sheet, headers);
  
  const row = [
    new Date(),
    data.week,
    data.year,
    data.sources.join(', '),
    data.total_projections,
    data.source_counts.ESPN || 0,
    data.source_counts.FanDuel || 0,
    data.source_counts.WinWithOdds || 0
  ];
  
  sheet.appendRow(row);
  
  // Send email notification
  const subject = `Projections Captured - Week ${data.week}`;
  const body = `
Fantasy AI has successfully captured projections for Week ${data.week}!

Capture Summary:
• Total Projections: ${data.total_projections}
• Sources: ${data.sources.join(', ')}
• ESPN: ${data.source_counts.ESPN || 0} players
• FanDuel: ${data.source_counts.FanDuel || 0} players  
• WinWithOdds: ${data.source_counts.WinWithOdds || 0} players

⏰ Captured: ${new Date().toLocaleString()}

You're all set for accurate post-week analysis on Tuesday! 

---
Automated by Fantasy AI
  `;
  
  sendEmail(subject, body);
  
  return { summary: `Captured ${data.total_projections} projections for Week ${data.week}` };
}

/**
 * Handle post-week analysis data
 */
function handlePostWeekAnalysis(data) {
  Logger.log('Processing post-week analysis...');
  
  // Store overall analysis
  const analysisSheet = getOrCreateSheet(SHEETS.POST_WEEK);
  const analysisHeaders = [
    'Timestamp', 'Week', 'Year', 'Players Analyzed', 'Most Accurate Source', 
    'Least Accurate Source', 'Overall MAE', 'Overall RMSE',
    'Biggest Boom Player', 'Biggest Boom Points', 'Biggest Bust Player', 'Biggest Bust Points'
  ];
  ensureHeaders(analysisSheet, analysisHeaders);
  
  const analysisRow = [
    new Date(),
    data.week,
    data.year,
    data.total_players_analyzed,
    data.most_accurate_source,
    data.least_accurate_source,
    data.overall_mae,
    data.overall_rmse,
    data.biggest_boom.player_name,
    data.biggest_boom.difference,
    data.biggest_bust.player_name,
    data.biggest_bust.difference
  ];
  
  analysisSheet.appendRow(analysisRow);
  
  // Store team performance if available
  if (data.my_team_performance) {
    const teamSheet = getOrCreateSheet(SHEETS.TEAM_PERFORMANCE);
    const teamHeaders = [
      'Timestamp', 'Week', 'Year', 'Team Name', 'Total Projected', 'Total Actual', 
      'Difference', 'Players Analyzed', 'Best Performer', 'Best Performance', 
      'Worst Performer', 'Worst Performance', 'Lineup MAE', 'Bench MAE'
    ];
    ensureHeaders(teamSheet, teamHeaders);
    
    const team = data.my_team_performance;
    const teamRow = [
      new Date(),
      data.week,
      data.year,
      team.team_name,
      team.total_projected,
      team.total_actual,
      team.difference,
      team.players_analyzed,
      team.best_performer.player_name,
      team.best_performer.difference,
      team.worst_performer.player_name,
      team.worst_performer.difference,
      team.lineup_accuracy,
      team.bench_accuracy
    ];
    
    teamSheet.appendRow(teamRow);
  }
  
  // Send comprehensive email
  const subject = `Week ${data.week} Analysis - ${data.my_team_performance ? (data.my_team_performance.difference >= 0 ? 'BEAT Projections!' : 'Missed Projections ') : 'Complete'}`;
  
  let body = `
Fantasy AI Week ${data.week} Post-Game Analysis

LEAGUE-WIDE RESULTS:
• Players Analyzed: ${data.total_players_analyzed}
• Most Accurate Source: ${data.most_accurate_source}
• Overall Accuracy (MAE): ${data.overall_mae.toFixed(2)} points

League Biggest Boom: ${data.biggest_boom.player_name} (+${data.biggest_boom.difference.toFixed(1)} points)
League Biggest Bust: ${data.biggest_bust.player_name} (${data.biggest_bust.difference.toFixed(1)} points)
`;

  if (data.my_team_performance) {
    const team = data.my_team_performance;
    const result = team.difference >= 0 ? 'BEAT PROJECTIONS!' : 'MISSED PROJECTIONS';
    
    body += `

YOUR TEAM PERFORMANCE: ${team.team_name}
${result}

Team Summary:
• Projected: ${team.total_projected.toFixed(1)} points
• Actual: ${team.total_actual.toFixed(1)} points  
• Difference: ${team.difference >= 0 ? '+' : ''}${team.difference.toFixed(1)} points

Your Best: ${team.best_performer.player_name} (+${team.best_performer.difference.toFixed(1)})
Your Worst: ${team.worst_performer.player_name} (${team.worst_performer.difference.toFixed(1)})

📏 Accuracy: Lineup ${team.lineup_accuracy.toFixed(2)}, Bench ${team.bench_accuracy.toFixed(2)} MAE
`;
  }

  body += `

View detailed analysis and historical trends in your Google Sheet:
https://docs.google.com/spreadsheets/d/${CONFIG.SHEET_ID}

---
Automated by Fantasy AI • ${new Date().toLocaleString()}
  `;
  
  sendEmail(subject, body);
  
  return { summary: `Analyzed Week ${data.week} - ${data.total_players_analyzed} players` };
}

/**
 * Handle weekly status updates
 */
function handleWeeklyStatus(data) {
  Logger.log('Processing weekly status...');
  
  const subject = `Weekly Fantasy Status - Week ${data.week}`;
  const body = `
Fantasy AI Weekly Check-In

Team Status: ${data.team_name}
• Record: ${data.wins}-${data.losses}
• Points For: ${data.points_for}
• League Rank: ${data.rank}

Top Waiver Recommendations:
${data.top_recommendations.map(rec => `• ${rec.name} (${rec.position}) - ${rec.projected_points} proj`).join('\n')}

📅 This Week's Schedule:
• Wednesday: Waivers clear
• Thursday: First games + projection capture
• Sunday: Main slate
• Tuesday: Post-week analysis

---
Automated by Fantasy AI • ${new Date().toLocaleString()}
  `;
  
  sendEmail(subject, body);
  
  return { summary: `Weekly status for ${data.team_name}` };
}

/**
 * Handle HTML report data
 */
function handleHtmlReport(data) {
  Logger.log('Processing HTML report...');
  
  // Create a dynamic subject line
  const reportTitle = data.report_type.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  const subject = `${reportTitle} Report - Week ${data.week}`;
  
  const plain_text_body = `See attached HTML report for your ${data.report_type} analysis for week ${data.week}.`;
  
  // Send email with HTML body
  sendEmail(subject, plain_text_body, data.html_content);
  
  return { summary: `Sent ${data.report_type} report for Week ${data.week}` };
}

/**
 * Utility functions
 */
function getOrCreateSheet(sheetName) {
  const spreadsheet = SpreadsheetApp.openById(CONFIG.SHEET_ID);
  let sheet = spreadsheet.getSheetByName(sheetName);
  
  if (!sheet) {
    sheet = spreadsheet.insertSheet(sheetName);
    Logger.log(`Created new sheet: ${sheetName}`);
  }
  
  return sheet;
}

function ensureHeaders(sheet, headers) {
  const range = sheet.getRange(1, 1, 1, headers.length);
  const existingHeaders = range.getValues()[0];
  
  // Only set headers if the first cell is empty (new sheet)
  if (!existingHeaders[0]) {
    range.setValues([headers]);
    range.setFontWeight('bold');
    range.setBackground('#4285F4');
    range.setFontColor('white');
    Logger.log(`Set headers for sheet: ${sheet.getName()}`);
  }
}

function sendEmail(subject, body, htmlBody = null) {
  try {
    if (htmlBody) {
      // Send HTML email with plain text fallback
      GmailApp.sendEmail(
        CONFIG.EMAIL_RECIPIENT, 
        subject, 
        body,
        {
          htmlBody: htmlBody,
          attachments: []
        }
      );
    } else {
      // Send plain text email
      GmailApp.sendEmail(CONFIG.EMAIL_RECIPIENT, subject, body);
    }
    Logger.log(`Email sent: ${subject}`);
  } catch (error) {
    Logger.log(`Failed to send email: ${error.toString()}`);
    throw error;
  }
}

function logActivity(type, status, details) {
  try {
    const logSheet = getOrCreateSheet(SHEETS.LOG);
    const headers = ['Timestamp', 'Type', 'Status', 'Details'];
    ensureHeaders(logSheet, headers);
    
    const row = [new Date(), type, status, details];
    logSheet.appendRow(row);
  } catch (error) {
    Logger.log(`Failed to log activity: ${error.toString()}`);
  }
}

/**
 * Test function - you can run this manually to test the setup
 */
function testWebhook() {
  const testData = {
    secret: CONFIG.WEBHOOK_SECRET,
    type: 'projection_capture',
    week: 1,
    year: 2025,
    sources: ['ESPN', 'FanDuel'],
    total_projections: 450,
    source_counts: {
      ESPN: 200,
      FanDuel: 250
    }
  };
  
  const mockEvent = {
    postData: {
      contents: JSON.stringify(testData)
    }
  };
  
  const result = doPost(mockEvent);
  Logger.log('Test result: ' + result.getContent());
}