#!/usr/bin/env python3
"""
Webhook Sender for Fantasy AI Google Apps Script Integration

Sends analysis results to Google Apps Script via webhook for:
- Email notifications via Gmail
- Data storage in Google Sheets  
- Historical tracking and trends
"""

import requests
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
import os
from pathlib import Path
import argparse
import sys

logger = logging.getLogger(__name__)

class FantasyWebhookSender:
    """Sends Fantasy AI data to Google Apps Script webhook"""
    
    def __init__(self, webhook_url: str, secret_key: str):
        """
        Initialize webhook sender
        
        Args:
            webhook_url: Google Apps Script web app URL
            secret_key: Secret key for webhook authentication
        """
        self.webhook_url = webhook_url
        self.secret_key = secret_key
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'FantasyAI-Webhook/1.0'
        })
    
    def send_projection_capture(
        self, 
        week: int, 
        year: int,
        sources: list,
        total_projections: int,
        source_counts: Dict[str, int]
    ) -> bool:
        """
        Send projection capture notification
        
        Args:
            week: NFL week number
            year: Season year
            sources: List of projection sources captured
            total_projections: Total number of projections
            source_counts: Count by source (ESPN, FanDuel, etc.)
        """
        data = {
            'secret': self.secret_key,
            'type': 'projection_capture',
            'timestamp': datetime.now().isoformat(),
            'week': week,
            'year': year,
            'sources': sources,
            'total_projections': total_projections,
            'source_counts': source_counts
        }
        
        return self._send_webhook(data, f"projection capture for Week {week}")
    
    def send_post_week_analysis(
        self,
        week: int,
        year: int,
        analysis_summary: Dict[str, Any],
        team_performance: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Send post-week analysis results
        
        Args:
            week: NFL week number  
            year: Season year
            analysis_summary: Overall analysis results
            team_performance: Team-specific performance data
        """
        data = {
            'secret': self.secret_key,
            'type': 'post_week_analysis',
            'timestamp': datetime.now().isoformat(),
            'week': week,
            'year': year,
            'total_players_analyzed': analysis_summary.get('total_players_analyzed', 0),
            'most_accurate_source': analysis_summary.get('most_accurate_source', 'ESPN'),
            'least_accurate_source': analysis_summary.get('least_accurate_source', 'ESPN'), 
            'overall_mae': analysis_summary.get('overall_mae', 0.0),
            'overall_rmse': analysis_summary.get('overall_rmse', 0.0),
            'biggest_boom': {
                'player_name': analysis_summary.get('biggest_boom', {}).get('player_name', 'Unknown'),
                'difference': analysis_summary.get('biggest_boom', {}).get('difference', 0.0),
                'position': analysis_summary.get('biggest_boom', {}).get('position', 'Unknown')
            },
            'biggest_bust': {
                'player_name': analysis_summary.get('biggest_bust', {}).get('player_name', 'Unknown'), 
                'difference': analysis_summary.get('biggest_bust', {}).get('difference', 0.0),
                'position': analysis_summary.get('biggest_bust', {}).get('position', 'Unknown')
            }
        }
        
        if team_performance:
            data['my_team_performance'] = {
                'team_name': team_performance.get('team_name', 'My Team'),
                'total_projected': team_performance.get('total_projected', 0.0),
                'total_actual': team_performance.get('total_actual', 0.0),
                'difference': team_performance.get('difference', 0.0),
                'players_analyzed': team_performance.get('players_analyzed', 0),
                'lineup_players': team_performance.get('lineup_players', 0),
                'bench_players': team_performance.get('bench_players', 0),
                'best_performer': {
                    'player_name': team_performance.get('best_performer', {}).get('player_name', 'Unknown'),
                    'difference': team_performance.get('best_performer', {}).get('difference', 0.0),
                    'position': team_performance.get('best_performer', {}).get('position', 'Unknown')
                },
                'worst_performer': {
                    'player_name': team_performance.get('worst_performer', {}).get('player_name', 'Unknown'),
                    'difference': team_performance.get('worst_performer', {}).get('difference', 0.0),
                    'position': team_performance.get('worst_performer', {}).get('position', 'Unknown')
                },
                'lineup_accuracy': team_performance.get('lineup_accuracy', 0.0),
                'bench_accuracy': team_performance.get('bench_accuracy', 0.0)
            }
        
        return self._send_webhook(data, f"post-week analysis for Week {week}")
    
    def send_weekly_status(
        self,
        week: int,
        team_name: str,
        wins: int,
        losses: int,
        points_for: float,
        rank: int,
        top_recommendations: list
    ) -> bool:
        """
        Send weekly status update
        
        Args:
            week: NFL week number
            team_name: Fantasy team name
            wins: Season wins
            losses: Season losses  
            points_for: Total points scored
            rank: League ranking
            top_recommendations: Top waiver recommendations
        """
        data = {
            'secret': self.secret_key,
            'type': 'weekly_status',
            'timestamp': datetime.now().isoformat(),
            'week': week,
            'team_name': team_name,
            'wins': wins,
            'losses': losses,
            'points_for': points_for,
            'rank': rank,
            'top_recommendations': top_recommendations
        }
        
        return self._send_webhook(data, f"weekly status for Week {week}")

    def send_html_report(
        self,
        week: int,
        year: int,
        report_type: str,
        html_content: str
    ) -> bool:
        """
        Send HTML report content
        
        Args:
            week: NFL week number
            year: Season year
            report_type: Type of report (e.g., 'start_sit', 'waiver_wire')
            html_content: The HTML content of the report
        """
        data = {
            'secret': self.secret_key,
            'type': 'html_report',
            'timestamp': datetime.now().isoformat(),
            'week': week,
            'year': year,
            'report_type': report_type,
            'html_content': html_content
        }
        
        return self._send_webhook(data, f"{report_type} report for Week {week}")
    
    def _send_webhook(self, data: Dict[str, Any], description: str) -> bool:
        """
        Send webhook request to Google Apps Script
        
        Args:
            data: Data payload to send
            description: Description for logging
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Sending webhook: {description}")
            
            response = self.session.post(
                self.webhook_url,
                data=json.dumps(data),
                timeout=30
            )
            
            if response.status_code == 200 and 'Success' in response.text:
                logger.info(f"✅ Webhook sent successfully: {description}")
                return True
            else:
                logger.error(f"❌ Webhook failed: {description}")
                logger.error(f"Status: {response.status_code}, Response: {response.text}")
                return False
                
        except requests.exceptions.Timeout:
            logger.error(f"⏰ Webhook timeout: {description}")
            return False
        except requests.exceptions.RequestException as e:
            logger.error(f"🌐 Webhook network error: {description} - {e}")
            return False
        except Exception as e:
            logger.error(f"💥 Webhook unexpected error: {description} - {e}")
            return False

def load_webhook_config() -> Dict[str, str]:
    """Load webhook configuration from environment variables (.env file)"""
    config = {}
    
    # Load from environment variables (primary source)
    config['webhook_url'] = os.getenv('FANTASY_WEBHOOK_URL')
    config['secret_key'] = os.getenv('FANTASY_WEBHOOK_SECRET', 'fantasy-ai-secret-key-2025')
    
    # Fallback to config file only if env vars not set (deprecated)
    if not config['webhook_url']:
        config_file = Path(__file__).parent / 'webhook_config.json'
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    file_config = json.load(f)
                    config.update(file_config)
                logger.warning("Using deprecated webhook_config.json - please migrate to .env variables")
            except Exception as e:
                logger.warning(f"Could not load webhook config file: {e}")
        else:
            logger.info("No webhook configuration found. Set FANTASY_WEBHOOK_URL in your .env file for email delivery")
    
    return config

# Utility functions for CLI integration
def send_capture_webhook(week: int, year: int, sources: list, total: int, counts: Dict[str, int]) -> bool:
    """Helper function for capture webhook"""
    config = load_webhook_config()
    if not config.get('webhook_url'):
        logger.warning("No webhook URL configured, skipping webhook")
        return False
    
    sender = FantasyWebhookSender(config['webhook_url'], config['secret_key'])
    return sender.send_projection_capture(week, year, sources, total, counts)

def send_analysis_webhook(week: int, year: int, summary: dict, team: dict = None) -> bool:
    """Helper function for analysis webhook"""
    config = load_webhook_config()
    if not config.get('webhook_url'):
        logger.warning("No webhook URL configured, skipping webhook")
        return False
    
    sender = FantasyWebhookSender(config['webhook_url'], config['secret_key'])
    return sender.send_post_week_analysis(week, year, summary, team)

def send_status_webhook(week: int, team_name: str, wins: int, losses: int, points_for: float, rank: int, top_recommendations: list) -> bool:
    """Helper function for status webhook"""
    config = load_webhook_config()
    if not config.get('webhook_url'):
        logger.warning("No webhook URL configured, skipping webhook")
        return False
    
    sender = FantasyWebhookSender(config['webhook_url'], config['secret_key'])
    return sender.send_weekly_status(week, team_name, wins, losses, points_for, rank, top_recommendations)

def send_report_webhook(week: int, year: int, report_type: str, html_content: str) -> bool:
    """Helper function for report webhook"""
    config = load_webhook_config()
    if not config.get('webhook_url'):
        logger.warning("No webhook URL configured, skipping webhook")
        return False
    
    sender = FantasyWebhookSender(config['webhook_url'], config['secret_key'])
    return sender.send_html_report(week, year, report_type, html_content)

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    parser = argparse.ArgumentParser(description="Send data to Fantasy AI Google Apps Script webhook.")
    subparsers = parser.add_subparsers(dest="task", required=True, help="Task to perform")

    # Sub-parser for projection_capture
    capture_parser = subparsers.add_parser("projection-capture", help="Send projection capture notification")
    capture_parser.add_argument("--week", type=int, required=True, help="NFL week number")
    capture_parser.add_argument("--year", type=int, required=True, help="Season year")
    capture_parser.add_argument("--sources", nargs='+', required=True, help="List of projection sources")
    capture_parser.add_argument("--total-projections", type=int, required=True, help="Total number of projections")
    capture_parser.add_argument("--source-counts", type=json.loads, required=True, help="JSON string of source counts (e.g., '{\"ESPN\": 100, \"FanDuel\": 150}')")

    # Sub-parser for post_week_analysis
    analysis_parser = subparsers.add_parser("post-week-analysis", help="Send post-week analysis results")
    analysis_parser.add_argument("--week", type=int, required=True, help="NFL week number")
    analysis_parser.add_argument("--year", type=int, required=True, help="Season year")
    analysis_parser.add_argument("--analysis-summary", type=json.loads, required=True, help="JSON string of the analysis summary")
    analysis_parser.add_argument("--team-performance", type=json.loads, help="JSON string of the team performance data")

    # Sub-parser for weekly_status
    status_parser = subparsers.add_parser("weekly-status", help="Send weekly status update")
    status_parser.add_argument("--week", type=int, required=True, help="NFL week number")
    status_parser.add_argument("--team-name", required=True, help="Fantasy team name")
    status_parser.add_argument("--wins", type=int, required=True, help="Season wins")
    status_parser.add_argument("--losses", type=int, required=True, help="Season losses")
    status_parser.add_argument("--points-for", type=float, required=True, help="Total points scored")
    status_parser.add_argument("--rank", type=int, required=True, help="League ranking")
    status_parser.add_argument("--top-recommendations", nargs='+', required=True, help="List of top waiver recommendations")

    # Sub-parser for html-report
    report_parser = subparsers.add_parser("html-report", help="Send HTML report")
    report_parser.add_argument("--week", type=int, required=True, help="NFL week number")
    report_parser.add_argument("--year", type=int, required=True, help="Season year")
    report_parser.add_argument("--report-type", required=True, help="Type of report (e.g., 'start_sit')")
    report_parser.add_argument("--html-file", required=True, help="Path to the HTML report file")

    args = parser.parse_args()

    success = False
    if args.task == "projection-capture":
        success = send_capture_webhook(
            args.week,
            args.year,
            args.sources,
            args.total_projections,
            args.source_counts
        )
    elif args.task == "post-week-analysis":
        success = send_analysis_webhook(
            args.week,
            args.year,
            args.analysis_summary,
            args.team_performance
        )
    elif args.task == "weekly-status":
        success = send_status_webhook(
            args.week,
            args.team_name,
            args.wins,
            args.losses,
            args.points_for,
            args.rank,
            args.top_recommendations
        )
    elif args.task == "html-report":
        with open(args.html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        success = send_report_webhook(
            args.week,
            args.year,
            args.report_type,
            html_content
        )

    if success:
        logger.info("Webhook task completed successfully.")
        sys.exit(0)
    else:
        logger.error("Webhook task failed.")
        sys.exit(1)